<?xml version="1.0" encoding="ASCII"?>
<application:Application xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:advanced="http://www.eclipse.org/ui/2010/UIModel/application/ui/advanced" xmlns:application="http://www.eclipse.org/ui/2010/UIModel/application" xmlns:basic="http://www.eclipse.org/ui/2010/UIModel/application/ui/basic" xmlns:menu="http://www.eclipse.org/ui/2010/UIModel/application/ui/menu" xmi:id="_wtsmkc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.legacy.ide.application" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_wttNoM_kEe-m5_Ju82ZM_w" bindingContexts="_wttNps_kEe-m5_Ju82ZM_w">
  <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workbench>&#xD;&#xA;&lt;mruList>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;utg_windows.c&quot; tooltip=&quot;UTG/src/app/gui/utg_windows.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/app/gui/utg_windows.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;utg_data_analyse.c&quot; tooltip=&quot;UTG/src/app/utg_data_analyse.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/app/utg_data_analyse.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;misc_selftest.c&quot; tooltip=&quot;UTG_bsp/ps7_cortexa9_0/libsrc/misc_v1_0/src/misc_selftest.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG_bsp/ps7_cortexa9_0/libsrc/misc_v1_0/src/misc_selftest.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;drv_adc_captrue.c&quot; tooltip=&quot;UTG/src/drivers/drv_adc_captrue.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/drivers/drv_adc_captrue.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;sd_test.c&quot; tooltip=&quot;UTG/src/app/sd_test.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/app/sd_test.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;main.c&quot; tooltip=&quot;UTG/src/app/main.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/app/main.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.xilinx.sdk.sw.MSSEditor&quot; name=&quot;system.mss&quot; tooltip=&quot;UTG_bsp/system.mss&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG_bsp/system.mss&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;drv_misc.c&quot; tooltip=&quot;UTG/src/drivers/drv_misc.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/drivers/drv_misc.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;lv_port_disp.c&quot; tooltip=&quot;UTG/src/components/gui/lvgl_8.4.0/porting/lv_port_disp.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/components/gui/lvgl_8.4.0/porting/lv_port_disp.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.xilinx.sdk.lscript.editor&quot; name=&quot;lscript.ld&quot; tooltip=&quot;UTG/src/lscript.ld&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/lscript.ld&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;drv_gic_interrupt.h&quot; tooltip=&quot;UTG/src/drivers/drv_gic_interrupt.h&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/drivers/drv_gic_interrupt.h&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;com.xilinx.sdk.hw.ui.HwSpecXMLEditor&quot; name=&quot;system.hdf&quot; tooltip=&quot;UGT_hw_platform_0/system.hdf&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UGT_hw_platform_0/system.hdf&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.ui.DefaultTextEditor&quot; name=&quot;Xilinx.spec&quot; tooltip=&quot;UTG/src/Xilinx.spec&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UTG/src/Xilinx.spec&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;AXI_ADC_selftest.c&quot; tooltip=&quot;UGT_hw_platform_0/drivers/AXI_ADC_v1_0/src/AXI_ADC_selftest.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UGT_hw_platform_0/drivers/AXI_ADC_v1_0/src/AXI_ADC_selftest.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;file factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;AXI_ADC.c&quot; tooltip=&quot;UGT_hw_platform_0/drivers/AXI_ADC_v1_0/src/AXI_ADC.c&quot;>&#xD;&#xA;&lt;persistable path=&quot;/UGT_hw_platform_0/drivers/AXI_ADC_v1_0/src/AXI_ADC.c&quot;/>&#xD;&#xA;&lt;/file>&#xD;&#xA;&lt;/mruList>&#xD;&#xA;&lt;/workbench>"/>
  <tags>activeSchemeId:org.eclipse.ui.defaultAcceleratorConfiguration</tags>
  <tags>ModelMigrationProcessor.001</tags>
  <children xsi:type="basic:TrimmedWindow" xmi:id="_wttNoM_kEe-m5_Ju82ZM_w" elementId="IDEWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" selectedElement="_w3liY8_kEe-m5_Ju82ZM_w" x="0" y="0" width="1024" height="768">
    <persistedState key="coolBarVisible" value="true"/>
    <persistedState key="perspectiveBarVisible" value="true"/>
    <persistedState key="isRestored" value="true"/>
    <persistedState key="show_in_time" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;show_in_time/>"/>
    <persistedState key="workingSets" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;workingSets/>"/>
    <persistedState key="aggregateWorkingSetId" value="Aggregate for window 1736576692203"/>
    <tags>topLevel</tags>
    <tags>shellMaximized</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_w3liY8_kEe-m5_Ju82ZM_w" selectedElement="_w3liZM_kEe-m5_Ju82ZM_w" horizontal="true">
      <children xsi:type="advanced:PerspectiveStack" xmi:id="_w3liZM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.perspectivestack" containerData="7500" selectedElement="_w6dQMc_kEe-m5_Ju82ZM_w">
        <children xsi:type="advanced:Perspective" xmi:id="_w6dQMc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_w6d3QM_kEe-m5_Ju82ZM_w" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif" tooltip="C/C++">
          <persistedState key="persp.hiddenItems" value="persp.hideMenuSC:org.eclipse.jdt.ui.refactoring.menu,persp.hideMenuSC:org.eclipse.jdt.ui.source.menu,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView</tags>
          <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
          <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
          <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
          <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
          <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
          <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
          <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.appwiz.AppWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.profile.ui.wizards.ZpeProjectWizard</tags>
          <tags>persp.newWizSC:com.xilinx.sdk.sw.ui.NewBspWizard</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_w6d3QM_kEe-m5_Ju82ZM_w" selectedElement="_w6d3Qc_kEe-m5_Ju82ZM_w" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="_w6d3Qc_kEe-m5_Ju82ZM_w" containerData="1743" selectedElement="_w6d3Qs_kEe-m5_Ju82ZM_w">
              <children xsi:type="basic:PartStack" xmi:id="_w6d3Qs_kEe-m5_Ju82ZM_w" elementId="topLeft" containerData="7500" selectedElement="_w6d3Q8_kEe-m5_Ju82ZM_w">
                <tags>active</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Q8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_w6WigM_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_w6d3RM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false" ref="_w6XJkM_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Rc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false" ref="_w6XJkc_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Rs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_w6XJks_kEe-m5_Ju82ZM_w"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_w6d3R8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementViewMStack" containerData="2500" selectedElement="_w6d3SM_kEe-m5_Ju82ZM_w">
                <children xsi:type="advanced:Placeholder" xmi:id="_w6d3SM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" ref="_w6cCEM_kEe-m5_Ju82ZM_w"/>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_w6d3Sc_kEe-m5_Ju82ZM_w" containerData="8257" selectedElement="_w6d3UM_kEe-m5_Ju82ZM_w">
              <children xsi:type="basic:PartSashContainer" xmi:id="_w6d3Ss_kEe-m5_Ju82ZM_w" containerData="4708" selectedElement="_w6d3S8_kEe-m5_Ju82ZM_w" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_w6d3S8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_w6TfMM_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="basic:PartStack" xmi:id="_w6d3TM_kEe-m5_Ju82ZM_w" elementId="topRight" containerData="2500" selectedElement="_w6d3Tc_kEe-m5_Ju82ZM_w">
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Tc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ContentOutline" ref="_w6YXsc_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Ts_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" ref="_w6cpIM_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3T8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.views.MakeView" ref="_w6dQMM_kEe-m5_Ju82ZM_w"/>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_w6d3UM_kEe-m5_Ju82ZM_w" containerData="5292" selectedElement="_w6d3Uc_kEe-m5_Ju82ZM_w" horizontal="true">
                <children xsi:type="basic:PartStack" xmi:id="_w6d3Uc_kEe-m5_Ju82ZM_w" elementId="bottom" containerData="6406" selectedElement="_w6d3VM_kEe-m5_Ju82ZM_w">
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Us_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ProblemView" ref="_w6XJk8_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3U8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.TaskList" ref="_w6XwoM_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3VM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.console.ConsoleView" ref="_w6Xwoc_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Vc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.PropertySheet" ref="_w6YXsM_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3Vs_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.terminal.sdkterminal" ref="_w6cCEc_kEe-m5_Ju82ZM_w"/>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_w6d3V8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="3594" selectedElement="_JU_PANGeEe-HpuLBqvGhkQ">
                  <tags>General</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_w6d3WM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogView" ref="_w6bbAM_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_JU_PANGeEe-HpuLBqvGhkQ" elementId="org.eclipse.ui.views.ProgressView" ref="_JU-n8NGeEe-HpuLBqvGhkQ"/>
                </children>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="_NmvF8tPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.DebugPerspective" selectedElement="_NmvF89PsEe-B0Nqrxp_NhQ" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/$nl$/icons/full/eview16/debug_persp.png">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
          <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.SignalsView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.RegisterView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ModuleView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.MemoryView</tags>
          <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
          <tags>persp.viewSC:org.eclipse.cdt.debug.ui.executablesView</tags>
          <tags>persp.actionSet:org.eclipse.cdt.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view</tags>
          <tags>persp.viewSC:org.eclipse.cdt.dsf.debug.ui.disassembly.view</tags>
          <tags>persp.perspSC:org.eclipse.cdt.ui.CPerspective</tags>
          <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
          <tags>persp.viewSC:org.eclipse.pde.runtime.LogView</tags>
          <tags>persp.showIn:org.eclipse.egit.ui.RepositoriesView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="_NmvF89PsEe-B0Nqrxp_NhQ" selectedElement="_NmvF9NPsEe-B0Nqrxp_NhQ">
            <children xsi:type="basic:PartSashContainer" xmi:id="_NmvF9NPsEe-B0Nqrxp_NhQ" containerData="7500" selectedElement="_NmvtANPsEe-B0Nqrxp_NhQ">
              <children xsi:type="basic:PartSashContainer" xmi:id="_NmvF9dPsEe-B0Nqrxp_NhQ" toBeRendered="false" containerData="4500" horizontal="true">
                <children xsi:type="basic:PartSashContainer" xmi:id="_NmvF9tPsEe-B0Nqrxp_NhQ" toBeRendered="false" containerData="5000" horizontal="true">
                  <children xsi:type="basic:PartStack" xmi:id="_NmvF99PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView" toBeRendered="false" containerData="5000">
                    <tags>org.eclipse.e4.primaryNavigationStack</tags>
                    <children xsi:type="advanced:Placeholder" xmi:id="_NmvF-NPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.DebugView" toBeRendered="false" ref="_Nmq0gNPsEe-B0Nqrxp_NhQ"/>
                    <children xsi:type="advanced:Placeholder" xmi:id="_NmvF-dPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.navigator.ProjectExplorer" toBeRendered="false" ref="_w6WigM_kEe-m5_Ju82ZM_w"/>
                  </children>
                  <children xsi:type="basic:PartStack" xmi:id="_NmvF-tPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.viewMStack" toBeRendered="false" containerData="5000">
                    <children xsi:type="advanced:Placeholder" xmi:id="_NmvF-9PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" toBeRendered="false" ref="_Nmue4dPsEe-B0Nqrxp_NhQ"/>
                  </children>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="_NmvF_NPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.internal.ui.ToolsFolderView" toBeRendered="false" containerData="5000">
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvF_dPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.VariableView" toBeRendered="false" ref="_Nmq0gdPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvF_tPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.BreakpointView" toBeRendered="false" ref="_NmrbkNPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvF_9PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.ExpressionView" toBeRendered="false" ref="_NmrbkdPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvGANPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_NmrbktPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvGAdPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" toBeRendered="false" ref="_NmspsNPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvGAtPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" toBeRendered="false" ref="_NmtQwNPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvGA9PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.debug.ui.SignalsView" toBeRendered="false" ref="_Nmt30NPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvGBNPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.ModuleView" toBeRendered="false" ref="_Nmt30dPsEe-B0Nqrxp_NhQ"/>
                </children>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="_NmvtANPsEe-B0Nqrxp_NhQ" containerData="5500" selectedElement="_NmvtAdPsEe-B0Nqrxp_NhQ" horizontal="true">
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtAdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.editorss" containerData="7500" ref="_w6TfMM_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="basic:PartStack" xmi:id="_NmvtAtPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.internal.ui.OutlineFolderView" containerData="2500" selectedElement="_NmvtA9PsEe-B0Nqrxp_NhQ">
                  <tags>org.eclipse.e4.secondaryNavigationStack</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvtA9PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.views.ContentOutline" ref="_w6YXsc_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvtBNPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" toBeRendered="false" ref="_Nmue4tPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_NmvtBdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.tcf.ProfilerView" toBeRendered="false" ref="_NmvF8dPsEe-B0Nqrxp_NhQ"/>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="_NmvtBtPsEe-B0Nqrxp_NhQ" containerData="2500" selectedElement="_NmvtB9PsEe-B0Nqrxp_NhQ" horizontal="true">
              <children xsi:type="basic:PartStack" xmi:id="_NmvtB9PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.internal.ui.ConsoleFolderView" containerData="6000" selectedElement="_NmvtCNPsEe-B0Nqrxp_NhQ">
                <tags>org.eclipse.e4.secondaryDataStack</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtCNPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.console.ConsoleView" ref="_w6Xwoc_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtCdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.views.TaskList" ref="_w6XwoM_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtCtPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false" ref="_w6XJks_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtC9PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_w6YXsM_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtDNPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.terminal.sdkterminal" ref="_w6cCEc_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtDdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.ui.views.ProblemView" ref="_w6XJk8_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtDtPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.debug.ui.executablesView" ref="_Nmue4NPsEe-B0Nqrxp_NhQ"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtD9PsEe-B0Nqrxp_NhQ" elementId="org.eclipse.tcf.TraceView" toBeRendered="false" ref="_NmvF8NPsEe-B0Nqrxp_NhQ"/>
              </children>
              <children xsi:type="basic:PartStack" xmi:id="_NmvtENPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="4000" selectedElement="_lgkB0dPsEe-B0Nqrxp_NhQ">
                <tags>Debug</tags>
                <children xsi:type="advanced:Placeholder" xmi:id="_NmvtEdPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.logger.SdkLogView" ref="_w6bbAM_kEe-m5_Ju82ZM_w"/>
                <children xsi:type="advanced:Placeholder" xmi:id="_lgkB0dPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.MemoryView" ref="_lgkB0NPsEe-B0Nqrxp_NhQ"/>
              </children>
            </children>
          </children>
        </children>
        <children xsi:type="advanced:Perspective" xmi:id="__DxcgEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.PerformancePerspective" selectedElement="__DxcgUQdEfCzx70EH90i6w" label="Performance Analysis" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif">
          <persistedState key="persp.hiddenItems" value="persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
          <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
          <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
          <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
          <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
          <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
          <tags>persp.actionSet:org.eclipse.debug.ui.debugActionSet</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.DebugView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.VariableView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.BreakpointView</tags>
          <tags>persp.viewSC:org.eclipse.debug.ui.ExpressionView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.performance.ui.views.PsPerfGraphsView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.performance.ui.views.PsPerfTableView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.performance.ui.views.ApmPerfTableView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.performance.ui.views.MbPerfGraphsView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.performance.ui.views.MbPerfTableView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView</tags>
          <tags>persp.viewSC:com.xilinx.sdk.logger.SdkLogView</tags>
          <children xsi:type="basic:PartSashContainer" xmi:id="__DxcgUQdEfCzx70EH90i6w" selectedElement="__DxcgkQdEfCzx70EH90i6w" horizontal="true">
            <children xsi:type="basic:PartSashContainer" xmi:id="__DxcgkQdEfCzx70EH90i6w" containerData="5369" selectedElement="__Dxcg0QdEfCzx70EH90i6w">
              <children xsi:type="basic:PartStack" xmi:id="__Dxcg0QdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.ProjectFolderView" containerData="4000" selectedElement="__DxchEQdEfCzx70EH90i6w">
                <children xsi:type="advanced:Placeholder" xmi:id="__DxchEQdEfCzx70EH90i6w" elementId="org.eclipse.ui.navigator.ProjectExplorer" ref="_w6WigM_kEe-m5_Ju82ZM_w"/>
              </children>
              <children xsi:type="basic:PartSashContainer" xmi:id="__DxchUQdEfCzx70EH90i6w" containerData="6000" selectedElement="__DxcikQdEfCzx70EH90i6w">
                <children xsi:type="basic:PartStack" xmi:id="__DxchkQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.DebugFolderView" containerData="5854" selectedElement="__DxciEQdEfCzx70EH90i6w">
                  <children xsi:type="advanced:Placeholder" xmi:id="__Dxch0QdEfCzx70EH90i6w" elementId="org.eclipse.debug.ui.DebugView" ref="_Nmq0gNPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxciEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.logger.SdkLogView" ref="_w6bbAM_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxciUQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" ref="_NmspsNPsEe-B0Nqrxp_NhQ"/>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="__DxcikQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.PerfSessionManagerView" containerData="4146" selectedElement="__DxckkQdEfCzx70EH90i6w">
                  <children xsi:type="advanced:Placeholder" xmi:id="__Dxci0QdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView" toBeRendered="false" ref="__DuZMEQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcjEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView" toBeRendered="false" ref="__DvAQEQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcjUQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" ref="_w6cCEM_kEe-m5_Ju82ZM_w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcjkQdEfCzx70EH90i6w" elementId="org.eclipse.debug.ui.VariableView" toBeRendered="false" ref="_Nmq0gdPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__Dxcj0QdEfCzx70EH90i6w" elementId="org.eclipse.debug.ui.BreakpointView" toBeRendered="false" ref="_NmrbkNPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxckEQdEfCzx70EH90i6w" elementId="org.eclipse.debug.ui.ExpressionView" toBeRendered="false" ref="_NmrbkdPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxckUQdEfCzx70EH90i6w" elementId="org.eclipse.debug.ui.RegisterView" toBeRendered="false" ref="_NmrbktPsEe-B0Nqrxp_NhQ"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxckkQdEfCzx70EH90i6w" elementId="org.eclipse.ui.console.ConsoleView" ref="_w6Xwoc_kEe-m5_Ju82ZM_w"/>
                </children>
              </children>
            </children>
            <children xsi:type="basic:PartSashContainer" xmi:id="__Dxck0QdEfCzx70EH90i6w" containerData="4631" selectedElement="__DxcokQdEfCzx70EH90i6w">
              <children xsi:type="basic:PartSashContainer" xmi:id="__DxclEQdEfCzx70EH90i6w" toBeRendered="false" containerData="3973">
                <children xsi:type="basic:PartStack" xmi:id="__DxclUQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.PerformanceFolderView" toBeRendered="false" containerData="6000">
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxclkQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView" toBeRendered="false" ref="__DvAQUQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__Dxcl0QdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfTableView" toBeRendered="false" ref="__DvnUEQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcmEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView" toBeRendered="false" ref="__DvnUUQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcmUQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfTableView" toBeRendered="false" ref="__DvnUkQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcmkQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView" toBeRendered="false" ref="__DvnU0QdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__Dxcm0QdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.MbPerfTableView" toBeRendered="false" ref="__DvnVEQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcnEQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" toBeRendered="false" ref="__DwOYEQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcnUQdEfCzx70EH90i6w" elementId="org.eclipse.ui.views.PropertySheet" toBeRendered="false" ref="_w6YXsM_kEe-m5_Ju82ZM_w"/>
                </children>
                <children xsi:type="basic:PartStack" xmi:id="__DxcnkQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.HistogramFolderView" toBeRendered="false" containerData="4000">
                  <tags>General</tags>
                  <children xsi:type="advanced:Placeholder" xmi:id="__Dxcn0QdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" toBeRendered="false" ref="__DwOYUQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcoEQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" toBeRendered="false" ref="__DwOYkQdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="__DxcoUQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" toBeRendered="false" ref="__DwOY0QdEfCzx70EH90i6w"/>
                  <children xsi:type="advanced:Placeholder" xmi:id="_eDGwgEnGEfC8OcvF0-otwQ" elementId="org.eclipse.ui.views.ProgressView" toBeRendered="false" ref="_JU-n8NGeEe-HpuLBqvGhkQ"/>
                </children>
              </children>
              <children xsi:type="advanced:Placeholder" xmi:id="__DxcokQdEfCzx70EH90i6w" elementId="org.eclipse.ui.editorss" containerData="6027" ref="_w6TfMM_kEe-m5_Ju82ZM_w"/>
            </children>
          </children>
        </children>
      </children>
      <children xsi:type="basic:PartStack" xmi:id="_w3liZc_kEe-m5_Ju82ZM_w" elementId="stickyFolderRight" toBeRendered="false" containerData="2500">
        <children xsi:type="advanced:Placeholder" xmi:id="_w3liZs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.help.ui.HelpView" toBeRendered="false" ref="_w3liYM_kEe-m5_Ju82ZM_w"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_w3liZ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.internal.introview" toBeRendered="false" ref="_w3liYc_kEe-m5_Ju82ZM_w"/>
        <children xsi:type="advanced:Placeholder" xmi:id="_w3liaM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" toBeRendered="false" ref="_w3liYs_kEe-m5_Ju82ZM_w"/>
      </children>
    </children>
    <sharedElements xsi:type="basic:Part" xmi:id="_w3liYM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.help.ui.HelpView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w3liYc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.internal.introview" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_xDFnwM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.internal.introview">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_xDFnwc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.internal.introview" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w3liYs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
      <tags>View</tags>
      <tags>categoryTag:Help</tags>
    </sharedElements>
    <sharedElements xsi:type="advanced:Area" xmi:id="_w6TfMM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editorss" selectedElement="_w6TfMc_kEe-m5_Ju82ZM_w">
      <children xsi:type="basic:PartStack" xmi:id="_w6TfMc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.primaryDataStack" selectedElement="_zHK1UHv8EfCBB5MBvcTLbw">
        <tags>org.eclipse.e4.primaryDataStack</tags>
        <tags>EditorStack</tags>
        <children xsi:type="basic:Part" xmi:id="_wraS8HDXEfCuF8M9o8LdDA" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="drv_adc_captrue.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;drv_adc_captrue.c&quot; partName=&quot;drv_adc_captrue.c&quot; title=&quot;drv_adc_captrue.c&quot; tooltip=&quot;UTG/src/drivers/drv_adc_captrue.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UTG/src/drivers/drv_adc_captrue.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;21&quot; selectionOffset=&quot;631&quot; selectionTopPixel=&quot;188&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_oyjcIHgxEfCMcOYPgfensg" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="utg_data_analyse.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;utg_data_analyse.c&quot; partName=&quot;utg_data_analyse.c&quot; title=&quot;utg_data_analyse.c&quot; tooltip=&quot;UTG/src/app/utg_data_analyse.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UTG/src/app/utg_data_analyse.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;672&quot; selectionTopPixel=&quot;168&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
        <children xsi:type="basic:Part" xmi:id="_zHK1UHv8EfCBB5MBvcTLbw" elementId="org.eclipse.e4.ui.compatibility.editor" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor" label="utg_windows.c" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/obj16/c_file_obj.gif" closeable="true">
          <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;editor id=&quot;org.eclipse.cdt.ui.editor.CEditor&quot; name=&quot;utg_windows.c&quot; partName=&quot;utg_windows.c&quot; title=&quot;utg_windows.c&quot; tooltip=&quot;UTG/src/app/gui/utg_windows.c&quot;>&#xD;&#xA;&lt;input factoryID=&quot;org.eclipse.ui.part.FileEditorInputFactory&quot; path=&quot;/UTG/src/app/gui/utg_windows.c&quot;/>&#xD;&#xA;&lt;editorState selectionHorizontalPixel=&quot;0&quot; selectionLength=&quot;0&quot; selectionOffset=&quot;10132&quot; selectionTopPixel=&quot;2814&quot;/>&#xD;&#xA;&lt;/editor>"/>
          <tags>Editor</tags>
          <tags>org.eclipse.cdt.ui.editor.CEditor</tags>
          <tags>removeOnHide</tags>
        </children>
      </children>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6WigM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.ProjectExplorer" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view CommonNavigator.LINKING_ENABLED=&quot;0&quot; org.eclipse.cdt.ui.cview.groupincludes=&quot;false&quot; org.eclipse.cdt.ui.cview.groupmacros=&quot;false&quot; org.eclipse.cdt.ui.editor.CUChildren=&quot;true&quot; org.eclipse.ui.navigator.resources.workingSets.showTopLevelWorkingSets=&quot;1&quot;/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <tags>active</tags>
      <tags>activeOnClose</tags>
      <menus xmi:id="_w7-TIM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.ProjectExplorer">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_w7-TIc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6XJkM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6XJkc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ResourceNavigator" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6XJks_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.BookmarkView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6XJk8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ProblemView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot; categoryGroup=&quot;org.eclipse.ui.ide.severity&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.problemsGenerator&quot; partName=&quot;Problems&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.locationField=&quot;75&quot; org.eclipse.ui.ide.markerType=&quot;75&quot; org.eclipse.ui.ide.pathField=&quot;100&quot; org.eclipse.ui.ide.resourceField=&quot;75&quot; org.eclipse.ui.ide.severityAndDescriptionField=&quot;535&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.severityAndDescriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_xBRp4M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ProblemView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_xBRp4c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ProblemView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6XwoM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.TaskList" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view PRIMARY_SORT_FIELD=&quot;org.eclipse.ui.ide.completionField&quot; categoryGroup=&quot;none&quot; markerContentGenerator=&quot;org.eclipse.ui.ide.tasksGenerator&quot; partName=&quot;Tasks&quot;>&#xD;&#xA;&lt;columnWidths org.eclipse.ui.ide.completionField=&quot;40&quot; org.eclipse.ui.ide.descriptionField=&quot;250&quot; org.eclipse.ui.ide.locationField=&quot;75&quot; org.eclipse.ui.ide.markerType=&quot;75&quot; org.eclipse.ui.ide.pathField=&quot;100&quot; org.eclipse.ui.ide.priorityField=&quot;25&quot; org.eclipse.ui.ide.resourceField=&quot;75&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.completionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.priorityField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.descriptionField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.resourceField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.pathField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.locationField&quot;/>&#xD;&#xA;&lt;visible IMemento.internal.id=&quot;org.eclipse.ui.ide.markerType&quot;/>&#xD;&#xA;&lt;/view>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_88334NilEe-30qmAH786JA" elementId="org.eclipse.ui.views.TaskList">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_88334dilEe-30qmAH786JA" elementId="org.eclipse.ui.views.TaskList" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6Xwoc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.console.ConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_xR7VAM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.console.ConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_xR7VAc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.console.ConsoleView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6YXsM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.PropertySheet" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_sy4poEQeEfCN_JwOQiXrVQ" elementId="org.eclipse.ui.views.PropertySheet">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_sy4poUQeEfCN_JwOQiXrVQ" elementId="org.eclipse.ui.views.PropertySheet" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6YXsc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ContentOutline" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_xA4BQM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ContentOutline">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_xA4BQc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ContentOutline"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6bbAM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SDK Log" iconURI="platform:/plugin/com.xilinx.sdk.loggers/icons/icon.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.loggers.SDKLogView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.loggers"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_xBqrcM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_xBqrcc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6cCEM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Target Connections" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/target-mgmt-view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.targetmanager.ui.TargetManagement"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.targetmanager.ui"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_w-c_YM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_w-c_Yc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6cCEc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.terminal.sdkterminal" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="SDK Terminal" iconURI="platform:/plugin/com.xilinx.sdk.terminal/icons/console.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.terminal.SDKTerminalView"/>
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.terminal"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_N6LtkNimEe-30qmAH786JA" elementId="com.xilinx.sdk.terminal.sdkterminal">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_N6LtkdimEe-30qmAH786JA" elementId="com.xilinx.sdk.terminal.sdkterminal" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6cpIM_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Documents" iconURI="platform:/plugin/ilg.gnuarmeclipse.managedbuild.packs/icons/pdficon_small.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView"/>
      <persistedState key="originalCompatibilityViewBundle" value="ilg.gnuarmeclipse.managedbuild.packs"/>
      <tags>View</tags>
      <tags>categoryTag:C/C++ Packs</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_w6dQMM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.views.MakeView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
      <tags>View</tags>
      <tags>categoryTag:Make</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_JU-n8NGeEe-HpuLBqvGhkQ" elementId="org.eclipse.ui.views.ProgressView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:General</tags>
      <menus xmi:id="_JVGjwNGeEe-HpuLBqvGhkQ" elementId="org.eclipse.ui.views.ProgressView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_JVHK0NGeEe-HpuLBqvGhkQ" elementId="org.eclipse.ui.views.ProgressView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Nmq0gNPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.DebugView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_NnAywNPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.DebugView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_NnBZ0NPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.DebugView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Nmq0gdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.VariableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_NoR-ENPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.VariableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_NoR-EdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.VariableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_NmrbkNPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.BreakpointView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_lgS8ENPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.BreakpointView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_lgS8EdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.BreakpointView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_NmrbkdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.ExpressionView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_NmrbktPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.RegisterView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_lhM7ANPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.RegisterView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_lhM7AdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.RegisterView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_NmspsNPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="XSCT Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.targetmanager.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_NpdCwNPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_NpdCwdPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_NmtQwNPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Emulation Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.targetmanager.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_62R-0NilEe-30qmAH786JA" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_62R-0dilEe-30qmAH786JA" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Nmt30NPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.debug.ui.SignalsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Nmt30dPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.ModuleView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_lhX6INPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.ModuleView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_lhX6IdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.ModuleView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Nmue4NPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.debug.ui.executablesView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Nmue4dPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_Nmue4tPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_NmvF8NPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.tcf.TraceView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TCF Trace" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/tcf.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tcf.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tcf.internal.debug.ui.trace.TraceView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_NmvF8dPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.tcf.ProfilerView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="TCF Profiler" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/profiler.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tcf.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tcf.internal.debug.ui.profiler.ProfilerView"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="_lgkB0NPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.MemoryView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Debug</tags>
      <menus xmi:id="_lgnsMNPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.MemoryView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_lgnsMdPsEe-B0Nqrxp_NhQ" elementId="org.eclipse.debug.ui.MemoryView"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DuZMEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Performance Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="__FIHYEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__FIHYUQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DvAQEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Trace Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_AfDOEEQmEfCqV_0fhVy36w" elementId="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_AfDOEUQmEfCqV_0fhVy36w" elementId="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DvAQUQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="PS Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="__FbCUEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__FbpYEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DvnUEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfTableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="PS Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.PsPerfTableView"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_cvxLUEQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.PsPerfTableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_cvxLUUQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.PsPerfTableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DvnUUQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="APM Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_dEgFMEQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_dEgFMUQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DvnUkQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfTableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="APM Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.ApmPerfTableView"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_dNbXsEQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfTableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_dNbXsUQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfTableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DvnU0QdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="MicroBlaze Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_dcmcIEQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_dcmcIUQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DvnVEQdEfCzx70EH90i6w" elementId="com.xilinx.sdk.performance.ui.views.MbPerfTableView" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="MicroBlaze Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
      <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.MbPerfTableView"/>
      <tags>View</tags>
      <tags>categoryTag:Xilinx</tags>
      <menus xmi:id="_dnQM8EQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.MbPerfTableView">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_dnQM8UQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.performance.ui.views.MbPerfTableView" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DwOYEQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statistics.TmfStatisticsView"/>
      <tags>View</tags>
      <tags>categoryTag:Tracing</tags>
      <menus xmi:id="_d1So8EQeEfCN_JwOQiXrVQ" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_d1So8UQeEfCN_JwOQiXrVQ" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DwOYUQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
      <persistedState key="memento" value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?>&#xD;&#xA;&lt;view/>"/>
      <tags>View</tags>
      <tags>categoryTag:Tracing</tags>
      <menus xmi:id="__FvLYEQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="__FvLYUQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DwOYkQdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
      <tags>View</tags>
      <tags>categoryTag:Tracing</tags>
      <menus xmi:id="_u3-G0EQeEfCN_JwOQiXrVQ" elementId="org.eclipse.linuxtools.tmf.ui.views.filter">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_u3-G0UQeEfCN_JwOQiXrVQ" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" visible="false"/>
    </sharedElements>
    <sharedElements xsi:type="basic:Part" xmi:id="__DwOY0QdEfCzx70EH90i6w" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" closeable="true">
      <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
      <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
      <tags>View</tags>
      <tags>categoryTag:Tracing</tags>
      <menus xmi:id="_va0VMEQeEfCN_JwOQiXrVQ" elementId="org.eclipse.linuxtools.tmf.ui.views.colors">
        <tags>ViewMenu</tags>
        <tags>menuContribution:menu</tags>
      </menus>
      <toolbar xmi:id="_va0VMUQeEfCN_JwOQiXrVQ" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" visible="false"/>
    </sharedElements>
    <trimBars xmi:id="_wttNoc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.main.toolbar" contributorURI="platform:/plugin/org.eclipse.ui.workbench">
      <children xsi:type="menu:ToolBar" xmi:id="_w4L_UM_kEe-m5_Ju82ZM_w" elementId="group.file" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_w4L_Uc_kEe-m5_Ju82ZM_w" elementId="group.file" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w4MmYM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.workbench.file">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_uZovMHv8EfCBB5MBvcTLbw" elementId="print" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/print_edit.png" tooltip="Print" command="_wuClQc_kEe-m5_Ju82ZM_w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w4MmYc_kEe-m5_Ju82ZM_w" elementId="additions" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_w4MmYs_kEe-m5_Ju82ZM_w" elementId="additions" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w63f4M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w7JzwM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.launchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w6-0oM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.searchActionSet">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w6z1gM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.actionSet.presentation">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w4MmY8_kEe-m5_Ju82ZM_w" elementId="group.nav" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_w4MmZM_kEe-m5_Ju82ZM_w" elementId="group.nav" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w4MmZc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.workbench.navigate">
        <tags>Draggable</tags>
        <children xsi:type="menu:HandledToolItem" xmi:id="_uZp9VXv8EfCBB5MBvcTLbw" elementId="org.eclipse.ui.window.pinEditor" visible="false" iconURI="platform:/plugin/org.eclipse.ui/icons/full/etool16/pin_editor.png" tooltip="Pin Editor" type="Check" command="_wuB-Ls_kEe-m5_Ju82ZM_w"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w4MmZs_kEe-m5_Ju82ZM_w" elementId="group.editor" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_w4MmZ8_kEe-m5_Ju82ZM_w" elementId="group.editor" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w4MmaM_kEe-m5_Ju82ZM_w" elementId="group.help" toBeRendered="false">
        <tags>toolbarSeparator</tags>
        <children xsi:type="menu:ToolBarSeparator" xmi:id="_w4Mmac_kEe-m5_Ju82ZM_w" elementId="group.help" toBeRendered="false"/>
      </children>
      <children xsi:type="menu:ToolBar" xmi:id="_w4Mmas_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.workbench.help" visible="false">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_w49bYM_kEe-m5_Ju82ZM_w" elementId="PerspectiveSpacer" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
        <tags>stretch</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_w4-CcM_kEe-m5_Ju82ZM_w" elementId="PerspectiveSwitcher" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.e4.ui.workbench.addons.perspectiveswitcher.PerspectiveSwitcher">
        <tags>Draggable</tags>
        <tags>HIDEABLE</tags>
        <tags>SHOW_RESTORE_MENU</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_wttNos_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.trim.status" contributorURI="platform:/plugin/org.eclipse.ui.workbench" side="Bottom">
      <children xsi:type="menu:ToolControl" xmi:id="_w5FXMM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.StatusLine" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>stretch</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_w5KPsM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.HeapStatus" toBeRendered="false" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_w5VO0M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ProgressBar" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.StandardTrim">
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_wttNo8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.trim.vertical1" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Left">
      <children xsi:type="menu:ToolControl" xmi:id="_xEZPUM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.perspectivestack(minimized)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_5A_4MNilEe-30qmAH786JA" elementId="org.eclipse.debug.internal.ui.ToolsFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_PXSkwNimEe-30qmAH786JA" elementId="org.eclipse.ui.editorss(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
      <children xsi:type="menu:ToolControl" xmi:id="_2fPVkNkzEe-fyfOIoZh1Jw" elementId="org.eclipse.debug.internal.ui.NavigatorFolderView(IDEWindow).(org.eclipse.debug.ui.DebugPerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
    <trimBars xmi:id="_wttNpM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.trim.vertical2" contributorURI="platform:/plugin/org.eclipse.ui.workbench" toBeRendered="false" side="Right">
      <children xsi:type="menu:ToolControl" xmi:id="_PG_YgGIaEfChDPJHb5VdNg" elementId="org.eclipse.ui.editorss(IDEWindow).(com.xilinx.sdk.performance.ui.PerformancePerspective)" toBeRendered="false" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.TrimStack">
        <tags>TrimStack</tags>
        <tags>Draggable</tags>
      </children>
    </trimBars>
  </children>
  <bindingTables xmi:id="_wttNpc_kEe-m5_Ju82ZM_w" contributorURI="platform:/plugin/org.eclipse.ui.workbench" bindingContext="_wttNps_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4v8_kEe-m5_Ju82ZM_w" keySequence="CTRL+1" command="_wuB-Js_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4xc_kEe-m5_Ju82ZM_w" keySequence="ALT+/" command="_wuCk-8_kEe-m5_Ju82ZM_w">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_wuVfzc_kEe-m5_Ju82ZM_w" keySequence="CTRL+V" command="_wuBWwM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG4M_kEe-m5_Ju82ZM_w" keySequence="CTRL+F10" command="_wuBW68_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt5c_kEe-m5_Ju82ZM_w" keySequence="CTRL+INSERT" command="_wuAvs8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU9M_kEe-m5_Ju82ZM_w" keySequence="CTRL+X" command="_wuClK8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVDc_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+F3" command="_wuClSs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVFc_kEe-m5_Ju82ZM_w" keySequence="CTRL+C" command="_wuAvs8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVF8_kEe-m5_Ju82ZM_w" keySequence="ALT+PAGE_UP" command="_wuDMVc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVGs_kEe-m5_Ju82ZM_w" keySequence="ALT+PAGE_DOWN" command="_wuB93s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjEs_kEe-m5_Ju82ZM_w" keySequence="SHIFT+INSERT" command="_wuBWwM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjHs_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+L" command="_wuCljc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjIs_kEe-m5_Ju82ZM_w" keySequence="CTRL+Z" command="_wuClIM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjJs_kEe-m5_Ju82ZM_w" keySequence="CTRL+Y" command="_wuDMPc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKJc_kEe-m5_Ju82ZM_w" keySequence="SHIFT+DEL" command="_wuClK8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKLs_kEe-m5_Ju82ZM_w" keySequence="CTRL+A" command="_wuDMCM_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuR1YM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.debugging" bindingContext="_wuEaFc_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuURoM_kEe-m5_Ju82ZM_w" keySequence="SHIFT+F6" command="_wuBW0c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4ss_kEe-m5_Ju82ZM_w" keySequence="SHIFT+F7" command="_wuBXA8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfyM_kEe-m5_Ju82ZM_w" keySequence="SHIFT+F5" command="_wuDL98_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf28_kEe-m5_Ju82ZM_w" keySequence="CTRL+F5" command="_wuDL88_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU_8_kEe-m5_Ju82ZM_w" keySequence="SHIFT+F8" command="_wuCk5c_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4sM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.textEditorScope" bindingContext="_wuEaAs_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4sc_kEe-m5_Ju82ZM_w" keySequence="CTRL+NUMPAD_ADD" command="_wuDMEM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4wM_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+/" command="_wuDL8s_kEe-m5_Ju82ZM_w">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_wuVfxs_kEe-m5_Ju82ZM_w" keySequence="F2" command="_wuB-Mc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfz8_kEe-m5_Ju82ZM_w" keySequence="CTRL+NUMPAD_SUBTRACT" command="_wuClR8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf0s_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+Q" command="_wuB-D8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf18_kEe-m5_Ju82ZM_w" keySequence="CTRL+K" command="_wuB91c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf2M_kEe-m5_Ju82ZM_w" keySequence="INSERT" command="_wuBW5M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG1c_kEe-m5_Ju82ZM_w" keySequence="HOME" command="_wuDL7s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG2M_kEe-m5_Ju82ZM_w" keySequence="CTRL+NUMPAD_DIVIDE" command="_wuB9xM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG3M_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+Y" command="_wuCljs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG4c_kEe-m5_Ju82ZM_w" keySequence="CTRL+F10" command="_wuClRc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt4c_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+X" command="_wuAvwc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt58_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_wuB-G8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt6M_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+INSERT" command="_wuB94s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt68_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+J" command="_wuB9_M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt7c_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+K" command="_wuBXMc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt9s_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Y" command="_wuAIkc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU8M_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_wuBW5c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU8c_kEe-m5_Ju82ZM_w" keySequence="END" command="_wuClYs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU8s_kEe-m5_Ju82ZM_w" keySequence="SHIFT+CR" command="_wuDL6c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU88_kEe-m5_Ju82ZM_w" keySequence="CTRL+J" command="_wuBW9c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU-M_kEe-m5_Ju82ZM_w" keySequence="CTRL+L" command="_wuClGM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVAs_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+CR" command="_wuClSM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVCc_kEe-m5_Ju82ZM_w" keySequence="CTRL+ARROW_DOWN" command="_wuDMbs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVDs_kEe-m5_Ju82ZM_w" keySequence="CTRL+ARROW_LEFT" command="_wuAvrM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVFs_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+ARROW_LEFT" command="_wuDL8M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVG8_kEe-m5_Ju82ZM_w" keySequence="CTRL+D" command="_wuBXCM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVHM_kEe-m5_Ju82ZM_w" keySequence="CTRL+ARROW_RIGHT" command="_wuB-DM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVHc_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+ARROW_RIGHT" command="_wuB968_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8DM_kEe-m5_Ju82ZM_w" keySequence="CTRL+HOME" command="_wuBWv8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Dc_kEe-m5_Ju82ZM_w" keySequence="SHIFT+END" command="_wuDL6M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Ds_kEe-m5_Ju82ZM_w" keySequence="SHIFT+HOME" command="_wuCldM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Es_kEe-m5_Ju82ZM_w" keySequence="CTRL+BS" command="_wuAvq8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjEM_kEe-m5_Ju82ZM_w" keySequence="CTRL+END" command="_wuB97s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjEc_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+J" command="_wuB-Hc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjH8_kEe-m5_Ju82ZM_w" keySequence="CTRL+NUMPAD_MULTIPLY" command="_wuB-Ds_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjJc_kEe-m5_Ju82ZM_w" keySequence="CTRL+-" command="_wuClkM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjKM_kEe-m5_Ju82ZM_w" keySequence="CTRL++" command="_wuBXGc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKIM_kEe-m5_Ju82ZM_w" keySequence="ALT+ARROW_UP" command="_wuDMUs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKIc_kEe-m5_Ju82ZM_w" keySequence="CTRL+DEL" command="_wuClGs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKJM_kEe-m5_Ju82ZM_w" keySequence="CTRL+=" command="_wuBXGc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKMc_kEe-m5_Ju82ZM_w" keySequence="CTRL+ARROW_UP" command="_wuB9wc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKMs_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+ARROW_UP" command="_wuDMIs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKNM_kEe-m5_Ju82ZM_w" keySequence="ALT+ARROW_DOWN" command="_wuB96c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKNc_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+ARROW_DOWN" command="_wuDMFs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKNs_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+DEL" command="_wuClAM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKOc_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+A" command="_wuBWt8_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4s8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.debugging" bindingContext="_wuEaC8_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4tM_kEe-m5_Ju82ZM_w" keySequence="F7" command="_wuDMMM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4u8_kEe-m5_Ju82ZM_w" keySequence="F5" command="_wuBW4c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4xs_kEe-m5_Ju82ZM_w" keySequence="CTRL+R" command="_wuAvvM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfx8_kEe-m5_Ju82ZM_w" keySequence="F8" command="_wuBW4M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf3c_kEe-m5_Ju82ZM_w" keySequence="F6" command="_wuDL9s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjHc_kEe-m5_Ju82ZM_w" keySequence="CTRL+F2" command="_wuClBM_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4tc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.cEditorScope" bindingContext="_wuEaEs_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4ts_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+T" command="_wuCk4c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf0c_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Z" command="_wuClNM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf3M_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+S" command="_wuBW78_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG0M_kEe-m5_Ju82ZM_w" keySequence="CTRL+T" command="_wuB98s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG2c_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+P" command="_wuClZs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG2s_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+O" command="_wuClM8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG3c_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+O" command="_wuClRM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG3s_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+S" command="_wuDMQs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG38_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+T" command="_wuCk1s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt4M_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+L" command="_wuDMGM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt48_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+I" command="_wuBWys_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt6c_kEe-m5_Ju82ZM_w" keySequence="CTRL+I" command="_wuB9-c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt6s_kEe-m5_Ju82ZM_w" keySequence="CTRL+TAB" command="_wuDMac_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt8c_kEe-m5_Ju82ZM_w" keySequence="F3" command="_wuDMdc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt98_kEe-m5_Ju82ZM_w" keySequence="CTRL+O" command="_wuAvys_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU_c_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+N" command="_wuB-C8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVAM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+R" command="_wuClCs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVA8_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+/" command="_wuDMDc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVBM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+M" command="_wuBWuM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVEM_kEe-m5_Ju82ZM_w" keySequence="CTRL+#" command="_wuB-QM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8A8_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_wuClhc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Bc_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+F" command="_wuDMas_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8CM_kEe-m5_Ju82ZM_w" keySequence="CTRL+G" command="_wuDMNc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8EM_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+G" command="_wuAvpc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8FM_kEe-m5_Ju82ZM_w" keySequence="SHIFT+TAB" command="_wuBW_8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Fc_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+H" command="_wuB-KM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8F8_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+H" command="_wuBW_c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjFM_kEe-m5_Ju82ZM_w" keySequence="F4" command="_wuDMCc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjHM_kEe-m5_Ju82ZM_w" keySequence="CTRL+/" command="_wuBWw8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjKs_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+\" command="_wuBWxM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKI8_kEe-m5_Ju82ZM_w" keySequence="CTRL+=" command="_wuB-QM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKJs_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+ARROW_UP" command="_wuClEM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKJ8_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_wuDMa8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKKs_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_wuCk_M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKLM_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+ARROW_DOWN" command="_wuB938_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKLc_kEe-m5_Ju82ZM_w" keySequence="ALT+C" command="_wuBWxs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKN8_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_UP" command="_wuCle8_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4t8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.cViewScope" bindingContext="_wuEaE8_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4uM_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+T" command="_wuCk4c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt5M_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+I" command="_wuBWys_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt8s_kEe-m5_Ju82ZM_w" keySequence="F3" command="_wuDMdc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVAc_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+R" command="_wuClCs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Cc_kEe-m5_Ju82ZM_w" keySequence="CTRL+G" command="_wuDMNc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Ec_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+G" command="_wuAvpc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Fs_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+H" command="_wuB-KM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8GM_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+H" command="_wuBW_c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjFc_kEe-m5_Ju82ZM_w" keySequence="F4" command="_wuDMCc_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4uc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.contexts.window" bindingContext="_wttNp8_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4us_kEe-m5_Ju82ZM_w" keySequence="DEL" command="_wuBXKs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4vM_kEe-m5_Ju82ZM_w" keySequence="F5" command="_wuClY8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4wc_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+R" command="_wuDMcs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuU4ws_kEe-m5_Ju82ZM_w" keySequence="CTRL+F7" command="_wuAvtM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfwM_kEe-m5_Ju82ZM_w" keySequence="CTRL+{" command="_wuCk58_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuVfwc_kEe-m5_Ju82ZM_w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="false"/>
    </bindings>
    <bindings xmi:id="_wuVfws_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+N" command="_wuClJs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfxM_kEe-m5_Ju82ZM_w" keySequence="CTRL+3" command="_wuB-MM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfxc_kEe-m5_Ju82ZM_w" keySequence="F2" command="_wuBWy8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfyc_kEe-m5_Ju82ZM_w" keySequence="SHIFT+F5" command="_wuDMG8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfzs_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+F7" command="_wuDMBM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf0M_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+W" command="_wuClKs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf1M_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+F4" command="_wuClKs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf1c_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+W" command="_wuDMIc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf1s_kEe-m5_Ju82ZM_w" keySequence="F12" command="_wuCk_s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf2c_kEe-m5_Ju82ZM_w" keySequence="SHIFT+F9" command="_wuClQs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf2s_kEe-m5_Ju82ZM_w" keySequence="CTRL+F4" command="_wuDMSc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG18_kEe-m5_Ju82ZM_w" keySequence="CTRL+N" command="_wuDMRM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG28_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+F8" command="_wuCk5s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt5s_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+NUMPAD_MULTIPLY" command="_wuBXDc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt7M_kEe-m5_Ju82ZM_w" keySequence="ALT+F11" command="_wuBXIM_kEe-m5_Ju82ZM_w">
      <tags>platform:win32</tags>
    </bindings>
    <bindings xmi:id="_wuWt7s_kEe-m5_Ju82ZM_w" keySequence="F11" command="_wuDMAc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt9c_kEe-m5_Ju82ZM_w" keySequence="F9" command="_wuClas_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt-M_kEe-m5_Ju82ZM_w" keySequence="ALT+-" command="_wuAvz8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt-s_kEe-m5_Ju82ZM_w" keySequence="CTRL+." command="_wuDMJc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt-8_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+NUMPAD_DIVIDE" command="_wuCk6s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU9c_kEe-m5_Ju82ZM_w" keySequence="CTRL+F8" command="_wuB-J8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU9s_kEe-m5_Ju82ZM_w" keySequence="CTRL+Q" command="_wuClUc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU98_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+S" command="_wuBXD8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU-c_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+F7" command="_wuB98M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU-s_kEe-m5_Ju82ZM_w" keySequence="CTRL+M" command="_wuCk8M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXU_s_kEe-m5_Ju82ZM_w" keySequence="ALT+F7" command="_wuBWwc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVBc_kEe-m5_Ju82ZM_w" keySequence="CTRL+F11" command="_wuClZ8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVC8_kEe-m5_Ju82ZM_w" keySequence="ALT+ARROW_RIGHT" command="_wuClSc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVDM_kEe-m5_Ju82ZM_w" keySequence="CTRL+B" command="_wuBWzc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVEc_kEe-m5_Ju82ZM_w" keySequence="CTRL+#" command="_wuBW7M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8AM_kEe-m5_Ju82ZM_w" keySequence="CTRL+F" command="_wuBXF8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Ac_kEe-m5_Ju82ZM_w" keySequence="CTRL+E" command="_wuClFs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8As_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+E" command="_wuB93M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8C8_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+G" command="_wuBXLM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8E8_kEe-m5_Ju82ZM_w" keySequence="CTRL+H" command="_wuCk-s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjE8_kEe-m5_Ju82ZM_w" keySequence="CTRL+S" command="_wuClkc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjFs_kEe-m5_Ju82ZM_w" keySequence="CTRL+P" command="_wuClQc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjF8_kEe-m5_Ju82ZM_w" keySequence="ALT+CR" command="_wuCk0s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjGM_kEe-m5_Ju82ZM_w" keySequence="CTRL+," command="_wuBWx8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjGs_kEe-m5_Ju82ZM_w" keySequence="CTRL+W" command="_wuDMSc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjJM_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+F6" command="_wuBXFs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjJ8_kEe-m5_Ju82ZM_w" keySequence="CTRL+F6" command="_wuBXJM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjK8_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+?" command="_wuCk4s_kEe-m5_Ju82ZM_w">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_wuYjLc_kEe-m5_Ju82ZM_w" keySequence="CTRL+_" command="_wuCk58_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuYjLs_kEe-m5_Ju82ZM_w" elementId="Splitter.isHorizontal" name="Splitter.isHorizontal" value="true"/>
    </bindings>
    <bindings xmi:id="_wuZKIs_kEe-m5_Ju82ZM_w" keySequence="ALT+?" command="_wuCk4s_kEe-m5_Ju82ZM_w">
      <tags>locale:zh</tags>
    </bindings>
    <bindings xmi:id="_wuZKKc_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+B" command="_wuBXMs_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKMM_kEe-m5_Ju82ZM_w" keySequence="ALT+ARROW_LEFT" command="_wuBW8s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKM8_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+B" command="_wuB90s_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKOs_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q S" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZKO8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.search.ui.views.SearchView"/>
    </bindings>
    <bindings xmi:id="_wuZKPM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q B" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZKPc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.BreakpointView"/>
    </bindings>
    <bindings xmi:id="_wuZKPs_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q V" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZKP8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.debug.ui.VariableView"/>
    </bindings>
    <bindings xmi:id="_wuZKQM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q Y" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZKQc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.sync.views.SynchronizeView"/>
    </bindings>
    <bindings xmi:id="_wuZKQs_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q X" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZKQ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ProblemView"/>
    </bindings>
    <bindings xmi:id="_wuZKRM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q Z" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZKRc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.team.ui.GenericHistoryView"/>
    </bindings>
    <bindings xmi:id="_wuZxMM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q H" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZxMc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.cheatsheets.views.CheatSheetView"/>
    </bindings>
    <bindings xmi:id="_wuZxMs_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q O" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZxM8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.views.ContentOutline"/>
    </bindings>
    <bindings xmi:id="_wuZxNM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q C" command="_wuB9yc_kEe-m5_Ju82ZM_w">
      <parameters xmi:id="_wuZxNc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="org.eclipse.ui.views.showView.viewId" value="org.eclipse.ui.console.ConsoleView"/>
    </bindings>
    <bindings xmi:id="_wuZxNs_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+Q Q" command="_wuB9yc_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4vc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.memoryview" bindingContext="_wuEaCs_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4vs_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+M" command="_wuDMBc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG0c_kEe-m5_Ju82ZM_w" keySequence="CTRL+T" command="_wuB-Oc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG1s_kEe-m5_Ju82ZM_w" keySequence="CTRL+N" command="_wuBXIc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt4s_kEe-m5_Ju82ZM_w" keySequence="ALT+CTRL+N" command="_wuDME8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjGc_kEe-m5_Ju82ZM_w" keySequence="CTRL+W" command="_wuBW6M_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4w8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.TerminalContext" bindingContext="_wuEaEM_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4xM_kEe-m5_Ju82ZM_w" keySequence="ALT+T" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfw8_kEe-m5_Ju82ZM_w" keySequence="ALT+P" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVfys_kEe-m5_Ju82ZM_w" keySequence="ALT+N" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuVf08_kEe-m5_Ju82ZM_w" keySequence="ALT+S" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWt-c_kEe-m5_Ju82ZM_w" keySequence="ALT+V" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVBs_kEe-m5_Ju82ZM_w" keySequence="ALT+R" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVD8_kEe-m5_Ju82ZM_w" keySequence="ALT+E" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVGc_kEe-m5_Ju82ZM_w" keySequence="ALT+F" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVHs_kEe-m5_Ju82ZM_w" keySequence="ALT+G" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVH8_kEe-m5_Ju82ZM_w" keySequence="ALT+H" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjI8_kEe-m5_Ju82ZM_w" keySequence="ALT+W" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjLM_kEe-m5_Ju82ZM_w" keySequence="ALT+A" command="_wuAvyc_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuU4x8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" bindingContext="_wuEaDM_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuU4yM_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+," command="_wuClU8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuWG0s_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+." command="_wuCk7M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Cs_kEe-m5_Ju82ZM_w" keySequence="CTRL+G" command="_wuCk7s_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuVfy8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesView" bindingContext="_wuEaFM_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuVfzM_kEe-m5_Ju82ZM_w" keySequence="CTRL+V" command="_wuAvv8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVEs_kEe-m5_Ju82ZM_w" keySequence="CTRL+C" command="_wuB-Ps_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuWG08_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" bindingContext="_wuEaDs_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuWG1M_kEe-m5_Ju82ZM_w" keySequence="HOME" command="_wuB-Ms_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8B8_kEe-m5_Ju82ZM_w" keySequence="CTRL+G" command="_wuDMP8_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuWt78_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" bindingContext="_wuEaEc_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuWt8M_kEe-m5_Ju82ZM_w" keySequence="F3" command="_wuBXBc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjG8_kEe-m5_Ju82ZM_w" keySequence="CTRL+/" command="_wuB92M_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuYjKc_kEe-m5_Ju82ZM_w" keySequence="CTRL+\" command="_wuBXKM_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuWt88_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" bindingContext="_wuEaCM_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuWt9M_kEe-m5_Ju82ZM_w" keySequence="F3" command="_wuDMdc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVCs_kEe-m5_Ju82ZM_w" keySequence="ALT+ARROW_RIGHT" command="_wuB-Fc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKL8_kEe-m5_Ju82ZM_w" keySequence="ALT+ARROW_LEFT" command="_wuB99M_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuXU-8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.EditContext" bindingContext="_wuEaAM_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuXU_M_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+V" command="_wuClgM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuXVGM_kEe-m5_Ju82ZM_w" keySequence="CTRL+SHIFT+C" command="_wuB-Ns_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuXVB8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" bindingContext="_wuEaBc_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuXVCM_kEe-m5_Ju82ZM_w" keySequence="ALT+R" command="_wuCk-c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8BM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_RIGHT" command="_wuBW7c_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8Bs_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+HOME" command="_wuDMIM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuX8D8_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+END" command="_wuClFc_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKKM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_DOWN" command="_wuBWtM_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKK8_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_LEFT" command="_wuDMb8_kEe-m5_Ju82ZM_w"/>
    <bindings xmi:id="_wuZKOM_kEe-m5_Ju82ZM_w" keySequence="ALT+SHIFT+ARROW_UP" command="_wuClTM_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuXVE8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.ReflogView" bindingContext="_wuEaD8_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuXVFM_kEe-m5_Ju82ZM_w" keySequence="CTRL+C" command="_wuBXJs_kEe-m5_Ju82ZM_w"/>
  </bindingTables>
  <bindingTables xmi:id="_wuYjIM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.console" bindingContext="_wuEaBM_kEe-m5_Ju82ZM_w">
    <bindings xmi:id="_wuYjIc_kEe-m5_Ju82ZM_w" keySequence="CTRL+Z" command="_wuDMHs_kEe-m5_Ju82ZM_w">
      <tags>platform:win32</tags>
    </bindings>
  </bindingTables>
  <bindingTables xmi:id="_w6UGQc_kEe-m5_Ju82ZM_w" bindingContext="_w6UGQM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UGQ8_kEe-m5_Ju82ZM_w" bindingContext="_w6UGQs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UGRc_kEe-m5_Ju82ZM_w" bindingContext="_w6UGRM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UGR8_kEe-m5_Ju82ZM_w" bindingContext="_w6UGRs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UGSc_kEe-m5_Ju82ZM_w" bindingContext="_w6UGSM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UGS8_kEe-m5_Ju82ZM_w" bindingContext="_w6UGSs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UGTc_kEe-m5_Ju82ZM_w" bindingContext="_w6UGTM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UGT8_kEe-m5_Ju82ZM_w" bindingContext="_w6UGTs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtUc_kEe-m5_Ju82ZM_w" bindingContext="_w6UtUM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtU8_kEe-m5_Ju82ZM_w" bindingContext="_w6UtUs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtVc_kEe-m5_Ju82ZM_w" bindingContext="_w6UtVM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtV8_kEe-m5_Ju82ZM_w" bindingContext="_w6UtVs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtWc_kEe-m5_Ju82ZM_w" bindingContext="_w6UtWM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtW8_kEe-m5_Ju82ZM_w" bindingContext="_w6UtWs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtXc_kEe-m5_Ju82ZM_w" bindingContext="_w6UtXM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6UtX8_kEe-m5_Ju82ZM_w" bindingContext="_w6UtXs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUYc_kEe-m5_Ju82ZM_w" bindingContext="_w6VUYM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUY8_kEe-m5_Ju82ZM_w" bindingContext="_w6VUYs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUZc_kEe-m5_Ju82ZM_w" bindingContext="_w6VUZM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUZ8_kEe-m5_Ju82ZM_w" bindingContext="_w6VUZs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUac_kEe-m5_Ju82ZM_w" bindingContext="_w6VUaM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUa8_kEe-m5_Ju82ZM_w" bindingContext="_w6VUas_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUbc_kEe-m5_Ju82ZM_w" bindingContext="_w6VUbM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUb8_kEe-m5_Ju82ZM_w" bindingContext="_w6VUbs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUcc_kEe-m5_Ju82ZM_w" bindingContext="_w6VUcM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUc8_kEe-m5_Ju82ZM_w" bindingContext="_w6VUcs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUdc_kEe-m5_Ju82ZM_w" bindingContext="_w6VUdM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUd8_kEe-m5_Ju82ZM_w" bindingContext="_w6VUds_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6VUec_kEe-m5_Ju82ZM_w" bindingContext="_w6VUeM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6V7cc_kEe-m5_Ju82ZM_w" bindingContext="_w6V7cM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6V7c8_kEe-m5_Ju82ZM_w" bindingContext="_w6V7cs_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6V7dc_kEe-m5_Ju82ZM_w" bindingContext="_w6V7dM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6V7d8_kEe-m5_Ju82ZM_w" bindingContext="_w6V7ds_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_w6V7ec_kEe-m5_Ju82ZM_w" bindingContext="_w6V7eM_kEe-m5_Ju82ZM_w"/>
  <bindingTables xmi:id="_lgFgsdPsEe-B0Nqrxp_NhQ" bindingContext="_lgFgsNPsEe-B0Nqrxp_NhQ"/>
  <rootContext xmi:id="_wttNps_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.contexts.dialogAndWindow" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs and Windows" description="Either a dialog or a window is open">
    <children xmi:id="_wttNp8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.contexts.window" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Windows" description="A window is open">
      <children xmi:id="_wttNqM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.contexts.views" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="%bindingcontext.name.bindingView"/>
      <children xmi:id="_wuEaAM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.EditContext" name="Terminal widget context" description="Override ALT+x menu access keys"/>
      <children xmi:id="_wuEaAc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.BreakpointView" name="In Breakpoints View" description="The breakpoints view context"/>
      <children xmi:id="_wuEaAs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.textEditorScope" name="Editing Text" description="Editing Text Context">
        <children xmi:id="_wuEaEc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.makefileEditorScope" name="Makefile Editor" description="Editor for makefiles"/>
        <children xmi:id="_wuEaEs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.cEditorScope" name="C/C++ Editor" description="Editor for C/C++ Source Files"/>
      </children>
      <children xmi:id="_wuEaBM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.console" name="In I/O Console" description="In I/O console"/>
      <children xmi:id="_wuEaBs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.compareEditorScope" name="Comparing in an Editor" description="Comparing in an Editor"/>
      <children xmi:id="_wuEaCc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.console.ConsoleView" name="In Console View" description="In Console View"/>
      <children xmi:id="_wuEaCs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.memoryview" name="In Memory View" description="In memory view"/>
      <children xmi:id="_wuEaC8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.debugging" name="Debugging" description="Debugging programs">
        <children xmi:id="_wuEaDM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.memory.abstractasynctablerendering" name="In Table Memory Rendering" description="In Table Memory Rendering"/>
        <children xmi:id="_wuEaDc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.debug.ui.debugging" name="Debugging using Target Communication Framework" description="Debugging using Target Communication Framework"/>
        <children xmi:id="_wuEaDs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.context" name="In Disassembly" description="When debugging in assembly mode"/>
        <children xmi:id="_wuEaFc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.debugging" name="Debugging C/C++" description="Debugging C/C++ Programs"/>
      </children>
      <children xmi:id="_wuEaD8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.ReflogView" name="In Git Reflog View"/>
      <children xmi:id="_wuEaE8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.cViewScope" name="In C/C++ Views" description="In C/C++ Views"/>
      <children xmi:id="_wuEaFM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesView" name="In Git Repositories View"/>
    </children>
    <children xmi:id="_wttNqc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.contexts.dialog" contributorURI="platform:/plugin/org.eclipse.ui.workbench" name="In Dialogs" description="A dialog is open"/>
    <children xmi:id="_wuEaCM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.macroExpansionHoverScope" name="In Macro Expansion Hover" description="In Macro Expansion Hover"/>
  </rootContext>
  <rootContext xmi:id="_wuEaA8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.contexts.actionSet" name="Action Set" description="Parent context for action sets"/>
  <rootContext xmi:id="_wuEaBc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.view.uml2sd.context" name="UML2 Sequence Diagram Viewer"/>
  <rootContext xmi:id="_wuEaB8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.contexts.workbenchMenu" name="Workbench Menu" description="When no Workbench windows are active"/>
  <rootContext xmi:id="_wuEaEM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.TerminalContext" name="Terminal widget context" description="Override ALT+x menu access keys"/>
  <rootContext xmi:id="_w6UGQM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.debugActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_w6UGQs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.reverseDebuggingActionSet" name="Auto::org.eclipse.cdt.debug.ui.reverseDebuggingActionSet"/>
  <rootContext xmi:id="_w6UGRM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.tracepointActionSet" name="Auto::org.eclipse.cdt.debug.ui.tracepointActionSet"/>
  <rootContext xmi:id="_w6UGRs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.debugViewLayoutActionSet" name="Auto::org.eclipse.cdt.debug.ui.debugViewLayoutActionSet"/>
  <rootContext xmi:id="_w6UGSM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.updateModes" name="Auto::org.eclipse.cdt.dsf.debug.ui.updateModes"/>
  <rootContext xmi:id="_w6UGSs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.updateActionSet" name="Auto::org.eclipse.cdt.make.ui.updateActionSet"/>
  <rootContext xmi:id="_w6UGTM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.makeTargetActionSet" name="Auto::org.eclipse.cdt.make.ui.makeTargetActionSet"/>
  <rootContext xmi:id="_w6UGTs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CodingActionSet" name="Auto::org.eclipse.cdt.ui.CodingActionSet"/>
  <rootContext xmi:id="_w6UtUM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.SearchActionSet" name="Auto::org.eclipse.cdt.ui.SearchActionSet"/>
  <rootContext xmi:id="_w6UtUs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.NavigationActionSet" name="Auto::org.eclipse.cdt.ui.NavigationActionSet"/>
  <rootContext xmi:id="_w6UtVM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.OpenActionSet" name="Auto::org.eclipse.cdt.ui.OpenActionSet"/>
  <rootContext xmi:id="_w6UtVs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.buildConfigActionSet" name="Auto::org.eclipse.cdt.ui.buildConfigActionSet"/>
  <rootContext xmi:id="_w6UtWM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CElementCreationActionSet" name="Auto::org.eclipse.cdt.ui.CElementCreationActionSet"/>
  <rootContext xmi:id="_w6UtWs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.text.c.actionSet.presentation" name="Auto::org.eclipse.cdt.ui.text.c.actionSet.presentation"/>
  <rootContext xmi:id="_w6UtXM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.breakpointActionSet" name="Auto::org.eclipse.debug.ui.breakpointActionSet"/>
  <rootContext xmi:id="_w6UtXs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.debugActionSet" name="Auto::org.eclipse.debug.ui.debugActionSet"/>
  <rootContext xmi:id="_w6VUYM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.launchActionSet" name="Auto::org.eclipse.debug.ui.launchActionSet"/>
  <rootContext xmi:id="_w6VUYs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.profileActionSet" name="Auto::org.eclipse.debug.ui.profileActionSet"/>
  <rootContext xmi:id="_w6VUZM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.gitaction" name="Auto::org.eclipse.egit.ui.gitaction"/>
  <rootContext xmi:id="_w6VUZs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.navigation" name="Auto::org.eclipse.egit.ui.navigation"/>
  <rootContext xmi:id="_w6VUaM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.cheatsheets.actionSet" name="Auto::org.eclipse.ui.cheatsheets.actionSet"/>
  <rootContext xmi:id="_w6VUas_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.core.search.searchActionSet" name="Auto::org.eclipse.rse.core.search.searchActionSet"/>
  <rootContext xmi:id="_w6VUbM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.searchActionSet" name="Auto::org.eclipse.search.searchActionSet"/>
  <rootContext xmi:id="_w6VUbs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.ui.actionSet" name="Auto::org.eclipse.team.ui.actionSet"/>
  <rootContext xmi:id="_w6VUcM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.actionSet.annotationNavigation" name="Auto::org.eclipse.ui.edit.text.actionSet.annotationNavigation"/>
  <rootContext xmi:id="_w6VUcs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.actionSet.navigation" name="Auto::org.eclipse.ui.edit.text.actionSet.navigation"/>
  <rootContext xmi:id="_w6VUdM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo" name="Auto::org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo"/>
  <rootContext xmi:id="_w6VUds_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.externaltools.ExternalToolsSet" name="Auto::org.eclipse.ui.externaltools.ExternalToolsSet"/>
  <rootContext xmi:id="_w6VUeM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.NavigateActionSet" name="Auto::org.eclipse.ui.NavigateActionSet"/>
  <rootContext xmi:id="_w6V7cM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.actionSet.keyBindings" name="Auto::org.eclipse.ui.actionSet.keyBindings"/>
  <rootContext xmi:id="_w6V7cs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.WorkingSetModificationActionSet" name="Auto::org.eclipse.ui.WorkingSetModificationActionSet"/>
  <rootContext xmi:id="_w6V7dM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.WorkingSetActionSet" name="Auto::org.eclipse.ui.WorkingSetActionSet"/>
  <rootContext xmi:id="_w6V7ds_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.actionSet.openFiles" name="Auto::org.eclipse.ui.actionSet.openFiles"/>
  <rootContext xmi:id="_w6V7eM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.actionSet.presentation" name="Auto::org.eclipse.ui.edit.text.actionSet.presentation"/>
  <rootContext xmi:id="_lgFgsNPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.tcf.debug.LaunchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective" name="Auto::com.xilinx.sdk.tcf.debug.LaunchConfigurationType.internal.org.eclipse.debug.ui.DebugPerspective"/>
  <descriptors xmi:id="_wxp5gM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.compatibility.editor" allowMultiple="true" category="org.eclipse.e4.primaryDataStack" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityEditor">
    <tags>Editor</tags>
  </descriptors>
  <descriptors xmi:id="_wxrusM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogView" label="SDK Log" iconURI="platform:/plugin/com.xilinx.sdk.loggers/icons/icon.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.loggers.SDKLogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.loggers"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxuyAM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.oprofile.view" label="Oprofile View" iconURI="platform:/plugin/com.xilinx.sdk.oprofile/icons/gprof.gif" tooltip="" category="Oprofile" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.oprofile.internal.ui.OprofileView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.oprofile"/>
    <tags>View</tags>
    <tags>categoryTag:Oprofile</tags>
  </descriptors>
  <descriptors xmi:id="_wxvZEM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView" label="PS Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.PsPerfGraphsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxvZEc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.PsPerfTableView" label="PS Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.PsPerfTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxwAIM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfTableView" label="APM Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.ApmPerfTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxwAIc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView" label="APM Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.ApmPerfGraphsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxwAIs_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView" label="Performance Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.PerfSessionManagerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxwAI8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView" label="MicroBlaze Performance Graphs" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.MbPerfGraphsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxwnMM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.MbPerfTableView" label="MicroBlaze Performance Counters" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.MbPerfTableView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxxOQM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView" label="Trace Session Manager" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.ui.views.TraceSessionManagerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxxOQc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.performance.stm.freertos.trace.FreeRtosAnalysisView" label="FreeRTOS Analysis" iconURI="platform:/plugin/com.xilinx.sdk.profile/icons/performance.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.performance.stm.freertos.trace.FreeRtosAnalysisView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.profile"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxxOQs_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView" label="XSCT Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.targetmanager.ui.xsdb.XSDBConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.targetmanager.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxxOQ8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView" label="Target Connections" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/target-mgmt-view.gif" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.targetmanager.ui.TargetManagement"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.targetmanager.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxx1UM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView" label="Emulation Console" iconURI="platform:/plugin/com.xilinx.sdk.targetmanager.ui/icons/xsdbconsole_icon.png" tooltip="" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.targetmanager.ui.qemu.QEMUConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.targetmanager.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxx1Uc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.terminal.sdkterminal" label="SDK Terminal" iconURI="platform:/plugin/com.xilinx.sdk.terminal/icons/console.gif" tooltip="" allowMultiple="true" category="Xilinx" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="com.xilinx.sdk.terminal.SDKTerminalView"/>
    <persistedState key="originalCompatibilityViewBundle" value="com.xilinx.sdk.terminal"/>
    <tags>View</tags>
    <tags>categoryTag:Xilinx</tags>
  </descriptors>
  <descriptors xmi:id="_wxx1Us_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView" label="Documents" iconURI="platform:/plugin/ilg.gnuarmeclipse.managedbuild.packs/icons/pdficon_small.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="ilg.gnuarmeclipse.managedbuild.packs"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wxycYM_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.ui.views.DevicesView" label="Devices" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/hardware_chip.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="ilg.gnuarmeclipse.packs.ui.views.DevicesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="ilg.gnuarmeclipse.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wxycYc_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.ui.views.BoardsView" label="Boards" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/board.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="ilg.gnuarmeclipse.packs.ui.views.BoardsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="ilg.gnuarmeclipse.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wxycYs_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.ui.views.KeywordsView" label="Keywords" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/info_obj.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="ilg.gnuarmeclipse.packs.ui.views.KeywordsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="ilg.gnuarmeclipse.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wxycY8_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.ui.views.PackagesView" label="Packs" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/packages.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="ilg.gnuarmeclipse.packs.ui.views.PacksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="ilg.gnuarmeclipse.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wxzDcM_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.ui.views.OutlineView" label="Outline" iconURI="platform:/plugin/ilg.gnuarmeclipse.packs.ui/icons/outline_co.png" tooltip="" category="C/C++ Packs" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="ilg.gnuarmeclipse.packs.ui.views.OutlineView"/>
    <persistedState key="originalCompatibilityViewBundle" value="ilg.gnuarmeclipse.packs.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++ Packs</tags>
  </descriptors>
  <descriptors xmi:id="_wxzDcc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails" label="Problem Details" iconURI="platform:/plugin/org.eclipse.cdt.codan.ui/icons/edit_bug.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.codan.internal.ui.views.ProblemDetails"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.codan.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wxzDcs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.executablesView" label="Executables" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/obj16/exec_view_obj.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.executables.ExecutablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wxzqgM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.SignalsView" label="Signals" iconURI="platform:/plugin/org.eclipse.cdt.debug.ui/icons/view16/signals_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.debug.internal.ui.views.signals.FlexibleSignalsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wxzqgc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.gdb.ui.tracecontrol.view" label="Trace Control" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/tracecontrol_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.tracepoints.TraceControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx04oM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.gdb.ui.osresources.view" label="OS Resources" iconURI="platform:/plugin/org.eclipse.cdt.dsf.gdb.ui/icons/full/view16/osresources_view.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.gdb.internal.ui.osview.OSResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.gdb.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx04oc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.view" label="Disassembly" iconURI="platform:/plugin/org.eclipse.cdt.dsf.ui/icons/disassembly.gif" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.dsf.debug.internal.ui.disassembly.DisassemblyView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.dsf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx04os_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.views.MakeView" label="Make Target" iconURI="platform:/plugin/org.eclipse.cdt.make.ui/icons/view16/make_target.gif" tooltip="" category="Make" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.make.ui.views.MakeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.make.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Make</tags>
  </descriptors>
  <descriptors xmi:id="_wx1fsM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CView" label="C/C++ Projects" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/cview.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.cview.CView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wx1fsc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.IndexView" label="C/C++ Index" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/types.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.indexview.IndexView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wx1fss_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.includeBrowser" label="Include Browser" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/includeBrowser.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.includebrowser.IBViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wx2GwM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.callHierarchy" label="Call Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/call_hierarchy.gif" tooltip="" allowMultiple="true" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.callhierarchy.CHViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wx2Gwc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.typeHierarchy" label="Type Hierarchy" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/class_hi.gif" tooltip="" category="C/C++" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.cdt.internal.ui.typehierarchy.THViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:C/C++</tags>
  </descriptors>
  <descriptors xmi:id="_wx2Gws_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.texteditor.TemplatesView" label="Templates" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/templates.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.texteditor.templates.TemplatesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.cdt.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx2Gw8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.DebugView" label="Debug" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/debug_view.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.launch.LaunchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx2t0M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.BreakpointView" label="Breakpoints" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/breakpoint_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.breakpoints.BreakpointsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx2t0c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.VariableView" label="Variables" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/variable_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.variables.VariablesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx2t0s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.ExpressionView" label="Expressions" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/watchlist_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.expression.ExpressionView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx3U4M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.RegisterView" label="Registers" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/register_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.registers.RegistersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx3U4c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.ModuleView" label="Modules" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/module_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.modules.ModulesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx3U4s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.MemoryView" label="Memory" iconURI="platform:/plugin/org.eclipse.debug.ui/icons/full/eview16/memory_view.png" tooltip="" allowMultiple="true" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.debug.internal.ui.views.memory.MemoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx3U48_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesView" label="Git Repositories" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/repo_rep.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.repository.RepositoriesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wx378M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.StagingView" label="Git Staging" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/staging.png" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.staging.StagingView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wx378c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.InteractiveRebaseView" label="Git Interactive Rebase" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/rebase_interactive.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.rebase.RebaseInteractiveView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wx4jAM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.CompareTreeView" label="Git Tree Compare" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/obj16/gitrepository.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.dialogs.CompareTreeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wx4jAc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.ReflogView" label="Git Reflog" iconURI="platform:/plugin/org.eclipse.egit.ui/icons/eview16/reflog.gif" tooltip="" category="Git" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.egit.ui.internal.reflog.ReflogView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.egit.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Git</tags>
  </descriptors>
  <descriptors xmi:id="_wx4jAs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.help.ui.HelpView" label="Help" iconURI="platform:/plugin/org.eclipse.help.ui/icons/view16/help_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.help.ui.internal.views.HelpView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.help.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_wx5KEM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.dataviewers.charts.view" label="Chart View" iconURI="platform:/plugin/org.eclipse.linuxtools.dataviewers.charts/icons/chart_icon.png" tooltip="" allowMultiple="true" category="Charts" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.dataviewers.charts.view.ChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.dataviewers.charts"/>
    <tags>View</tags>
    <tags>categoryTag:Charts</tags>
  </descriptors>
  <descriptors xmi:id="_wx5KEc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.gprof.view" label="gprof" iconURI="platform:/plugin/org.eclipse.linuxtools.gprof/icons/toggle.gif" tooltip="Gprof view displays the profiling information contained in a gmon.out file" allowMultiple="true" category="Profiling" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.linuxtools.internal.gprof.view.GmonView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.linuxtools.gprof"/>
    <tags>View</tags>
    <tags>categoryTag:Profiling</tags>
  </descriptors>
  <descriptors xmi:id="_wx5KEs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.remote.ui.view.connections" label="Connections" iconURI="platform:/plugin/org.eclipse.remote.ui/icons/connection.gif" tooltip="" category="Connections" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.remote.internal.ui.views.RemoteConnectionsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.remote.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Connections</tags>
  </descriptors>
  <descriptors xmi:id="_wx5xIM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.shells.ui.view.commandsView" label="Remote Shell" iconURI="platform:/plugin/org.eclipse.rse.shells.ui/icons/full/cview16/commands_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.shells.ui.view.SystemCommandsViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.shells.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx5xIc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.terminals.ui.view.TerminalView" label="Terminals" iconURI="platform:/plugin/org.eclipse.rse.terminals.ui/icons/terminal_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.terminals.ui.views.TerminalViewer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.terminals.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx6YMM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.ui.view.systemView" label="Remote Systems" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.ui.view.SystemViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx6YMc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.ui.view.teamView" label="Team" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/team_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.ui.view.team.SystemTeamViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx6YMs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.ui.view.systemTableView" label="Remote System Details" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/cview16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.ui.view.SystemTableViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx6_QM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.ui.view.SystemSearchView" label="Remote Search" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/obj16/system_search.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.ui.view.search.SystemSearchViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx6_Qc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.ui.view.scratchpad.SystemScratchpadViewPart" label="Remote Scratchpad" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/scratchpad_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.ui.view.scratchpad.SystemScratchpadViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx6_Qs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.ui.view.monitorView" label="Remote Monitor" iconURI="platform:/plugin/org.eclipse.rse.ui/icons/full/view16/system_view.gif" tooltip="" category="Remote Systems" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.rse.internal.ui.view.monitor.SystemMonitorViewPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.rse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Remote Systems</tags>
  </descriptors>
  <descriptors xmi:id="_wx6_Q8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.SearchResultView" label="Classic Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search.internal.ui.SearchResultView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx6_RM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.views.SearchView" label="Search" iconURI="platform:/plugin/org.eclipse.search/icons/full/eview16/searchres.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.search2.internal.ui.SearchView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.search"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx7mUM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.TraceView" label="TCF Trace" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/tcf.gif" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tcf.internal.debug.ui.trace.TraceView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tcf.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx7mUc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.ProfilerView" label="TCF Profiler" iconURI="platform:/plugin/org.eclipse.tcf.debug.ui/icons/profiler.png" tooltip="" category="Debug" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tcf.internal.debug.ui.profiler.ProfilerView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tcf.debug.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Debug</tags>
  </descriptors>
  <descriptors xmi:id="_wx7mUs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.sync.views.SynchronizeView" label="Synchronize" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/synch_synch.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.synchronize.SynchronizeView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_wx8NYM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.ui.GenericHistoryView" label="History" iconURI="platform:/plugin/org.eclipse.team.ui/icons/full/eview16/history_view.gif" tooltip="" allowMultiple="true" category="Team" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.team.internal.ui.history.GenericHistoryView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.team.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Team</tags>
  </descriptors>
  <descriptors xmi:id="_wx8NYc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.view.TerminalView" label="Terminal" iconURI="platform:/plugin/org.eclipse.tm.terminal.view/icons/cview16/terminal_view.gif" tooltip="" allowMultiple="true" category="Terminal" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tm.internal.terminal.view.TerminalView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tm.terminal.view"/>
    <tags>View</tags>
    <tags>categoryTag:Terminal</tags>
  </descriptors>
  <descriptors xmi:id="_wx8NYs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.controlflow" label="Control Flow" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/control_flow_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.os.linux.ui.views.controlflow.ControlFlowView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wx8NY8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.resources" label="Resources" iconURI="platform:/plugin/org.eclipse.tracecompass.analysis.os.linux.ui/icons/eview16/resources_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.os.linux.ui.views.resources.ResourcesView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wx8NZM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tracecompass.analysis.os.linux.views.cpuusage" label="CPU Usage" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.analysis.os.linux.ui.views.cpuusage.CpuUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.analysis.os.linux.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wx80cM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.views.control" label="Control" iconURI="platform:/plugin/org.eclipse.tracecompass.lttng2.control.ui/icons/eview16/control_view.gif" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.control.ui.views.ControlView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.control.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wx80cc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.lttng2.ust.memoryusage" label="UST Memory Usage" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.lttng2.ust.ui.views.memusage.MemoryUsageView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.lttng2.ust.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wx80cs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.views.timegraph" label="XML Time Graph View" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.analysis.xml.ui.views.timegraph.XmlTimeGraphView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wx80c8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.tmf.analysis.xml.ui.views.xyview" label="XML XY Chart View" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="LTTng" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.analysis.xml.ui.views.xychart.XmlXYView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.analysis.xml.ui"/>
    <tags>View</tags>
    <tags>categoryTag:LTTng</tags>
  </descriptors>
  <descriptors xmi:id="_wx80dM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.pcap.ui.view.stream.list" label="Stream List" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.pcap.ui/icons/stream_list_view.gif" tooltip="" category="Network Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.internal.tmf.pcap.ui.stream.StreamListView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.pcap.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Network Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bgM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.timechart" label="Time Chart" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/timechart_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.timechart.TimeChartView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bgc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.ssvisualizer" label="State System Explorer" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/events_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statesystem.TmfStateSystemExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bgs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.colors" label="Colors" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/colors_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.colors.ColorsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bg8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.filter" label="Filters" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/filters_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.filter.FilterView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bhM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.tmfUml2SDSyncView" label="Sequence Diagram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/sequencediagram_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.uml2sd.SDView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bhc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.statistics" label="Statistics" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/statistics_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.statistics.TmfStatisticsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bhs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.histogram" label="Histogram" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/histogram.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.histogram.HistogramView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx9bh8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.callstack" label="Call Stack" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/callstack_view.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.callstack.CallStackView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx-CkM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.synchronization" label="Synchronization" iconURI="platform:/plugin/org.eclipse.tracecompass.tmf.ui/icons/eview16/synced.gif" tooltip="" category="Tracing" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.tracecompass.tmf.ui.views.synchronization.TmfSynchronizationView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.tracecompass.tmf.ui"/>
    <tags>View</tags>
    <tags>categoryTag:Tracing</tags>
  </descriptors>
  <descriptors xmi:id="_wx-Ckc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.internal.introview" label="Welcome" iconURI="platform:/plugin/org.eclipse.ui/icons/full/eview16/defaultview_misc.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.ViewIntroAdapterPart"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-Cks_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.browser.view" label="Internal Web Browser" iconURI="platform:/plugin/org.eclipse.ui.browser/icons/obj16/internal_browser.gif" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.browser.WebBrowserView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.browser"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-Ck8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.cheatsheets.views.CheatSheetView" label="Cheat Sheets" iconURI="platform:/plugin/org.eclipse.ui.cheatsheets/icons/view16/cheatsheet_view.gif" tooltip="" category="Help" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.cheatsheets.views.CheatSheetView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.cheatsheets"/>
    <tags>View</tags>
    <tags>categoryTag:Help</tags>
  </descriptors>
  <descriptors xmi:id="_wx-ClM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.console.ConsoleView" label="Console" iconURI="platform:/plugin/org.eclipse.ui.console/icons/full/cview16/console_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.console.ConsoleView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.console"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-poM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ProgressView" label="Progress" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/pview.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.progress.ProgressView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-poc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ResourceNavigator" label="Navigator" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/filenav_nav.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.navigator.ResourceNavigator"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-pos_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.BookmarkView" label="Bookmarks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/bkmrk_nav.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.BookmarksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-po8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.TaskList" label="Tasks" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/tasks_tsk.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.TasksView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-ppM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ProblemView" label="Problems" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.ProblemsView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-ppc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.AllMarkersView" label="Markers" iconURI="platform:/plugin/org.eclipse.ui.ide/icons/full/eview16/problems_view.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.internal.views.markers.AllMarkersView"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.ide"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx-pps_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.ProjectExplorer" label="Project Explorer" iconURI="platform:/plugin/org.eclipse.ui.navigator.resources/icons/full/eview16/resource_persp.gif" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.navigator.resources.ProjectExplorer"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.navigator.resources"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx_QsM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.PropertySheet" label="Properties" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/prop_ps.png" tooltip="" allowMultiple="true" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.properties.PropertySheet"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <descriptors xmi:id="_wx_Qsc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ContentOutline" label="Outline" iconURI="platform:/plugin/org.eclipse.ui.views/icons/full/eview16/outline_co.png" tooltip="" category="General" closeable="true" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.e4.compatibility.CompatibilityView">
    <persistedState key="originalCompatibilityViewClass" value="org.eclipse.ui.views.contentoutline.ContentOutline"/>
    <persistedState key="originalCompatibilityViewBundle" value="org.eclipse.ui.views"/>
    <tags>View</tags>
    <tags>categoryTag:General</tags>
  </descriptors>
  <trimContributions xmi:id="_2r10UF9tEeO-yojH_y4TJA" elementId="org.eclipse.ui.ide.application.trimcontribution.QuickAccess" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" toBeRendered="false" parentId="org.eclipse.ui.main.toolbar" positionInParent="last">
    <children xsi:type="menu:ToolControl" xmi:id="_76uUAF9tEeO-yojH_y4TJA" elementId="Spacer Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:PerspectiveSpacer</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_8tJPcF9tEeO-yojH_y4TJA" elementId="SearchField" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.workbench/org.eclipse.ui.internal.quickaccess.SearchField">
      <tags>move_after:Spacer Glue</tags>
      <tags>HIDEABLE</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
    <children xsi:type="menu:ToolControl" xmi:id="_9LgmcF9tEeO-yojH_y4TJA" elementId="Search-PS Glue" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.renderers.swt/org.eclipse.e4.ui.workbench.renderers.swt.LayoutModifierToolControl">
      <tags>glue</tags>
      <tags>move_after:SearchField</tags>
      <tags>SHOW_RESTORE_MENU</tags>
    </children>
  </trimContributions>
  <snippets xsi:type="advanced:Perspective" xmi:id="_xWDnEM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CPerspective" selectedElement="_xWDnEc_kEe-m5_Ju82ZM_w" label="C/C++" iconURI="platform:/plugin/org.eclipse.cdt.ui/icons/view16/c_pers.gif" tooltip="C/C++">
    <persistedState key="persp.hiddenItems" value="persp.hideMenuSC:org.eclipse.jdt.ui.refactoring.menu,persp.hideMenuSC:org.eclipse.jdt.ui.source.menu,persp.hideToolbarSC:org.eclipse.debug.ui.commands.RunToLine,persp.hideToolbarSC:print,persp.hideToolbarSC:org.eclipse.ui.edit.text.toggleShowSelectedElementOnly,"/>
    <tags>persp.actionSet:org.eclipse.ui.cheatsheets.actionSet</tags>
    <tags>persp.actionSet:org.eclipse.rse.core.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.search.searchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.annotationNavigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.navigation</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.convertLineDelimitersTo</tags>
    <tags>persp.actionSet:org.eclipse.ui.externaltools.ExternalToolsSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.keyBindings</tags>
    <tags>persp.actionSet:org.eclipse.ui.actionSet.openFiles</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.SearchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.CElementCreationActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.NavigateActionSet</tags>
    <tags>persp.viewSC:org.eclipse.ui.console.ConsoleView</tags>
    <tags>persp.viewSC:org.eclipse.search.ui.views.SearchView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ContentOutline</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ProblemView</tags>
    <tags>persp.viewSC:org.eclipse.cdt.ui.CView</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.ResourceNavigator</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.PropertySheet</tags>
    <tags>persp.viewSC:org.eclipse.ui.views.TaskList</tags>
    <tags>persp.viewSC:ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView</tags>
    <tags>persp.showIn:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
    <tags>persp.viewSC:org.eclipse.cdt.codan.internal.ui.views.ProblemDetails</tags>
    <tags>persp.actionSet:org.eclipse.debug.ui.breakpointActionSet</tags>
    <tags>persp.viewSC:org.eclipse.cdt.make.ui.views.MakeView</tags>
    <tags>persp.actionSet:org.eclipse.cdt.make.ui.makeTargetActionSet</tags>
    <tags>persp.perspSC:org.eclipse.debug.ui.DebugPerspective</tags>
    <tags>persp.perspSC:org.eclipse.team.ui.TeamSynchronizingPerspective</tags>
    <tags>persp.actionSet:org.eclipse.debug.ui.launchActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.buildConfigActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.NavigationActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.OpenActionSet</tags>
    <tags>persp.actionSet:org.eclipse.cdt.ui.CodingActionSet</tags>
    <tags>persp.actionSet:org.eclipse.ui.edit.text.actionSet.presentation</tags>
    <tags>persp.showIn:org.eclipse.cdt.ui.includeBrowser</tags>
    <tags>persp.showIn:org.eclipse.cdt.ui.CView</tags>
    <tags>persp.showIn:org.eclipse.ui.navigator.ProjectExplorer</tags>
    <tags>persp.viewSC:org.eclipse.ui.navigator.ProjectExplorer</tags>
    <tags>persp.viewSC:org.eclipse.cdt.ui.includeBrowser</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFolderCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFolderCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewSourceFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewHeaderFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewFileCreationWizard</tags>
    <tags>persp.newWizSC:org.eclipse.cdt.ui.wizards.NewClassCreationWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.appwiz.AppWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.profile.ui.wizards.ZpeProjectWizard</tags>
    <tags>persp.newWizSC:com.xilinx.sdk.sw.ui.NewBspWizard</tags>
    <children xsi:type="basic:PartSashContainer" xmi:id="_xWDnEc_kEe-m5_Ju82ZM_w" selectedElement="_xWDnEs_kEe-m5_Ju82ZM_w" horizontal="true">
      <children xsi:type="basic:PartSashContainer" xmi:id="_xWDnEs_kEe-m5_Ju82ZM_w" containerData="2500" selectedElement="_xWDnE8_kEe-m5_Ju82ZM_w">
        <children xsi:type="basic:PartStack" xmi:id="_xWDnE8_kEe-m5_Ju82ZM_w" elementId="topLeft" containerData="7500" selectedElement="_xWDnFM_kEe-m5_Ju82ZM_w">
          <children xsi:type="advanced:Placeholder" xmi:id="_xWDnFM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.ProjectExplorer"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_xWDnFc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.CView" toBeRendered="false"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_xWDnFs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ResourceNavigator" toBeRendered="false"/>
          <children xsi:type="advanced:Placeholder" xmi:id="_xWDnF8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.BookmarkView" toBeRendered="false"/>
        </children>
        <children xsi:type="basic:PartStack" xmi:id="_xWDnGM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementViewMStack" containerData="2500" selectedElement="_xWDnGc_kEe-m5_Ju82ZM_w">
          <children xsi:type="advanced:Placeholder" xmi:id="_xWDnGc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.TargetManagementView"/>
        </children>
      </children>
      <children xsi:type="basic:PartSashContainer" xmi:id="_xWDnGs_kEe-m5_Ju82ZM_w" containerData="7500">
        <children xsi:type="basic:PartSashContainer" xmi:id="_xWDnG8_kEe-m5_Ju82ZM_w" containerData="7500" horizontal="true">
          <children xsi:type="advanced:Placeholder" xmi:id="_xWDnHM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editorss" containerData="7500"/>
          <children xsi:type="basic:PartStack" xmi:id="_xWDnHc_kEe-m5_Ju82ZM_w" elementId="topRight" containerData="2500" selectedElement="_xWDnHs_kEe-m5_Ju82ZM_w">
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnHs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ContentOutline"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnH8_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.managedbuild.packs.ui.views.DocsView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnIM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.views.MakeView"/>
          </children>
        </children>
        <children xsi:type="basic:PartSashContainer" xmi:id="_xWDnIc_kEe-m5_Ju82ZM_w" containerData="2500" horizontal="true">
          <children xsi:type="basic:PartStack" xmi:id="_xWDnIs_kEe-m5_Ju82ZM_w" elementId="bottom" containerData="5000" selectedElement="_xWDnI8_kEe-m5_Ju82ZM_w">
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnI8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.ProblemView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnJM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.TaskList"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnJc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.console.ConsoleView"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnJs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.PropertySheet"/>
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnJ8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.terminal.sdkterminal"/>
          </children>
          <children xsi:type="basic:PartStack" xmi:id="_xWDnKM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogViewMStack" containerData="5000" selectedElement="_xWDnKc_kEe-m5_Ju82ZM_w">
            <children xsi:type="advanced:Placeholder" xmi:id="_xWDnKc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.logger.SdkLogView"/>
          </children>
        </children>
      </children>
    </children>
  </snippets>
  <commands xmi:id="_wuAIkM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.pageUp" commandName="Select Page Up" description="Select to the top of the page" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAIkc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.toggleWordWrap" commandName="Toggle Word Wrap" description="Toggle word wrap in the current text editor" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvoM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectPreviousTraceRecord" commandName="Previous Trace Record" description="Select Previous Trace Record" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvoc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaseline" commandName="Reset quickdiff baseline" category="_wt_hns_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuAvos_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.ResetQuickdiffBaselineTarget" name="Reset target (HEAD, HEAD^1)" optional="false"/>
  </commands>
  <commands xmi:id="_wuAvo8_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.commands.updateCommand" commandName="Refresh" category="_wt_hhM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvpM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.targetCreateCommand" commandName="Create Make Target" description="Create a new make build target for the selected container." category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvpc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.search.findrefs" commandName="References" description="Searches for references to the selected element in the workspace" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvps_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.openFileSearchPage" commandName="File Search" description="Open the Search dialog's file search page" category="_wt_hlM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvp8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarerefresh" commandName="Refresh" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvqM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewAddRepository" commandName="Add a Git Repository" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvqc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.copyBuildIdCommand" commandName="Copy Build Id To Clipboard" description="Copies the build id to the clipboard." category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvqs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.textEnd" commandName="Select Text End" description="Select to the end of the text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvq8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.deletePreviousWord" commandName="Delete Previous Word" description="Delete the previous word" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvrM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.wordPrevious" commandName="Previous Word" description="Go to the previous word" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvrc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.connect" commandName="Connect" description="Connect to a process" category="_wt_hj8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvrs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.preferences" commandName="Preferences" description="Open the preferences dialog" category="_wt_hic_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuAvr8_kEe-m5_Ju82ZM_w" elementId="preferencePageId" name="Preference Page"/>
  </commands>
  <commands xmi:id="_wuAvsM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.delimiter.unix" commandName="Convert Line Delimiters to Unix (LF, \n, 0A, &#xb6;)" description="Converts the line delimiters to Unix (LF, \n, 0A, &#xb6;)" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvsc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.Squash" commandName="Squash Commits" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvss_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.Edit" commandName="Edit Commit" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvs8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.copy" commandName="Copy" description="Copy the selection to the clipboard" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvtM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.nextView" commandName="Next View" description="Switch to the next view" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvtc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.specific_content_assist.command" commandName="C/C++ Content Assist" description="A parameterizable command that invokes content assist with a single completion proposal category" category="_wt-6cs_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuAvts_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.specific_content_assist.category_id" name="type" optional="false"/>
  </commands>
  <commands xmi:id="_wuAvt8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.bootimage.commands.CreateBootImage" commandName="Create Boot Image" category="_wt_hkc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvuM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.revertToSaved" commandName="Revert to Saved" description="Revert to the last saved state" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvuc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.properties.NewPropertySheetCommand" commandName="Properties" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvus_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.addToWorkingSet" commandName="Add to Working Set" description="Adds the selected object to a working set." category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvu8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.ui.sdklibhelpint" commandName="Xilinx OS and Libraries Help" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvvM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.RunToLine" commandName="Run to Line" description="Resume and break when execution reaches the current line" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvvc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.ui.TeamSynchronizingPerspective" commandName="Team Synchronizing" description="Open the Team Synchronizing Perspective" category="_wt_hms_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvvs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.search.findrefs.workingset" commandName="References in Working Set" description="Searches for references to the selected element in a working set" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvv8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewPaste" commandName="Paste Repository Path or URI" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvwM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.showChangeRulerInformation" commandName="Show Quick Diff Ruler Tooltip" description="Displays quick diff or revision information for the caret line in a focused hover" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvwc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.upperCase" commandName="To Upper Case" description="Changes the selection to upper case" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvws_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ConfigureFetch" commandName="Configure Upstream Fetch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvw8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.goInto" commandName="Go Into" description="Navigate into the selected item" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvxM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.ConfigureUpstreamPush" commandName="Configure Upstream Push" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvxc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.OpenRunConfigurations" commandName="Run..." description="Open run launch configuration dialog" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvxs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.windowEnd" commandName="Select Window End" description="Select to the end of the window" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvx8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.minimizePart" commandName="Minimize Active View or Editor" description="Minimizes the active view or editor" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvyM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.remote.ui.command.openConnection" commandName="Open Connection" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvyc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.command1" commandName="Terminal view insert" category="_wt_hkM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvys_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.open.outline" commandName="Show outline" description="Shows outline" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvy8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.ungroupDebugContexts" commandName="Ungroup" description="Ungroups the selected debug contexts" category="_wt_hj8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvzM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Untrack" commandName="Untrack" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvzc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.xlcm.commands.manage" commandName="Manage License..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvzs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.ToggleLineBreakpoint" commandName="Toggle Line Breakpoint" description="Creates or removes a line breakpoint" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAvz8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.showSystemMenu" commandName="Show System Menu" description="Show the system menu" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv0M_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.commands.configureQEMU" commandName="Configure QEMU Settings" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv0c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.menu.rebuildIndex" commandName="Rebuild Index" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv0s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv08_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.commands.viewXSDBConsole" commandName="XSCT Console" description="View XSCT Console" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv1M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tracecompass.tmf.ui.copy_to_clipboard" commandName="Copy to Clipboard" description="Copy to Clipboard" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv1c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.savePerspective" commandName="Save Perspective As" description="Save the current perspective" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv1s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.castToArray" commandName="Cast To Type..." category="_wt_hnM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv18_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.internal.reflog.CheckoutCommand" commandName="Checkout" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuAv2M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.stopTracing" commandName="Stop Tracing " description="Stop Tracing Experiment" category="_wt_hos_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWsM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.clear_offset" commandName="Clear Time Offset" description="Clear time offset" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWsc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.move" commandName="Move..." description="Move the selected item" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWss_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.delete" commandName="Delete" description="Delete Target Node" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWs8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.command.configureTrace" commandName="Configure Git Debug Trace" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWtM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDDown" commandName="Scroll down" description="Scroll down the sequence diagram" category="_wt_hmM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWtc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.linkWithEditor" commandName="Toggle Link with Editor" description="Toggles linking of a view's selection with the active editor's selection" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWts_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.ignoreWhiteSpace" commandName="Ignore White Space" description="Ignore white space where applicable" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWt8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.toggleBlockSelectionMode" commandName="Toggle Block Selection" description="Toggle block / column selection in the current text editor" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWuM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactor.extract.function" commandName="Extract Function - Refactoring " description="Extracts a function for the selected list of expressions or statements" category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWuc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewRefresh" commandName="Refresh" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWus_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.goToResource" commandName="Go to" description="Go to a particular resource in the active view" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWu8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.resetPerspective" commandName="Reset Perspective" description="Reset the current perspective to its default state" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWvM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.buildLast" commandName="Repeat Working Set Build" description="Repeat the last working set build" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWvc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.buildProject" commandName="Build Project" description="Build the selected project" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWvs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.CompareIndexWithHead" commandName="Compare File in Index with HEAD Revision" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWv8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.textStart" commandName="Text Start" description="Go to the beginning of the text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWwM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.paste" commandName="Paste" description="Paste from the clipboard" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWwc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.part.nextPage" commandName="Next Page" description="Switch to the next page" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWws_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.menu.updateUnresolvedIncludes" commandName="Re-resolve Unresolved Includes" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWw8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.comment" commandName="Comment/Uncomment" description="Comments/Uncomments the selected lines" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWxM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.remove.block.comment" commandName="Remove Block Comment" description="Removes the block comment enclosing the selection" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWxc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CreateBranch" commandName="Create Branch" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWxs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactor.extract.constant" commandName="Extract Constant - Refactoring " description="Extracts a constant for the selected expression" category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWx8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.previous" commandName="Previous" description="Navigate to the previous item" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWyM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.clean" commandName="Clean..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWyc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewClone" commandName="Clone a Git Repository" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWys_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.open.include.browser" commandName="Open Include Browser" description="Open an include browser on the selected element" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWy8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.rename" commandName="Rename" description="Rename the selected item" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWzM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.clear.mark" commandName="Clear Mark" description="Clear the mark" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWzc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.buildAll" commandName="Build All" description="Build all projects" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWzs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.OpenInTextEditorCommand" commandName="Open in Text Editor" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBWz8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.sw.commands.scanrepo" commandName="Scan Repositories" category="_wt_hgc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW0M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewRemove" commandName="Remove Repository" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW0c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.reverseStepOver" commandName="Reverse Step Over" description="Perform Reverse Step Over" category="_wt_hnc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW0s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.cut.line.to.beginning" commandName="Cut to Beginning of Line" description="Cut to the beginning of a line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW08_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewCreateRepository" commandName="Create a Repository" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW1M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editors.revisions.rendering.cycle" commandName="Cycle Revision Coloring Mode" description="Cycles through the available coloring modes for revisions" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW1c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.editRegisterGroup" commandName="Edit Register Group" description="Edits a Register Group" category="_wt_hgs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW1s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.PushTags" commandName="Push Tags..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW18_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.search.findrefs.project" commandName="References in Project" description="Searches for references to the selected element in the enclosing project" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW2M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.ContinueRebase" commandName="Continue Rebase" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW2c_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences" commandName="Modify ATF Configuration"/>
  <commands xmi:id="_wuBW2s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.search.finddecl.project" commandName="Declaration in Project" description="Searches for declarations of the selected element in the enclosing project" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW28_kEe-m5_Ju82ZM_w" elementId="org.eclipse.help.ui.closeTray" commandName="Close User Assistance Tray" description="Close the user assistance tray containing context help information and cheat sheets." category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW3M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.debug.ui.commands.toggleFilterVariants" commandName="Filter Variants by Discriminant Value" category="_wt_hoM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW3c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CreatePatch" commandName="Create Patch" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW3s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ltk.ui.refactoring.commands.moveResources" commandName="Move Resources" description="Move the selected resources and notify LTK participants." category="_wt_hm8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW38_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.managedbuilder.ui.rebuildConfigurations" commandName="Build Selected Configurations" category="_wt_ho8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW4M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.Resume" commandName="Resume" description="Resume" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW4c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.StepInto" commandName="Step Into" description="Step into" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW4s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.restartWorkbench" commandName="Restart" description="Restart the workbench" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW48_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.importer.openDirectory" commandName="Open Projects from File System..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW5M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.toggleOverwrite" commandName="Toggle Overwrite" description="Toggle overwrite mode" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW5c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.folding.collapse_all" commandName="Collapse All" description="Collapses all folded regions" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW5s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewClearCredentials" commandName="Clear Credentials" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW58_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.ToggleWatchpoint" commandName="Toggle Watchpoint" description="Creates or removes a watchpoint" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW6M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.closeRendering" commandName="Close Rendering" description="Close the selected rendering." category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW6c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewOpenInEditor" commandName="Open in Editor" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW6s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnChannel" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW68_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.showViewMenu" commandName="Show View Menu" description="Show the view menu" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW7M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Commit" commandName="Commit..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW7c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDRight" commandName="Scroll right" description="Scroll right the sequence diagram" category="_wt_hmM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW7s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.submodule.update" commandName="Update Submodule" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW78_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.source.quickMenu" commandName="Show Source Quick Menu" description="Shows the source quick menu" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW8M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.ProfileLast" commandName="Profile" description="Launch in profile mode" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW8c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.shiftRight" commandName="Shift Right" description="Shift a block of text to the right" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW8s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.backwardHistory" commandName="Backward History" description="Move backward in the editor navigation history" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW88_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Pull" commandName="Pull" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW9M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.removeTrailingWhitespace" commandName="Remove Trailing Whitespace" description="Removes the trailing whitespace of each line" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW9c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.findIncremental" commandName="Incremental Find" description="Incremental find" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW9s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.swap.mark" commandName="Swap Mark" description="Swap the mark with the cursor position" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW98_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.opencview" commandName="Show in C/C++ Project view" description="Shows the selected resource in the C/C++ Project view" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW-M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.ImportChangedProjectsCommandId" commandName="Import Changed Projects" description="Import or create in local Git repository" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW-c_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawareModeExec" commandName="Auto refresh on exec" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW-s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.analysis_help" commandName="Help" description="Help" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW-8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureFetch" commandName="Configure Fetch..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW_M_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.profile.ui.commands.configurefsbl" commandName="Configure FSBL Parameters" category="_wt_hoc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW_c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.navigate.open.type.in.hierarchy" commandName="Open Type in Hierarchy" description="Open a type in the type hierarchy view" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW_s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.navigate.open.element.in.call.hierarchy" commandName="Open Element in Call Hierarchy" description="Open an element in the call hierarchy view" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBW_8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.shiftLeft" commandName="Shift Left" description="Shift a block of text to the left" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXAM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Push" commandName="Push..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXAc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tools.command.linkgen" commandName="Generate Linker Script" description="Generates a Linker Script" category="_wt_hoc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXAs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.command.breakpointProperties" commandName="C/C++ Breakpoint Properties" description="View and edit properties for a given C/C++ breakpoint" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXA8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.uncall" commandName="Uncall" description="Perform Uncall" category="_wt_hnc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXBM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewNewRemote" commandName="Create Remote..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXBc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.opendecl" commandName="Open declaration" description="Follow to the directive definition" category="_wt_hks_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXBs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.Restart" commandName="Restart" description="Restart a process or debug target without terminating and re-launching" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXB8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.stash.drop" commandName="Delete Stashed Commit..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXCM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.delete.line" commandName="Delete Line" description="Delete a line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXCc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.refresh" commandName="Refresh" description="Refresh Node Configuration" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXCs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.DebugPerspective" commandName="Debug" description="Open the debug perspective" category="_wt_hms_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXC8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.RenameBranch" commandName="Rename Branch..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXDM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnSession" commandName="Enable Channel..." description="Enable a Trace Channel" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXDc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.expandAll" commandName="Expand All" description="Expand the current tree" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXDs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.executeScript" commandName="Execute Command Script..." description="Execute Command Script" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXD8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.saveAll" commandName="Save All" description="Save all current contents" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXEM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnChannel" commandName="Enable Event..." description="Enable Event" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXEc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.closeOthers" commandName="Close Others" description="Close all editors except the one that is active" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXEs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editors.quickdiff.revertLine" commandName="Revert Line" description="Revert the current line" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXE8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.OpenDebugConfigurations" commandName="Debug..." description="Open debug launch configuration dialog" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXFM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.SimpleFetch" commandName="Fetch from Upstream" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXFc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.start" commandName="Start" description="Start Trace Session" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXFs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.previousEditor" commandName="Previous Editor" description="Switch to the previous editor" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXF8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.findReplace" commandName="Find and Replace" description="Find and replace text" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXGM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.openFile" commandName="Open File" description="Opens a file" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXGc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.zoomIn" commandName="Zoom In" description="Zoom in text, increase default font size for text editors" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXGs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.MergeTool" commandName="Merge Tool" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXG8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.import" commandName="Import..." description="Import traces into project" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXHM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.OpenMarkersView" commandName="Open Another" description="Open another view" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXHc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.utils.enablewebtalk" commandName="Enable Webtalk" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXHs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.command.shareProject" commandName="Share with Git" description="Share the project using Git" category="_wt_hns_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuBXH8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.command.projectNameParameter" name="Project" optional="false"/>
  </commands>
  <commands xmi:id="_wuBXIM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.fullscreenmode" commandName="Toggle Full Screen" description="Toggles the window between full screen and normal" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXIc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.newRendering" commandName="New Rendering" description="Add a new rendering." category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXIs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.equinox.p2.ui.sdk.installationDetails" commandName="Installation Details" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXI8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize resources in the workspace with another location" category="_wt-6cM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXJM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.nextEditor" commandName="Next Editor" description="Switch to the next editor" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXJc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.closeUnrelatedProjects" commandName="Close Unrelated Projects" description="Close unrelated projects" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXJs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.internal.reflog.CopyCommand" commandName="Copy SHA-1" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXJ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.helpContents" commandName="Help Contents" description="Open the help contents" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXKM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.uncomment" commandName="Uncomment" description="Uncomment the selected # style comment lines" category="_wt_hks_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXKc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Reset" commandName="Reset..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXKs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.delete" commandName="Delete" description="Delete the selection" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXK8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.delete.line.to.beginning" commandName="Delete to Beginning of Line" description="Delete to the beginning of a line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXLM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.performTextSearchWorkspace" commandName="Find Text in Workspace" description="Searches the files in the workspace for specific text." category="_wt_hlM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXLc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.commands.programfpga" commandName="Program FPGA" category="_wt_hoc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXLs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.RenameBranch" commandName="Rename Branch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXL8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Synchronize" commandName="Synchronize" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXMM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.sw.commands.bspsettings" commandName="BSP Settings" category="_wt_hgc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXMc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.findPrevious" commandName="Find Previous" description="Find previous item" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXMs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.ToggleBreakpoint" commandName="Toggle Breakpoint" description="Creates or removes a breakpoint" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXM8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.exporttracepkg" commandName="Export Trace Package..." category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXNM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.addBookmark" commandName="Add Bookmark" description="Add a bookmark" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXNc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.reverseToggle" commandName="Reverse Toggle" description="Toggle Reverse Debugging" category="_wt_hnc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuBXNs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.Revert" commandName="Revert Commit" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9wM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.StashDrop" commandName="Delete Stashed Commit..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9wc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.scroll.lineUp" commandName="Scroll Line Up" description="Scroll up one line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9ws_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.menu.findUnresolvedIncludes" commandName="Search for Unresolved Includes" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9w8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.set.mark" commandName="Set Mark" description="Set the mark" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9xM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.folding.toggle" commandName="Toggle Folding" description="Toggles folding in the current editor" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9xc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.toggleShowWhitespaceCharacters" commandName="Show Whitespace Characters" description="Shows whitespace characters in current text editor" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9xs_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.ui.commands.buildsettings" commandName="Change C/C++ Build Settings" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9x8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableChannel" commandName="Disable Channel" description="Disable a Trace Channel" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9yM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.revert" commandName="Revert" description="Revert to the last saved state" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9yc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView" commandName="Show View" description="Shows a particular view" category="_wt-6cc_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuB9ys_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.viewId" name="View"/>
    <parameters xmi:id="_wuB9y8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.secondaryId" name="Secondary Id"/>
    <parameters xmi:id="_wuB9zM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.views.showView.makeFast" name="As FastView"/>
  </commands>
  <commands xmi:id="_wuB9zc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactor.hide.method" commandName="Hide Member Function..." category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9zs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Ignore" commandName="Ignore" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9z8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.Edit" commandName="Edit Commit" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB90M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.showResourceByPath" commandName="Show Resource in Navigator" description="Show a resource in the Navigator given its path" category="_wt_hhc_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuB90c_kEe-m5_Ju82ZM_w" elementId="resourcePath" name="Resource Path" typeId="org.eclipse.ui.ide.resourcePath" optional="false"/>
  </commands>
  <commands xmi:id="_wuB90s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.SkipAllBreakpoints" commandName="Skip All Breakpoints" description="Sets whether or not any breakpoint should suspend execution" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB908_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.delimiter.windows" commandName="Convert Line Delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" description="Converts the line delimiters to Windows (CRLF, \r\n, 0D0A, &#xa4;&#xb6;)" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB91M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.createSession" commandName="Create Session..." description="Create a Trace Session" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB91c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.findNext" commandName="Find Next" description="Find next item" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB91s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.hidetrimbars" commandName="Toggle visibility of the window toolbars" description="Toggle the visibility of the toolbars of the current window" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB918_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.buildAutomatically" commandName="Build Automatically" description="Toggle the workspace build automatically function" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB92M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.edit.text.makefile.comment" commandName="Comment" description="Turn the selected lines into # style comments" category="_wt_hks_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB92c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.import" commandName="Import" description="Import" category="_wt_hhs_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuB92s_kEe-m5_Ju82ZM_w" elementId="importWizardId" name="Import Wizard"/>
  </commands>
  <commands xmi:id="_wuB928_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.Merge" commandName="Merge" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB93M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.switchToEditor" commandName="Switch to Editor" description="Switch to an editor" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB93c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.dynamicHelp" commandName="Show Contextual Help" description="Open the contextual help" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB93s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.nextSubTab" commandName="Next Sub-Tab" description="Switch to the next sub-tab" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB938_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.member" commandName="Go to Next Member" description="Move the caret to the next member of the translation unit" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB94M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CreateTag" commandName="Create Tag..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB94c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.addTask" commandName="Add Task..." description="Add a task" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB94s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.toggleInsertMode" commandName="Toggle Insert Mode" description="Toggle insert mode" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB948_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.cheatsheets.openCheatSheet" commandName="Open Cheat Sheet" description="Open a Cheat Sheet." category="_wt_hi8_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuB95M_kEe-m5_Ju82ZM_w" elementId="cheatSheetId" name="Identifier"/>
  </commands>
  <commands xmi:id="_wuB95c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewDelete" commandName="Delete Repository" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB95s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.closePart" commandName="Close Part" description="Close the active workbench part" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB958_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editors.revisions.id.toggle" commandName="Toggle Revision Id Display" description="Toggles the display of the revision id" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB96M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.cleanAction" commandName="Build Clean" description="Discard old built state" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB96c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.moveLineDown" commandName="Move Lines Down" description="Moves the selected lines down" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB96s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.back" commandName="Back" description="Navigate back" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB968_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.wordNext" commandName="Select Next Word" description="Select the next word" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB97M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.equinox.p2.ui.sdk.update" commandName="Check for Updates" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB97c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.pageDown" commandName="Page Down" description="Go down one page" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB97s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.textEnd" commandName="Text End" description="Go to the end of the text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB978_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractConstant" commandName="Extract Constant..." category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB98M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.part.previousPage" commandName="Previous Page" description="Switch to the previous page" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB98c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.GarbageCollect" commandName="Collect Garbage" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB98s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.open.quick.type.hierarchy" commandName="Quick Type Hierarchy" description="Shows quick type hierarchy" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB988_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CompareWithWorkingTree" commandName="Compare with Working Directory" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB99M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.hover.backwardMacroExpansion" commandName="Back" description="Steps backward in macro expansions" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB99c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Branch" commandName="Branch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB99s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.synchronize_traces" commandName="Synchronize Traces" description="Synchronize 2 or more traces" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB998_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.loadAllSymbols" commandName="Load Symbols For All" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9-M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.closeProject" commandName="Close Project" description="Close the selected project" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9-c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.indent" commandName="Indent Line" description="Indents the current line" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9-s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigurePush" commandName="Configure Push..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9-8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.menu.createParserLog" commandName="Create Parser Log File" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9_M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.findIncrementalReverse" commandName="Incremental Find Reverse" description="Incremental find reverse" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9_c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.stash.apply" commandName="Apply Stashed Changes" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9_s_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.commands.progflash" commandName="Program Flash Memory" description="Program Flash Memory" category="_wt_hoc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB9_8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.next.bookmark" commandName="Next Bookmark" description="Goes to the next bookmark of the selected file" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-AM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.debugNewExecutable" commandName="Debug New Executable" description="Debug a new executable" category="_wt_hj8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ac_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.CherryPick" commandName="Cherry Pick" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-As_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.profile.ui.commands.configureApm" commandName="Configure Performance Analysis" category="_wt_hoc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-A8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.addRegisterGroup" commandName="Add RegisterGroup" description="Adds a Register Group" category="_wt_hgs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-BM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.resumeWithoutSignal" commandName="Resume Without Signal" description="Resume Without Signal" category="_wt_hg8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Bc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.gdb.ui.command.selectNextTraceRecord" commandName="Next Trace Record" description="Select Next Trace Record" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Bs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.performTextSearchFile" commandName="Find Text in File" description="Searches the files in the file for specific text." category="_wt_hlM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-B8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.columnNext" commandName="Next Column" description="Go to the next column" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-CM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.utils.enablesurvey" commandName="Enable User Survey" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Cc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.Squash" commandName="Squash Commits" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Cs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.performTextSearchWorkingSet" commandName="Find Text in Working Set" description="Searches the files in the working set for specific text." category="_wt_hlM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-C8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.add.include" commandName="Add Include" description="Create include statement on selection" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-DM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.wordNext" commandName="Next Word" description="Go to the next word" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Dc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.cut.line" commandName="Cut Line" description="Cut a line of text, or multiple lines when invoked again without interruption" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ds_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.folding.expand_all" commandName="Expand All" description="Expands all folded regions" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-D8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.quickdiff.toggle" commandName="Quick Diff Toggle" description="Toggles quick diff information display on the line number ruler" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-EM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.deleteNext" commandName="Delete Next" description="Delete the next character" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ec_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.select_traces" commandName="Select Traces..." description="Select Traces" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Es_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.FetchGerritChange" commandName="Fetch From Gerrit" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-E8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.cdt.debug.ui.command.breakpointCategoryProperties" commandName="Breakpoint Scope Properties" description="Edits the breakpoint scope settings of the given group of breakpoints. " category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-FM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.quickStartAction" commandName="Welcome" description="Show help for beginning users" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Fc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.hover.forwardMacroExpansion" commandName="Forward" description="Steps forward in macro expansions" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Fs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.Reword" commandName="Reword Commit" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-F8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.hideShowEditors" commandName="Toggle Shared Area Visibility" description="Toggles the visibility of the shared area" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-GM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.OpenCommit" commandName="Open Git Commit" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Gc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.displayHelp" commandName="Display Help" description="Display a Help topic" category="_wt_hi8_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuB-Gs_kEe-m5_Ju82ZM_w" elementId="href" name="Help topic href"/>
  </commands>
  <commands xmi:id="_wuB-G8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.folding.restore" commandName="Reset Structure" description="Resets the folding structure" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-HM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.pageDown" commandName="Select Page Down" description="Select to the bottom of the page" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Hc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.join.lines" commandName="Join Lines" description="Join lines of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Hs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.ui.addRegistersExpression" commandName="Add Expression Group > Registers" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-H8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.help.ui.indexcommand" commandName="Index" description="Show Keyword Index" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-IM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.commands.viewQEMUConsole" commandName="QEMU Console" description="View QEMU Console" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ic_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.showContextMenu" commandName="Show Context Menu" description="Show the context menu" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Is_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.restoreRegisterGroups" commandName="Restore Default Register Groups" description="Restores the Default Register Groups" category="_wt_hgs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-I8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disableEvent" commandName="Disable Event" description="Disable Event" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-JM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.destroySession" commandName="Destroy Session..." description="Destroy a Trace Session" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Jc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.forward" commandName="Forward" description="Navigate forward" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Js_kEe-m5_Ju82ZM_w" elementId="org.eclipse.jdt.ui.edit.text.java.correction.assist.proposals" commandName="Quick Fix" description="Suggest possible fixes for a problem" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-J8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.nextPerspective" commandName="Next Perspective" description="Switch to the next perspective" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-KM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.open.call.hierarchy" commandName="Open Call Hierarchy" description="Opens the call hierarchy for the selected element" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Kc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.StashApply" commandName="Apply Stashed Changes" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ks_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.DeleteTag" commandName="&amp;Delete Tag" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-K8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.ui.addLocalsExpression" commandName="Add Expression Group > Local Variables" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-LM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.ShowVersions" commandName="Open" category="_wt_hns_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuB-Lc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CompareMode" name="Compare mode"/>
  </commands>
  <commands xmi:id="_wuB-Ls_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.pinEditor" commandName="Pin Editor" description="Pin the current editor" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-L8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.nextTab" commandName="Next Tab" description="Switch to the next tab" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-MM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.quickAccess" commandName="Quick Access" description="Quickly access UI elements" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Mc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.showInformation" commandName="Show Tooltip Description" description="Displays information for the current caret location in a focused hover" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ms_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoPC" commandName="Go to Program Counter" description="Navigate to current program counter" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-M8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.pageUp" commandName="Page Up" description="Go up one page" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-NM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.submodule.sync" commandName="Sync Submodule" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Nc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.DeleteBranch" commandName="Delete Branch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ns_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.copy" commandName="Copy" category="_wt_hkM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-N8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.columnPrevious" commandName="Previous Column" description="Go to the previous column" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-OM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactor.getters.and.setters" commandName="Generate Getters and Setters..." description="Generates getters and setters for a selected field" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Oc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.toggleMemoryMonitorsPane" commandName="Toggle Memory Monitors Pane" description="Toggle visibility of the Memory Monitors Pane" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Os_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.ConfigureUpstreamFetch" commandName="Configure Upstream Fetch" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-O8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Discard" commandName="Replace with File in Index" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-PM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.selectNextChange" commandName="Select Next Change" description="Select Next Change" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Pc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewCreateBranch" commandName="Create Branch..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-Ps_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewCopyPath" commandName="Copy Path to Clipboard" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-P8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ltk.ui.refactoring.commands.renameResource" commandName="Rename Resource" description="Rename the selected resource and notify LTK participants." category="_wt_hm8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuB-QM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.open.quick.macro.explorer" commandName="Explore Macro Expansion" description="Opens a quick view for macro expansion exploration" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk0M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Rebase" commandName="Rebase on" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk0c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.ShowInHistory" commandName="Show in History" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk0s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.properties" commandName="Properties" description="Display the properties of the selected item" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk08_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnDomain" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk1M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.showRulerAnnotationInformation" commandName="Show Ruler Annotation Tooltip" description="Displays annotation information for the caret line in a focused hover" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk1c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ReplaceWithHead" commandName="Replace with HEAD revision" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk1s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactor.toggle.function" commandName="Toggle Function - Refactoring " description="Toggles the implementation between header and implementation file" category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk18_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.browser.openBrowser" commandName="Open Browser" description="Opens the default web browser." category="_wt_hic_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuCk2M_kEe-m5_Ju82ZM_w" elementId="url" name="URL"/>
    <parameters xmi:id="_wuCk2c_kEe-m5_Ju82ZM_w" elementId="browserId" name="Browser Id"/>
    <parameters xmi:id="_wuCk2s_kEe-m5_Ju82ZM_w" elementId="name" name="Browser Name"/>
    <parameters xmi:id="_wuCk28_kEe-m5_Ju82ZM_w" elementId="tooltip" name="Browser Tooltip"/>
  </commands>
  <commands xmi:id="_wuCk3M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.activeContextInfo" commandName="Show activeContext Info" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk3c_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.xlcm.commands.acquire" commandName="Acquire a License Key..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk3s_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tools.command.dumpmem" commandName="Dump/Restore Data File" description="Dump/Restore Data File" category="_wt_hoc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk38_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.textStart" commandName="Select Text Start" description="Select to the beginning of the text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk4M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.DeleteBranch" commandName="Delete Branch..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk4c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.navigate.opentype" commandName="Open Element" description="Open an element in an Editor" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk4s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.contentAssist.contextInformation" commandName="Context Information" description="Show Context Information" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk48_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.saveAs" commandName="Save As" description="Save the current contents to another location" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk5M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ConfigurePush" commandName="Configure Upstream Push" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk5c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.reverseResume" commandName="Reverse Resume" description="Perform Reverse Resume" category="_wt_hnc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk5s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.previousPerspective" commandName="Previous Perspective" description="Switch to the previous perspective" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk58_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.splitEditor" commandName="Toggle Split Editor" description="Split or join the currently active editor." category="_wt_hic_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuCk6M_kEe-m5_Ju82ZM_w" elementId="Splitter.isHorizontal" name="Orientation" optional="false"/>
  </commands>
  <commands xmi:id="_wuCk6c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.menu.freshenAllFiles" commandName="Freshen All Files in Index" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk6s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.collapseAll" commandName="Collapse All" description="Collapse the current tree" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk68_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.copyAllRightToLeft" commandName="Copy All from Right to Left" description="Copy All Changes from Right to Left" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk7M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.command.nextpage" commandName="Next Page of Memory" description="Load next page of memory" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk7c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.calibrate" commandName="Calibrate" description="Quantify LTTng overhead" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk7s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.command.gotoaddress" commandName="Go to Address" description="Go to Address" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk78_kEe-m5_Ju82ZM_w" elementId="org.eclipse.remote.ui.command.newConnection" commandName="New Connection" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk8M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.maximizePart" commandName="Maximize Active View or Editor" description="Toggles maximize/restore state of active view or editor" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk8c_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarestate" commandName="Enable Linux OS Awareness" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk8s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.menu.updateWithModifiedFiles" commandName="Update Index with Modified Files" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk88_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.lockToolBar" commandName="Lock the Toolbars" description="Lock the Toolbars" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk9M_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.hw.commands.changehwspec" commandName="Change Hardware Platform Specification" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk9c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.find.word" commandName="Find Word" description="Selects a word and find the next occurrence" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk9s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.exportToText" commandName="Export to Text..." description="Export trace to text..." category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk98_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.newEditor" commandName="New Editor" description="Open another editor on the active editor's input" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk-M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.Disconnect" commandName="Disconnect" description="Disconnect" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk-c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.GoToMessage" commandName="Go to associated message" description="Go to the associated message" category="_wt_hmM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk-s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.openSearchDialog" commandName="Open Search Dialog" description="Open the Search dialog" category="_wt_hlM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk-8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.contentAssist.proposals" commandName="Content Assist" description="Content Assist" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk_M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.previous" commandName="Select Previous C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk_c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.up" commandName="Up" description="Navigate up one level" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk_s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.activateEditor" commandName="Activate Editor" description="Activate the editor" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCk_8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.removeFromWorkingSet" commandName="Remove From Working Set" description="Removes the selected object from a working set." category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClAM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.delete.line.to.end" commandName="Delete to End of Line" description="Delete to the end of a line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClAc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.CreateBranch" commandName="Create Branch..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClAs_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.profile.ui.commands.exportascsv" commandName="Export As CSV" category="_wt_hoc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClA8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesLinkWithSelection" commandName="Link with Selection" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClBM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.Terminate" commandName="Terminate" description="Terminate" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClBc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ShowRepositoriesView" commandName="Show Git Repositories View" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClBs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.help.ui.ignoreMissingPlaceholders" commandName="Do not warn of missing documentation" description="Sets the help preferences to no longer report a warning about the current set of missing documents." category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClB8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.deleteCompleted" commandName="Delete Completed Tasks" description="Delete the tasks marked as completed" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClCM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.utils.survey.commands.popupsurvey" commandName="PopupSurvey" category="_wt_hgc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClCc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.compareWithOther" commandName="Compare With Other Resource" description="Compare resources, clipboard contents or editors" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClCs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.rename.element" commandName="Rename - Refactoring " description="Renames the selected element" category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClC8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.managedbuilder.ui.cleanFiles" commandName="Clean Selected File(s)" description="Deletes build output files for the selected source files" category="_wt_ho8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClDM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.CreatePatch" commandName="Create Patch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClDc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editors.revisions.author.toggle" commandName="Toggle Revision Author Display" description="Toggles the display of the revision author" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClDs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.windowEnd" commandName="Window End" description="Go to the end of the window" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClD8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnSession" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClEM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.prev.member" commandName="Go to Previous Member" description="Move the caret to the previous member of the translation unit" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClEc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RebaseInteractiveCurrent" commandName="%RebaseInteractiveCurrentHandler.name" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClEs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.perspectives.showPerspective" commandName="Show Perspective" description="Show a particular perspective" category="_wt_hms_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuClE8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.perspectives.showPerspective.perspectiveId" name="Parameter"/>
    <parameters xmi:id="_wuClFM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.perspectives.showPerspective.newWindow" name="In New Window"/>
  </commands>
  <commands xmi:id="_wuClFc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeEnd" commandName="Show node end" description="Show the node end" category="_wt_hmM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClFs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.openEditorDropDown" commandName="Quick Switch Editor" description="Open the editor drop down list" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClF8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.importtracepkg" commandName="Import Trace Package..." category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClGM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.line" commandName="Go to Line" description="Go to a specified line of text" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClGc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editors.quickdiff.revert" commandName="Revert Lines" description="Revert the current selection, block or deleted lines" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClGs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.deleteNextWord" commandName="Delete Next Word" description="Delete the next word" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClG8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.analysis.xml.ui.importxml" commandName="Import XML analysis" description="Import an XML file containing analysis information" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClHM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.refreshAll" commandName="Refresh Debug Views" description="Refresh all data in debug views" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClHc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.lineUp" commandName="Select Line Up" description="Extend the selection to the previous line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClHs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.SkipRebase" commandName="Skip commit and continue" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClH8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.SetQuickdiffBaseline" commandName="Set quickdiff baseline" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClIM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.undo" commandName="Undo" description="Undo the last operation" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClIc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.submodule.add" commandName="Add Submodule" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClIs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.disconnect" commandName="Disconnect" description="Disconnect to Target Node" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClI8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.managecustomparsers" commandName="Manage Custom Parsers..." description="Manage Custom Parsers" category="_wt_hn8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClJM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.cut.line.to.end" commandName="Cut to End of Line" description="Cut to the end of a line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClJc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.copyLeftToRight" commandName="Copy from Left to Right" description="Copy Current Change from Left to Right" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClJs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.newQuickMenu" commandName="New menu" description="Open the New menu" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClJ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.openProject" commandName="Open Project" description="Open a project" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClKM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.actions.WatchCommand" commandName="Watch" description="Create a watch expression from the current selection and add it to the Expressions view" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClKc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.openWorkspace" commandName="Switch Workspace" description="Open the workspace selection dialog" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClKs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.closeAll" commandName="Close All" description="Close all editors" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClK8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.cut" commandName="Cut" description="Cut the selection to the clipboard" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClLM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.ShowBlame" commandName="Show Annotations" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClLc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.org.eclipse.egit.ui.AbortRebase" commandName="Abort Rebase" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClLs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.addContextOnEvent" commandName="Add Context..." description="Add Context to Channel(s) and/or Event(s)" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClL8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.xbash.command" commandName="Launch Bash Shell" description="Launch Bash Shell" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClMM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Merge" commandName="Merge" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClMc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ReplaceWithRef" commandName="Replace with branch, tag, or reference" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClMs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.properties" commandName="Properties" description="Display the properties of the selected item's project " category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClM8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.toggleMarkOccurrences" commandName="Toggle Mark Occurrences" description="Toggles mark occurrences in C/C++ editors" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClNM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.surround.with.quickMenu" commandName="Surround With Quick Menu" description="Shows the Surround With quick menu" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClNc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CompareVersionsInTree" commandName="Compare in Tree" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClNs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.columnPrevious" commandName="Select Previous Column" description="Select the previous column" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClN8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.toggleShowSelectedElementOnly" commandName="Show Selected Element Only" description="Show Selected Element Only" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClOM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.uncomment" commandName="Uncomment" description="Uncomments the selected // style comment lines" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClOc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEventOnDomain" commandName="Enable Event (default channel)..." description="Enable Event on Default Channel" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClOs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.debug.ui.commands.toggleQualifiedTypeNames" commandName="Show Qualified Type Names" category="_wt_hoM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClO8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.export" commandName="Export" description="Export" category="_wt_hhs_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuClPM_kEe-m5_Ju82ZM_w" elementId="exportWizardId" name="Export Wizard"/>
  </commands>
  <commands xmi:id="_wuClPc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.cdt.ui.add_watchpoint" commandName="Add Watchpoint (C/C++)" description="Allows to add a new watchpoint on an arbitrary symbol" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClPs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchHierarchy" commandName="Toggle Branch Representation" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClP8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ApplyPatch" commandName="Apply Patch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClQM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ltk.ui.refactoring.commands.deleteResources" commandName="Delete Resources" description="Delete the selected resources and notify LTK participants." category="_wt_hm8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClQc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.print" commandName="Print" description="Print" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClQs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.targetBuildCommand" commandName="Make Target Build" description="Invoke a make target build for the selected container." category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClQ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.deleteConfigsCommand" commandName="Reset to Default" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClRM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.organize.includes" commandName="Organize Includes" description="Evaluates all required includes and replaces the current includes" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClRc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.showRulerContextMenu" commandName="Show Ruler Context Menu" description="Show the context menu for the ruler" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClRs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.copyConfigCommand" commandName="Copy Configuration Data To Clipboard" description="Copies the configuration data (system properties, installed bundles, etc) to the clipboard." category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClR8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.folding.collapse" commandName="Collapse" description="Collapses the folded region at the current selection" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClSM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.smartEnterInverse" commandName="Insert Line Above Current Line" description="Adds a new line above the current line" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClSc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.forwardHistory" commandName="Forward History" description="Move forward in the editor navigation history" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClSs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.spy" commandName="Show Contributing Plug-in" description="Shows contribution information for the currently selected element" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClS8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.helpSearch" commandName="Help Search" description="Open the help search" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClTM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDUp" commandName="Scroll up" description="Scroll up the sequence diagram" category="_wt_hmM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClTc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.performTextSearchProject" commandName="Find Text in Project" description="Searches the files in the project for specific text." category="_wt_hlM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClTs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.lineDown" commandName="Line Down" description="Go down one line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClT8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.rebuildAll" commandName="Rebuild All" description="Rebuild all projects" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClUM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.Checkout" commandName="Checkout" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClUc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.gotoLastEditPosition" commandName="Last Edit Location" description="Last edit location" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClUs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.open.hyperlink" commandName="Open Hyperlink" description="Opens the hyperlink at the caret location or opens a chooser if more than one hyperlink is available" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClU8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.command.prevpage" commandName="Previous Page of Memory" description="Load previous page of memory" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClVM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.openLocalFile" commandName="Open File..." description="Open a file" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClVc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Disconnect" commandName="Disconnect" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClVs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.configureFilters" commandName="Configure Contents..." description="Configure the filters to apply to the markers view" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClV8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.previousTab" commandName="Previous Tab" description="Switch to the previous tab" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClWM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.dialogs.openMessageDialog" commandName="Open Message Dialog" description="Open a Message Dialog" category="_wt_hls_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuClWc_kEe-m5_Ju82ZM_w" elementId="title" name="Title"/>
    <parameters xmi:id="_wuClWs_kEe-m5_Ju82ZM_w" elementId="message" name="Message"/>
    <parameters xmi:id="_wuClW8_kEe-m5_Ju82ZM_w" elementId="imageType" name="Image Type Constant" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_wuClXM_kEe-m5_Ju82ZM_w" elementId="defaultIndex" name="Default Button Index" typeId="org.eclipse.ui.dialogs.Integer"/>
    <parameters xmi:id="_wuClXc_kEe-m5_Ju82ZM_w" elementId="buttonLabel0" name="First Button Label"/>
    <parameters xmi:id="_wuClXs_kEe-m5_Ju82ZM_w" elementId="buttonLabel1" name="Second Button Label"/>
    <parameters xmi:id="_wuClX8_kEe-m5_Ju82ZM_w" elementId="buttonLabel2" name="Third Button Label"/>
    <parameters xmi:id="_wuClYM_kEe-m5_Ju82ZM_w" elementId="buttonLabel3" name="Fourth Button Label"/>
    <parameters xmi:id="_wuClYc_kEe-m5_Ju82ZM_w" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_wuClYs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.lineEnd" commandName="Line End" description="Go to the end of the line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClY8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.refresh" commandName="Refresh" description="Refresh the selected items" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClZM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.CreateTag" commandName="Create Tag..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClZc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewChangeCredentials" commandName="Change Credentials" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClZs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.goto.matching.bracket" commandName="Go to Matching Bracket" description="Moves the cursor to the matching bracket" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClZ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.RunLast" commandName="Run" description="Launch in run mode" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClaM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.closeAllPerspectives" commandName="Close All Perspectives" description="Close all open perspectives" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClac_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.externalTools.commands.OpenExternalToolsConfigurations" commandName="External Tools..." description="Open external tools launch configuration dialog" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClas_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.targetBuildLastCommand" commandName="Rebuild Last Target" description="Rebuild the last make target for the selected container or project." category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCla8_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.sw.ui.commands.RegenBspSources" commandName="Re-generate BSP Sources" category="_wt_hgc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClbM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.sw.commands.repositories" commandName="Repositories" category="_wt_hgc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClbc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.managedbuilder.ui.convertTarget" commandName="Convert To" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClbs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannelOnDomain" commandName="Enable Channel..." description="Enable a Trace Channel" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClb8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.deletePrevious" commandName="Delete Previous" description="Delete the previous character" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClcM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.loadSymbols" commandName="Load Symbols" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClcc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.managedbuilder.ui.buildFiles" commandName="Build Selected File(s)" description="Rebuilds the selected source files" category="_wt_ho8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClcs_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawareModeSuspend" commandName="Auto refresh on suspend" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClc8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.groupDebugContexts" commandName="Group" description="Groups the selected debug contexts" category="_wt_hj8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCldM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.lineStart" commandName="Select Line Start" description="Select to the beginning of the line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCldc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.connect" commandName="Connect" description="Connect to Target Node" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClds_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commit.Reword" commandName="Reword Commit" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCld8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.CompareWithPrevious" commandName="Compare with Previous Revision" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCleM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.editors.lineNumberToggle" commandName="Show Line Numbers" description="Toggle display of line numbers" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClec_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.columnNext" commandName="Select Next Column" description="Select the next column" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCles_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.new_experiment" commandName="New..." description="Create Tracing Experiment" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCle8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.enclosing" commandName="Select Enclosing C/C++ Element" description="Expand the selection to enclosing C/C++ element" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClfM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.debug.ui.commands.refresh" commandName="Refresh" category="_wt_hoM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClfc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.sw.ui.commands.ChangeReferencedBSP" commandName="Change Referenced BSP" category="_wt_hgc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClfs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.TerminateAndRelaunch" commandName="Terminate and Relaunch" description="Terminate and Relaunch" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClf8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesToggleBranchCommit" commandName="Toggle Latest Branch Commit" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClgM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.paste" commandName="Paste" category="_wt_hkM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClgc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.PushCommit" commandName="Push Commit..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClgs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Tag" commandName="Tag" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClg8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.NoAssumeUnchanged" commandName="No Assume Unchanged" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClhM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.commands.viewMemory" commandName="View Memory" description="View variable in memory view" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClhc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.next" commandName="Select Next C/C++ Element" description="Expand the selection to next C/C++ element" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClhs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.showInSystemExplorer" commandName="Show In (System Explorer)" description="Show in system's explorer (file manager)" category="_wt_hhc_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuClh8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.showInSystemExplorer.path" name="Resource System Path Parameter"/>
  </commands>
  <commands xmi:id="_wuCliM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.lineDown" commandName="Select Line Down" description="Extend the selection to the next line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClic_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.RemoveAllBreakpoints" commandName="Remove All Breakpoints" description="Removes all breakpoints" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClis_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.resources.nested.changeProjectPresentation" commandName="P&amp;rojects Presentation" category="_wt_hns_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuCli8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigator.resources.nested.enabled" name="&amp;Hierarchical"/>
    <parameters xmi:id="_wuCljM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.commands.radioStateParameter" name="Nested Project view - Radio State" optional="false"/>
  </commands>
  <commands xmi:id="_wuCljc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.showKeyAssist" commandName="Show Key Assist" description="Show the key assist dialog" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuCljs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.lowerCase" commandName="To Lower Case" description="Changes the selection to lower case" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClj8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.CompareWithIndex" commandName="Compare with Index" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClkM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.zoomOut" commandName="Zoom Out" description="Zoom out text, decrease default font size for text editors" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuClkc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.save" commandName="Save" description="Save the current contents" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL4M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.managedbuilder.ui.cleanAllConfigurations" commandName="Clean All Configurations" category="_wt_ho8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL4c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.terminals.ui.actions.LaunchTerminalCommand" commandName="Launch Terminal " category="_wt_hiM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL4s_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tcf.debug.ui.linuxosawarecontext" commandName="Select Linux OS Aware File" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL48_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.AddToIndex" commandName="Add to Index" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL5M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.ui.applyPatch" commandName="Apply Patch..." description="Apply a patch to one or more workspace projects." category="_wt-6cM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL5c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.saveTraceData" commandName="Save Trace Data " description="Save Trace Data to File" category="_wt_hos_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL5s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.tipsAndTricksAction" commandName="Tips and Tricks" description="Open the tips and tricks help page" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL58_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.stop" commandName="Stop" description="Stop Trace Session" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL6M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.lineEnd" commandName="Select Line End" description="Select to the end of the line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL6c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.smartEnter" commandName="Insert Line Below Current Line" description="Adds a new line below the current line" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL6s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.select_trace_type" commandName="Select Trace Type..." description="Select a trace type" category="_wt_hmc_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDL68_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.bundle" name="Bundle" optional="false"/>
    <parameters xmi:id="_wuDL7M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.type" name="Trace Type" optional="false"/>
    <parameters xmi:id="_wuDL7c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.commandparameter.select_trace_type.icon" name="Icon" optional="false"/>
  </commands>
  <commands xmi:id="_wuDL7s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.lineStart" commandName="Line Start" description="Go to the start of the line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL78_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureBranch" commandName="Configure Branch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL8M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.wordPrevious" commandName="Select Previous Word" description="Select the previous word" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL8c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.Suspend" commandName="Suspend" description="Suspend" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL8s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.hippieCompletion" commandName="Word Completion" description="Context insensitive completion" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL88_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.StepIntoSelection" commandName="Step Into Selection" description="Step into the current selected statement" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL9M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.remote.ui.command.deleteConnection" commandName="Delete Connection" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL9c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.ui.synchronizeLast" commandName="Repeat last synchronization" description="Repeat the last synchronization" category="_wt-6cM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL9s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.StepOver" commandName="Step Over" description="Step over" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL98_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.reverseStepInto" commandName="Reverse Step Into" description="Perform Reverse Step Into" category="_wt_hnc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL-M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.selectPreviousChange" commandName="Select Previous Change" description="Select Previous Change" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL-c_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewConfigureGerritRemote" commandName="Gerrit Configuration..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL-s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.configureColumns" commandName="Configure Columns..." description="Configure the columns in the markers view" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL-8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ReplaceWithCommit" commandName="Replace with commit" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL_M_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.exit" commandName="Exit" description="Exit the application" category="_wt_hhs_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDL_c_kEe-m5_Ju82ZM_w" elementId="mayPrompt" name="may prompt"/>
  </commands>
  <commands xmi:id="_wuDL_s_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.CompareWithHead" commandName="Compare with HEAD Revision" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDL_8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewOpen" commandName="Open" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMAM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ShowHistory" commandName="Show in History" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMAc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.DebugLast" commandName="Debug" description="Launch in debug mode" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMAs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.assign.event" commandName="Enable Event..." description="Assign Event to Session and Channel and Enable Event " category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMA8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.SimplePush" commandName="Push to Upstream" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMBM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.previousView" commandName="Previous View" description="Switch to the previous view" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMBc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.addMemoryMonitor" commandName="Add Memory Block" description="Add memory block" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMBs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.import" commandName="Import..." description="Import Traces to LTTng Project" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMB8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.PushHeadToGerrit" commandName="Push Current Head to Gerrit" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMCM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.selectAll" commandName="Select All" description="Select all" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMCc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.open.type.hierarchy" commandName="Open Type Hierarchy" description="Open a type hierarchy on the selected element" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMCs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.removeRegisterGroups" commandName="Remove Register Groups" description="Removes one or more Register Groups" category="_wt_hgs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMC8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactor.implement.method" commandName="Implement Method - Source Generation " description="Implements a method for a selected method declaration" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMDM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.DropToFrame" commandName="Drop to Frame" description="Drop to Frame" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMDc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.add.block.comment" commandName="Add Block Comment" description="Encloses the selection with a block comment" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMDs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.delete_suppl_files" commandName="Delete Supplementary Files..." category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMD8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.ShowBlame" commandName="Show Annotations" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMEM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.folding.expand" commandName="Expand" description="Expands the folded region at the current selection" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMEc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.project.rebuildProject" commandName="Rebuild Project" description="Rebuild the selected projects" category="_wt_hjM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMEs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.castToType" commandName="Cast To Type..." category="_wt_hnM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDME8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.nextMemoryBlock" commandName="Next Memory Monitor" description="Show renderings from next memory monitor." category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMFM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.internal.reflog.OpenInCommitViewerCommand" commandName="Open in Commit Viewer" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMFc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.Fetch" commandName="Fetch" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMFs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.copyLineDown" commandName="Copy Lines" description="Duplicates the selected lines and moves the selection to the copy" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMF8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.offset_traces" commandName="Apply Time Offset..." description="Shift traces by a constant time offset" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMGM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactor.extract.local.variable" commandName="Extract Local Variable - Refactoring " description="Extracts a local variable for the selected expression" category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMGc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.comment" commandName="Comment" description="Turns the selected lines into // style comments" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMGs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.installationDialog" commandName="Installation Information" description="Open the installation dialog" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMG8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.ToggleStepFilters" commandName="Use Step Filters" description="Toggles enablement of debug step filters" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMHM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.lineUp" commandName="Line Up" description="Go up one line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMHc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.goto.windowStart" commandName="Window Start" description="Go to the start of the window" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMHs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.eof" commandName="EOF" description="Send end of file" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMH8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.managedbuilder.ui.buildAllConfigurations" commandName="Build All Configurations" category="_wt_ho8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMIM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.ShowNodeStart" commandName="Show node start " description="Show the node start" category="_wt_hmM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMIc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.showInQuickMenu" commandName="Show In..." description="Open the Show In menu" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMIs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.copyLineUp" commandName="Duplicate Lines" description="Duplicates the selected lines and leaves the selection unchanged" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMI8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.ToggleMethodBreakpoint" commandName="Toggle Method Breakpoint" description="Creates or removes a method breakpoint" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMJM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewRenameBranch" commandName="Rename Branch..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMJc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.next" commandName="Next" description="Navigate to the next item" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMJs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.AssumeUnchanged" commandName="Assume Unchanged" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMJ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.closePerspective" commandName="Close Perspective" description="Close the current perspective" category="_wt_hic_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMKM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.closePerspective.perspectiveId" name="Perspective Id"/>
  </commands>
  <commands xmi:id="_wuDMKc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.commands.programfpga2" commandName="Configure FPGA with download.bit" category="_wt_hjc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMKs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.cheatsheets.openCheatSheetURL" commandName="Open Cheat Sheet from URL" description="Open a Cheat Sheet from file at a specified URL." category="_wt_hi8_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMK8_kEe-m5_Ju82ZM_w" elementId="cheatSheetId" name="Identifier" optional="false"/>
    <parameters xmi:id="_wuDMLM_kEe-m5_Ju82ZM_w" elementId="name" name="Name" optional="false"/>
    <parameters xmi:id="_wuDMLc_kEe-m5_Ju82ZM_w" elementId="url" name="URL" optional="false"/>
  </commands>
  <commands xmi:id="_wuDMLs_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.commands.showPerspectiveCommand" commandName="Switch to C/C++ Packs Perspective" category="_wt_hhM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDML8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.snapshot" commandName="Record Snapshot" description="Record a snapshot" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMMM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.StepReturn" commandName="Step Return" description="Step return" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMMc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.browser.openBundleResource" commandName="Open Resource in Browser" description="Opens a bundle resource in the default web browser." category="_wt_hic_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMMs_kEe-m5_Ju82ZM_w" elementId="plugin" name="Plugin"/>
    <parameters xmi:id="_wuDMM8_kEe-m5_Ju82ZM_w" elementId="path" name="Path"/>
  </commands>
  <commands xmi:id="_wuDMNM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.PushBranch" commandName="Push Branch..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMNc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.search.finddecl" commandName="Declaration" description="Searches for declarations of the selected element in the workspace" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMNs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.shells.ui.actions.LaunchShellCommand" commandName="Launch Shell" category="_wt_hiM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMN8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableEvent" commandName="Enable Event" description="Enable Event" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMOM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.help.aboutAction" commandName="About" description="Open the about dialog" category="_wt_hi8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMOc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.stash.create" commandName="Stash Changes..." category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMOs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.restoreDefaultType" commandName="Restore Original Type" description="View and edit properties for a given C/C++ breakpoint" category="_wt_hnM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMO8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CheckoutCommand" commandName="Checkout" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMPM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.closeAllSaved" commandName="Close All Saved" description="Close all saved editors" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMPc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.redo" commandName="Redo" description="Redo the last operation" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMPs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.refactoring.command.ExtractLocalVariable" commandName="Extract Local Variable..." category="_wt_his_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMP8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.gotoAddress" commandName="Go to Address..." description="Navigate to address" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMQM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.selectWorkingSets" commandName="Select Working Sets" description="Select the working sets that are applicable for this window." category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMQc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.codan.commands.runCodanCommand" commandName="Run Code Analysis" category="_wt_hjs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMQs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.sort.lines" commandName="Sort Lines" description="Sort selected lines alphabetically" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMQ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.team.RemoveFromIndex" commandName="Remove from Index" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMRM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.newWizard" commandName="New" description="Open the New item wizard" category="_wt_hhs_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMRc_kEe-m5_Ju82ZM_w" elementId="newWizardId" name="New Wizard"/>
  </commands>
  <commands xmi:id="_wuDMRs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.newWindow" commandName="New Window" description="Open another window" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMR8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CherryPick" commandName="Cherry Pick" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMSM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.importer.configureProject" commandName="Configure and Detect Nested Projects..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMSc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.file.close" commandName="Close" description="Close the active editor" category="_wt_hhs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMSs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.search.finddecl.workingset" commandName="Declaration in Working Set" description="Searches for declarations of the selected element in a working set" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMS8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.CompareVersions" commandName="Compare with each other" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMTM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.excludeCommand" commandName="Exclude from Build" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMTc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.equinox.p2.ui.sdk.install" commandName="Install New Software..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMTs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.window.customizePerspective" commandName="Customize Perspective" description="Customize the current perspective" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMT8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.externaltools.ExternalToolMenuDelegateToolbar" commandName="Run Last Launched External Tool" description="Runs the last launched external Tool" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMUM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.CheckoutCommand" commandName="Checkout" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMUc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.remote.ui.command.closeConnection" commandName="Close Connection" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMUs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.moveLineUp" commandName="Move Lines Up" description="Moves the selected lines up" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMU8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.Reset" commandName="Reset..." category="_wt_hns_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMVM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.ResetMode" name="Reset mode" optional="false"/>
  </commands>
  <commands xmi:id="_wuDMVc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.previousSubTab" commandName="Previous Sub-Tab" description="Switch to the previous sub-tab" category="_wt_hhc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMVs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.newConnection" commandName="New Connection..." description="New Connection to Target Node" category="_wt_hk8_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMV8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.lttng2.control.ui.remoteServicesIdParameter" name="Remote Services ID"/>
    <parameters xmi:id="_wuDMWM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.lttng2.control.ui.connectionNameParameter" name="Connection Name"/>
  </commands>
  <commands xmi:id="_wuDMWc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.showIn" commandName="Show In" category="_wt_hhc_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMWs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.showIn.targetId" name="Show In Target Id" optional="false"/>
  </commands>
  <commands xmi:id="_wuDMW8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.command.new_folder" commandName="New Folder..." description="Create a new trace folder" category="_wt_hmc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMXM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewRemoveRemote" commandName="Delete Remote" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMXc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.dialogs.openInputDialog" commandName="Open Input Dialog" description="Open an Input Dialog" category="_wt_hls_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMXs_kEe-m5_Ju82ZM_w" elementId="title" name="Title"/>
    <parameters xmi:id="_wuDMX8_kEe-m5_Ju82ZM_w" elementId="message" name="Message"/>
    <parameters xmi:id="_wuDMYM_kEe-m5_Ju82ZM_w" elementId="initialValue" name="Initial Value"/>
    <parameters xmi:id="_wuDMYc_kEe-m5_Ju82ZM_w" elementId="cancelReturns" name="Return Value on Cancel"/>
  </commands>
  <commands xmi:id="_wuDMYs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RebaseCurrent" commandName="Rebase" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMY8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.copyRightToLeft" commandName="Copy from Right to Left" description="Copy Current Change from Right to Left" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMZM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.commands.OpenProfileConfigurations" commandName="Profile..." description="Open profile launch configuration dialog" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMZc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.markCompleted" commandName="Mark Completed" description="Mark the selected tasks as completed" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMZs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.command.startTracing" commandName="Start Tracing " description="Start Tracing Experiment" category="_wt_hos_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMZ8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.recenter" commandName="Recenter" description="Scroll cursor line to center, top and bottom" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMaM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.select.windowStart" commandName="Select Window Start" description="Select to the start of the window" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMac_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.toggle.source.header" commandName="Toggle Source/Header" description="Toggles between corresponding source and header files" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMas_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.format" commandName="Format" description="Formats Source Code" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMa8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.text.c.select.last" commandName="Restore Last C/C++ Selection" description="Restore last selection in C/C++ editor" category="_wt-6cs_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMbM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.dsf.debug.ui.disassembly.commands.rulerToggleBreakpoint" commandName="Toggle Breakpoint" description="Toggle breakpoint in disassembly ruler" category="_wt_hlc_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMbc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.history.Revert" commandName="Revert Commit" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMbs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.edit.text.scroll.lineDown" commandName="Scroll Line Down" description="Scroll down one line of text" category="_wt_hgM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMb8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.handlers.MoveSDLeft" commandName="Scroll left" description="Scroll left the sequence diagram" category="_wt_hmM_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMcM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ToggleCoolbarAction" commandName="Toggle Main Toolbar Visibility" description="Toggles the visibility of the window toolbar" category="_wt_hic_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMcc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.RepositoriesViewImportProjects" commandName="Import Projects..." description="Import or create in local Git repository" category="_wt_hl8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMcs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.navigate.openResource" commandName="Open Resource" description="Open an editor on a particular resource" category="_wt_hhc_kEe-m5_Ju82ZM_w">
    <parameters xmi:id="_wuDMc8_kEe-m5_Ju82ZM_w" elementId="filePath" name="File Path" typeId="org.eclipse.ui.ide.resourcePath"/>
  </commands>
  <commands xmi:id="_wuDMdM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.enableChannel" commandName="Enable Channel" description="Enable a Trace Channel" category="_wt_hk8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMdc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.edit.opendecl" commandName="Open Declaration" description="Opens an editor on the selected element's declaration(s)" category="_wt-6c8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wuDMds_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.copyAllLeftToRight" commandName="Copy All from Left to Right" description="Copy All Changes from Left to Right" category="_wt_hh8_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_wyQ9gM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.utils.invokesurvey" commandName="com.xilinx.sdk.utils.invokesurvey"/>
  <commands xmi:id="_w3QLMM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ResumeAtLine" commandName="Resume at Line (C/C++)" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3QyQM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.MoveToLine" commandName="Move to Line (C/C++)" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3RZUM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugActionSet/org.eclipse.cdt.debug.ui.actions.ToggleInstructionStepMode" commandName="Instruction Stepping Mode" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3RZUc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.updateActionSet/org.eclipse.cdt.make.ui.UpdateMakeAction" commandName="Update Old Make Project..." description="Update Old Make Project" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3SncM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.actions.buildLastTargetAction" commandName="Rebuild Last Target" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Sncc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.make.ui.makeTargetActionSet/org.eclipse.cdt.make.ui.makeTargetAction" commandName="Build..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Sncs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ltk.ui.refactor.show.refactoring.history" commandName="History..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Snc8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ltk.ui.refactor.create.refactoring.script" commandName="Create Script..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3SndM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ltk.ui.refactor.apply.refactoring.script" commandName="Apply Script..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3TOgM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.SearchActionSet/org.eclipse.cdt.ui.actions.OpenCSearchPage" commandName="C/C++..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3TOgc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildActiveConfigToolbarAction" commandName="Build Active Configuration" description="Build the active configurations of selected projects" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3TOgs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigToolbarAction" commandName="Active Build Configuration" description="Manage configurations for the current project" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3TOg8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.manageConfigsAction2" commandName="Manage..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3TOhM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.buildConfigMenuAction" commandName="Set Active" description="Change active build configuration for project(s)" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3T1kM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.buildConfigActionSet/org.eclipse.cdt.ui.wsselection" commandName="Manage Working Sets..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3T1kc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewTypeDropDown" commandName="Class..." description="New C++ Class" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3T1ks_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFileDropDown" commandName="Source File..." description="New C/C++ Source File" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3T1k8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewFolderDropDown" commandName="Source Folder..." description="New C/C++ Source Folder" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3T1lM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.CElementCreationActionSet/org.eclipse.cdt.ui.actions.NewProjectDropDown" commandName="Project..." description="New C/C++ Project" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3UcoM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunWithConfigurationAction" commandName="Run As" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Ucoc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunHistoryMenuAction" commandName="Run History" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Ucos_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.RunDropDownAction" commandName="Run" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Uco8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugWithConfigurationAction" commandName="Debug As" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3VDsM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugHistoryMenuAction" commandName="Debug History" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3VDsc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.launchActionSet/org.eclipse.debug.internal.ui.actions.DebugDropDownAction" commandName="Debug" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3VDss_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileDropDownAction" commandName="Profile" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3VDs8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileWithConfigurationAction" commandName="Profile As" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3VDtM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.profileActionSet/org.eclipse.debug.internal.ui.actions.ProfileHistoryMenuAction" commandName="Profile History" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3VqwM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.ui.cheatsheets.actionSet/org.eclipse.ui.cheatsheets.actions.CheatSheetHelpMenuAction" commandName="Cheat Sheets..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Vqwc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.rse.core.search.searchActionSet/org.eclipse.rse.core.search.searchAction" commandName="Remote..." description="Opens Remote Search dialog page for text and file searching on remote systems" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Vqws_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.search.searchActionSet/org.eclipse.search.OpenSearchDialogPage" commandName="Search..." description="Search" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Vqw8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.synchronizeAll" commandName="Synchronize..." description="Synchronize..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3VqxM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.team.ui.actionSet/org.eclipse.team.ui.ConfigureProject" commandName="Share Project..." description="Share the project with others using a version and configuration management system." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3WR0M_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.ui.externaltools.ExternalToolsSet/org.eclipse.ui.externaltools.ExternalToolMenuDelegateMenu" commandName="External Tools" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3W44M_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.CEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3W44c_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.ui.editor.asm.AsmEditor.BreakpointRulerActions/org.eclipse.cdt.debug.ui.CEditor.RulerTobbleBreakpointAction" commandName="%Dummy.label" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3W44s_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.BookmarkRulerAction" commandName="dummy" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3W448_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.cdt.internal.ui.text.correction.CSelectRulerAction" commandName="dummy" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3W45M_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.ui.texteditor.ruler.actions/org.eclipse.ui.texteditor.SelectRulerAction" commandName="Text Editor Ruler Single-Click" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3W45c_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::com.xilinx.sdk.targetmanager.ui.viewContribution.restartXsdb/com.xilinx.sdk.targetmanager.ui.xsdb.ClearXSDBConsoleActionDelegate" commandName="Clear XSCT Console" description="Clear XSCT Console" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Xf8M_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::com.xilinx.sdk.targetmanager.ui.viewContribution.clearQEMUConsole/com.xilinx.sdk.targetmanager.ui.qemu.ClearQEMUConsoleActionDelegate" commandName="Clear QEMU Console" description="Clear QEMU Console" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Xf8c_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::com.xilinx.sdk.ui.filter/com.xilinx.sdk.ui.filter.action" commandName="Enable Filtering" description="Enable filtering functionality for project explorer view." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Xf8s_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugview.toolbar/org.eclipse.cdt.debug.internal.ui.actions.ToggleInstructionStepModeActionDelegate" commandName="Instruction Stepping Mode" description="Instruction Stepping Mode" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Xf88_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.debugView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Xf9M_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.ShowFullPathsAction" commandName="Show Full Paths" description="Show Full Paths" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Xf9c_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addWatchpoint" commandName="Add Watchpoint (C/C++)..." description="Add Watchpoint (C/C++)" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3Xf9s_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.internal.ui.actions.AddEventBreakpointActionDelegate" commandName="Add Event Breakpoint (C/C++)..." description="Add Event Breakpoint (C/C++)" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHAM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addFunctionBreakpoint" commandName="Add Function Breakpoint (C/C++)..." description="Add Function Breakpoint (C/C++)" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHAc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.breakpointView.menu/org.eclipse.cdt.debug.ui.addLineBreakpoint" commandName="Add Line Breakpoint (C/C++)..." description="Add Line Breakpoint (C/C++)" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHAs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveAllGlobalsActionDelegate" commandName="Remove All Global Variables" description="Remove All Global Variables" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHA8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.RemoveGlobalsActionDelegate" commandName="Remove Global Variables" description="Remove Selected Global Variables" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHBM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.variablesView.toolbar/org.eclipse.cdt.debug.internal.ui.actions.AddGlobalsActionDelegate" commandName="Add Global Variables..." description="Add Global Variables" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHBc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.pinDebugContext" commandName="Pin to Debug Context" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHBs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.expression.toolbar/org.eclipse.pinclone.expression.clone" commandName="Open New View" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHB8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.pinDebugContext" commandName="Pin to Debug Context" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHCM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.variable.toolbar/org.eclipse.pinclone.variable.clone" commandName="Open New View" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHCc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.pinDebugContext" commandName="Pin to Debug Context" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHCs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.register.toolbar/org.eclipse.pinclone.register.clone" commandName="Open New View" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHC8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.variables.update.Refresh/org.eclipse.cdt.dsf.debug.ui.variables.viewmodel.update.actions.refresh" commandName="Refresh" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHDM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.registers.update.Refresh/org.eclipse.cdt.dsf.debug.ui.registers.viewmodel.update.actions.refresh" commandName="Refresh" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHDc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.expressions.update.Refresh/org.eclipse.cdt.dsf.debug.ui.expressions.viewmodel.update.actions.refresh" commandName="Refresh" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHDs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.dsf.debug.ui.viewmodel.debugview.update.Refresh/org.eclipse.cdt.dsf.debug.ui.debugview.viewmodel.update.actions.refresh" commandName="Refresh" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YHD8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.pinDebugContext" commandName="Pin to Debug Context" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuEM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.cdt.debug.ui.disassembly.toolbar/org.eclipse.pinclone.disassembly.clone" commandName="Open New View" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuEc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.PulldownActions/org.eclipse.debug.ui.debugview.pulldown.ViewManagementAction" commandName="View Management..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuEs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.debugview.toolbar/org.eclipse.debug.ui.debugview.toolbar.removeAllTerminated" commandName="Remove All Terminated" description="Remove All Terminated Launches" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuE8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.removeAll" commandName="Remove All" description="Remove All Breakpoints" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuFM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.linkWithDebugView" commandName="Link with Debug View" description="Link with Debug View" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuFc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.workingSets" commandName="Working Sets..." description="Manage Working Sets" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuFs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.clearDefaultBreakpointGroup" commandName="Deselect Default Working Set" description="Deselect Default Working Set" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuF8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.setDefaultBreakpointGroup" commandName="Select Default Working Set..." description="Select Default Working Set" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuGM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.breakpointsview.toolbar/org.eclipse.debug.ui.breakpointsView.toolbar.groupByAction" commandName="Group By" description="Show" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuGc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.removeAll" commandName="Remove All" description="Remove All Expressions" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuGs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.expressionsView.toolbar/org.eclipse.debug.ui.expresssionsView.toolbar.AddWatchExpression" commandName="Add Watch Expression..." description="Create a new watch expression" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuG8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.PinMemoryBlockAction" commandName="Pin Memory Monitor" description="Pin Memory Monitor" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3YuHM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.NewMemoryViewAction" commandName="New Memory View" description="New Memory View" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3ZVIM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglemonitors" commandName="Toggle Memory Monitors Pane" description="Toggle Memory Monitors Pane" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3ZVIc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.linkrenderingpanes" commandName="Link Memory Rendering Panes" description="Link Memory Rendering Panes" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3ZVIs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.tablerendering.preferencesaction" commandName="Table Renderings Preferences..." description="&amp;Table Renderings Preferences..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3ZVI8_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.togglesplitpane" commandName="Toggle Split Pane" description="Toggle Split Pane" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3ZVJM_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.switchMemoryBlock" commandName="Switch Memory Monitor" description="Switch Memory Monitor" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3ZVJc_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.debug.ui.memoryView.toolbar/org.eclipse.debug.ui.memoryViewPreferencesAction" commandName="Preferences..." description="&amp;Preferences..." category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <commands xmi:id="_w3ZVJs_kEe-m5_Ju82ZM_w" elementId="AUTOGEN:::org.eclipse.rse.ui.view.systemView.toolbar/org.eclipse.rse.ui.view.systemView.toolbar.linkWithSystemView" commandName="Link with Editor" category="_wt_hns_kEe-m5_Ju82ZM_w"/>
  <addons xmi:id="_wttNqs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.core.commands.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.core.commands/org.eclipse.e4.core.commands.CommandServiceAddon"/>
  <addons xmi:id="_wttNq8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.contexts.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.services/org.eclipse.e4.ui.services.ContextServiceAddon"/>
  <addons xmi:id="_wttNrM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.bindings.service" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.bindings/org.eclipse.e4.ui.bindings.BindingServiceAddon"/>
  <addons xmi:id="_wttNrc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.workbench.commands.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.CommandProcessingAddon"/>
  <addons xmi:id="_wttNrs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.workbench.contexts.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.ContextProcessingAddon"/>
  <addons xmi:id="_wttNr8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.e4.ui.workbench.bindings.model" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.swt/org.eclipse.e4.ui.workbench.swt.util.BindingProcessingAddon"/>
  <addons xmi:id="_wttNsM_kEe-m5_Ju82ZM_w" elementId="Cleanup Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.cleanupaddon.CleanupAddon"/>
  <addons xmi:id="_wttNsc_kEe-m5_Ju82ZM_w" elementId="DnD Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.dndaddon.DnDAddon"/>
  <addons xmi:id="_wttNss_kEe-m5_Ju82ZM_w" elementId="MinMax Addon" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.minmax.MinMaxAddon">
    <persistedState key="org.eclipse.cdt.ui.CPerspective" value=""/>
  </addons>
  <addons xmi:id="_wttNs8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.workbench.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.workbench" contributionURI="bundleclass://org.eclipse.e4.ui.workbench/org.eclipse.e4.ui.internal.workbench.addons.HandlerProcessingAddon"/>
  <addons xmi:id="_wtwQ8M_kEe-m5_Ju82ZM_w" elementId="SplitterAddon" contributionURI="bundleclass://org.eclipse.e4.ui.workbench.addons.swt/org.eclipse.e4.ui.workbench.addons.splitteraddon.SplitterAddon"/>
  <addons xmi:id="_uQkSwXv8EfCBB5MBvcTLbw" elementId="org.eclipse.ui.ide.addon.0" contributionURI="bundleclass://org.eclipse.ui.ide/org.eclipse.ui.internal.ide.addons.SaveAllDirtyPartsAddon"/>
  <addons xmi:id="_dz0JgGOlEeSMMaPQU2nlzw" elementId="org.eclipse.ui.ide.application.addon.0" contributorURI="platform:/plugin/org.eclipse.ui.ide.application" contributionURI="bundleclass://org.eclipse.ui.ide.application/org.eclipse.ui.internal.ide.application.addons.ModelCleanupAddon"/>
  <addons xmi:id="_wussIM_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_TpPE8M_8Ee-nu5sqKF3pfA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_55aMkdACEe-_drItnYl1KA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_lFLqENCOEe-X5r5bwZwqnA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_WdqrMdFLEe-JsbwUrJrFGg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_GbQr0NF9Ee-_vMllcRB1zw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_zf75MdGbEe-HpuLBqvGhkQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_dgKu4NGeEe-kn98YMn3G6Q" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_LriogdITEe-ku6D6V1BoOA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_NRQrgNJHEe-dYJl_adCWqQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_rrdzAdMqEe-gm6b8eGYbkw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_K0vTsNPREe-OLrT0IDjVbQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_OhD0cdPREe-jC9KMHm28vQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_BxnSENPsEe-B0Nqrxp_NhQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_cn4tkdilEe-30qmAH786JA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_UeYEQNivEe-aEf34i9lFxw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_hOW9cdkpEe-zNOXW-u0QUQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_1S3CANkzEe-fyfOIoZh1Jw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_rG4nYem0Ee-dPKJ-7gGlmw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_rqNecOnWEe-rKLtMc2kkbQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_aByosOn2Ee-lZLHoE2mGLw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_aA48MO2YEe-ylYS69mUupg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_SQ24Ue3uEe-ISPkLqv1UxA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_ESNkAO5uEe-5dYlTjdNIsw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_-YS94e9rEe-527FDSaylRQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_ztUzgO9sEe-RD5Lx9_pogA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_HTPh0e97Ee-AdJEnJ9S_2w" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_knfj4PDMEe-S_fTPPrG8Cg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_8JUmYUOVEfCY__-X1FenMg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_agdtgEOiEfCteIPwcPgAww" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_laDpEUOpEfCOd51moUhweA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_ilCcYEOsEfCBcO7crplHSg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_W2qCUEOvEfCEZv8bQGMlPw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_aFLooEQDEfC395EaIG8Ekg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_ZxBhEUQEEfCtQZ_F4fVlvA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_ZXrQIEQIEfCijvopsiVxUA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_2czkgUQOEfCm2oz6wih43Q" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_6mTUAEQPEfCsY8a74FCvmQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_vGI8IUQSEfCzx70EH90i6w" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_Mx71oEQeEfCN_JwOQiXrVQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_Wj3lkUQlEfCqV_0fhVy36w" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_FYHpQEQ7EfCJMq01jkPyiQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_n2ax8UUWEfCg2PEfGp1Ygg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_27QiMEmMEfCPGpz72Ntytw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_6CcqsUmOEfC3E749hPjoHQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_wHkHMEmUEfCbxpbXEv99yQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_1uo1sUmaEfCLfpnQM04MTw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_WSCMcEm8EfCkF9wRlDa3Ng" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_NqofgUnBEfCOqawudMx6Og" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_SmVLMEnGEfC8OcvF0-otwQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_v8jeUUnGEfCwd_MS-O4oPw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_04Op8EnNEfC776xm9qMsmg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_zOi1sUnVEfCEhJPkiTAvJg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_X6xksEnkEfCml84SN1wXgg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_8Uqu4UnkEfCJcOtG0hIZWQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_4MgNYEnpEfCCfZeCScIWmg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_Loc04U5tEfCpl6QAL2XbcA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_KYelUE5uEfCSEpaCpcgWMA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_SgG28U5yEfC2vrLOvBJcPw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_9NIRcGINEfCyEt1aHcxP1Q" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_VCSWcWITEfChDPJHb5VdNg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_rTKPIGIhEfCOuMiukII0pg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_zF_c0WIiEfCiy83e482NmQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_hOZ8oGYGEfCDsNHq4QLEHQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_oBJ4kXDXEfCuF8M9o8LdDA" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_UcOfYHDiEfCjhO_rAXqvzQ" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_UvvkYXD6EfCYfbC7r1Hw0Q" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_3J6VsHD-EfCoufblWuwDLw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_jYz30XEAEfC6gZ15aEOaPg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_dHMdcHZeEfCTU71qGz4d_A" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_UzjgkXaQEfCYZq_CkwmRow" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_E4M_kHgvEfCMcOYPgfensg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="__Mu9IXv1EfCs74rPJvBJZg" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <addons xmi:id="_uQuDwHv8EfCBB5MBvcTLbw" elementId="com.xilinx.sdk.targetmanager.ui.addon.view" contributionURI="bundleclass://com.xilinx.sdk.targetmanager.ui/com.xilinx.sdk.targetmanager.ui.ViewVisibilityAdapterAddon"/>
  <categories xmi:id="_wt-6cM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.team.ui.category.team" name="Team" description="Actions that apply when working with a Team"/>
  <categories xmi:id="_wt-6cc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.views" name="Views" description="Commands for opening views"/>
  <categories xmi:id="_wt-6cs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.edit" name="Edit"/>
  <categories xmi:id="_wt-6c8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.category.source" name="C/C++ Source" description="C/C++ Source Actions"/>
  <categories xmi:id="_wt-6dM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.ide.markerContents" name="Contents" description="The category for menu contents"/>
  <categories xmi:id="_wt_hgM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.textEditor" name="Text Editing" description="Text Editing Commands"/>
  <categories xmi:id="_wt_hgc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.sw.commands.category" name="Sw Commands Category"/>
  <categories xmi:id="_wt_hgs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.category.registerGrouping" name="Register Grouping commands" description="Set of commands for Register Grouping"/>
  <categories xmi:id="_wt_hg8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.category.runControl" name="Run Control Commands" description="Set of commands for Run Control"/>
  <categories xmi:id="_wt_hhM_kEe-m5_Ju82ZM_w" elementId="ilg.gnuarmeclipse.packs.commands.category" name="C/C++ Packages Category"/>
  <categories xmi:id="_wt_hhc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.navigate" name="Navigate"/>
  <categories xmi:id="_wt_hhs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.file" name="File"/>
  <categories xmi:id="_wt_hh8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.compare.ui.category.compare" name="Compare" description="Compare command category"/>
  <categories xmi:id="_wt_hiM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.rse.ui.commands.category" name="Remote Systems"/>
  <categories xmi:id="_wt_hic_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.window" name="Window"/>
  <categories xmi:id="_wt_his_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.ui.category.refactoring" name="Refactor - C++" description="C/C++ Refactorings"/>
  <categories xmi:id="_wt_hi8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.help" name="Help"/>
  <categories xmi:id="_wt_hjM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.project" name="Project"/>
  <categories xmi:id="_wt_hjc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.hw.commands.hwcategory" name="Hardware Design"/>
  <categories xmi:id="_wt_hjs_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.codan.ui.commands.category" name="Code Analysis"/>
  <categories xmi:id="_wt_hj8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.category.debugViewLayout" name="Debug View Layout Commands" description="Set of commands for controlling the Debug View Layout"/>
  <categories xmi:id="_wt_hkM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tm.terminal.category1" name="Terminal view commands" description="Terminal view commands"/>
  <categories xmi:id="_wt_hkc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.bootimage.category" name="Bootgen Category"/>
  <categories xmi:id="_wt_hks_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.make.ui.category.source" name="Makefile Source" description="Makefile Source Actions"/>
  <categories xmi:id="_wt_hk8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.internal.lttng2.ui.commands.control.category" name="LTTng Trace Control Commands" description="LTTng Trace Control Commands"/>
  <categories xmi:id="_wt_hlM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.search.ui.category.search" name="Search" description="Search command category"/>
  <categories xmi:id="_wt_hlc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.debug.ui.category.run" name="Run/Debug" description="Run/Debug command category"/>
  <categories xmi:id="_wt_hls_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.dialogs" name="Dialogs" description="Commands for opening dialogs"/>
  <categories xmi:id="_wt_hl8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.egit.ui.commandCategory" name="Git"/>
  <categories xmi:id="_wt_hmM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.views.uml2sd.category" name="UML2 Sequence Diagram Viewer Commands" description="UML2 Sequence Diagram Viewer Commands"/>
  <categories xmi:id="_wt_hmc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.commands.category" name="Tracing" description="Tracing Commands"/>
  <categories xmi:id="_wt_hms_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ui.category.perspectives" name="Perspectives" description="Commands for opening perspectives"/>
  <categories xmi:id="_wt_hm8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.ltk.ui.category.refactoring" name="Refactoring"/>
  <categories xmi:id="_wt_hnM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.category.casting" name="Cast to Type or Array" description="Set of commands for displaying variables and expressions as other types or arrays."/>
  <categories xmi:id="_wt_hnc_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.category.reverseDebugging" name="Reverse Debugging Commands" description="Set of commands for Reverse Debugging"/>
  <categories xmi:id="_wt_hns_kEe-m5_Ju82ZM_w" elementId="org.eclipse.core.commands.categories.autogenerated" name="Uncategorized" description="Commands that were either auto-generated or have no category"/>
  <categories xmi:id="_wt_hn8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.linuxtools.tmf.ui.commands.parser.category" name="Parser Commands" description="Parser Commands"/>
  <categories xmi:id="_wt_hoM_kEe-m5_Ju82ZM_w" elementId="org.eclipse.tcf.debug.ui.commands" name="TCF Debugger" description="TCF Debugger Commands"/>
  <categories xmi:id="_wt_hoc_kEe-m5_Ju82ZM_w" elementId="com.xilinx.sdk.tools.command.category" name="SDK Tools"/>
  <categories xmi:id="_wt_hos_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.debug.ui.category.tracing" name="Tracing Commands" description="Category for Tracing Commands"/>
  <categories xmi:id="_wt_ho8_kEe-m5_Ju82ZM_w" elementId="org.eclipse.cdt.managedbuilder.ui.category.build" name="C/C++ Build" description="C/C++ Build Actions"/>
</application:Application>
