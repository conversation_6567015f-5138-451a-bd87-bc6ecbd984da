<?xml version="1.0" encoding="UTF-8" ?>
<document>
<!--The data in this file is primarily intended for consumption by Xilinx tools.
The structure and the elements are likely to change over the next few releases.
This means code written to parse this file will need to be revisited each subsequent release.-->
<application name="pa" timeStamp="Mon Aug 18 17:23:36 2025">
<section name="Project Information" visible="false">
<property name="ProjectID" value="7e204b91dd4641b89a8d397181267d9f" type="ProjectID"/>
<property name="ProjectIteration" value="112" type="ProjectIteration"/>
</section>
<section name="PlanAhead Usage" visible="true">
<item name="Project Data">
<property name="SrcSetCount" value="1" type="SrcSetCount"/>
<property name="ConstraintSetCount" value="1" type="ConstraintSetCount"/>
<property name="DesignMode" value="RTL" type="DesignMode"/>
<property name="SynthesisStrategy" value="Vivado Synthesis Defaults" type="SynthesisStrategy"/>
<property name="ImplStrategy" value="Vivado Implementation Defaults" type="ImplStrategy"/>
</item>
<item name="Java Command Handlers">
<property name="AddProbesToWaveform" value="2" type="JavaHandler"/>
<property name="AddSources" value="3" type="JavaHandler"/>
<property name="AutoAssignAddress" value="7" type="JavaHandler"/>
<property name="AutoConnectPort" value="18" type="JavaHandler"/>
<property name="AutoConnectTarget" value="63" type="JavaHandler"/>
<property name="CloseServer" value="11" type="JavaHandler"/>
<property name="CloseTarget" value="4" type="JavaHandler"/>
<property name="CoreView" value="50" type="JavaHandler"/>
<property name="CreateBlockDesign" value="1" type="JavaHandler"/>
<property name="CreateTopHDL" value="6" type="JavaHandler"/>
<property name="CustomizeCore" value="43" type="JavaHandler"/>
<property name="CustomizeRSBBlock" value="233" type="JavaHandler"/>
<property name="DebugWizardCmdHandler" value="4" type="JavaHandler"/>
<property name="DisableAutoRetrigger" value="1" type="JavaHandler"/>
<property name="EditConstraintSets" value="1" type="JavaHandler"/>
<property name="EditCopy" value="4" type="JavaHandler"/>
<property name="EditDelete" value="112" type="JavaHandler"/>
<property name="EditPaste" value="6" type="JavaHandler"/>
<property name="EditProperties" value="12" type="JavaHandler"/>
<property name="EditRedo" value="14" type="JavaHandler"/>
<property name="EditUndo" value="49" type="JavaHandler"/>
<property name="EnableAutoRetrigger" value="1" type="JavaHandler"/>
<property name="ExportILAData" value="1" type="JavaHandler"/>
<property name="FileExit" value="8" type="JavaHandler"/>
<property name="GenerateOutputForBDFile" value="1" type="JavaHandler"/>
<property name="LaunchOpenTarget" value="2" type="JavaHandler"/>
<property name="LaunchProgramFpga" value="3" type="JavaHandler"/>
<property name="MakeRSBConnection" value="1" type="JavaHandler"/>
<property name="ManageCompositeTargets" value="38" type="JavaHandler"/>
<property name="NewExportHardware" value="59" type="JavaHandler"/>
<property name="NewLaunchHardware" value="104" type="JavaHandler"/>
<property name="NewProject" value="2" type="JavaHandler"/>
<property name="OpenAddressEditor" value="5" type="JavaHandler"/>
<property name="OpenBlockDesign" value="1" type="JavaHandler"/>
<property name="OpenHardwareDashboard" value="1" type="JavaHandler"/>
<property name="OpenHardwareManager" value="30" type="JavaHandler"/>
<property name="OpenProject" value="3" type="JavaHandler"/>
<property name="OpenRecentTarget" value="2" type="JavaHandler"/>
<property name="OpenTarget" value="3" type="JavaHandler"/>
<property name="RecustomizeCore" value="14" type="JavaHandler"/>
<property name="RefreshDevice" value="15" type="JavaHandler"/>
<property name="RefreshServer" value="1" type="JavaHandler"/>
<property name="RegenerateRSBLayout" value="11" type="JavaHandler"/>
<property name="ReloadDesign" value="1" type="JavaHandler"/>
<property name="ReportClockInteraction" value="1" type="JavaHandler"/>
<property name="ReportClockNetworks" value="1" type="JavaHandler"/>
<property name="ReportIPStatus" value="6" type="JavaHandler"/>
<property name="ReportMethodology" value="1" type="JavaHandler"/>
<property name="ReportTimingSummary" value="10" type="JavaHandler"/>
<property name="RunBitgen" value="70" type="JavaHandler"/>
<property name="RunImplementation" value="47" type="JavaHandler"/>
<property name="RunSchematic" value="9" type="JavaHandler"/>
<property name="RunSynthesis" value="41" type="JavaHandler"/>
<property name="RunTrigger" value="31" type="JavaHandler"/>
<property name="RunTriggerImmediate" value="13" type="JavaHandler"/>
<property name="SaveDesign" value="10" type="JavaHandler"/>
<property name="SaveFileProxyHandler" value="1" type="JavaHandler"/>
<property name="SaveRSBDesign" value="90" type="JavaHandler"/>
<property name="ShowSource" value="3" type="JavaHandler"/>
<property name="ShowView" value="64" type="JavaHandler"/>
<property name="SimulationRun" value="1" type="JavaHandler"/>
<property name="StopTrigger" value="17" type="JavaHandler"/>
<property name="TimingConstraintsWizard" value="5" type="JavaHandler"/>
<property name="ToggleAutoFitSelection" value="4" type="JavaHandler"/>
<property name="ToggleSelectAreaMode" value="7" type="JavaHandler"/>
<property name="ToolsSettings" value="8" type="JavaHandler"/>
<property name="ToolsTemplates" value="8" type="JavaHandler"/>
<property name="UnmapAddressSegment" value="4" type="JavaHandler"/>
<property name="UpgradeIP" value="13" type="JavaHandler"/>
<property name="ValidateRSBDesign" value="83" type="JavaHandler"/>
<property name="ViewLayoutCmd" value="3" type="JavaHandler"/>
<property name="ViewTaskImplementation" value="11" type="JavaHandler"/>
<property name="ViewTaskRTLAnalysis" value="2" type="JavaHandler"/>
<property name="ViewTaskSynthesis" value="4" type="JavaHandler"/>
<property name="XdcSetInputDelay" value="5" type="JavaHandler"/>
<property name="ZoomFit" value="1" type="JavaHandler"/>
<property name="ZoomIn" value="1" type="JavaHandler"/>
<property name="ZoomOut" value="1" type="JavaHandler"/>
</item>
<item name="Gui Handlers">
<property name="AbstractCombinedPanel_REMOVE_SELECTED_ELEMENTS" value="2" type="GuiHandlerData"/>
<property name="AbstractFileView_RELOAD" value="11" type="GuiHandlerData"/>
<property name="AbstractSearchablePanel_SHOW_SEARCH" value="2" type="GuiHandlerData"/>
<property name="AddIlaProbesPopup_OK" value="3" type="GuiHandlerData"/>
<property name="AddRepositoryInfoDialog_OK" value="3" type="GuiHandlerData"/>
<property name="AddRepositoryInfoDialog_REPOSITORY_TREE" value="2" type="GuiHandlerData"/>
<property name="AddSrcWizard_SPECIFY_HDL_NETLIST_BLOCK_DESIGN" value="1" type="GuiHandlerData"/>
<property name="AddSrcWizard_SPECIFY_OR_CREATE_CONSTRAINT_FILES" value="2" type="GuiHandlerData"/>
<property name="AddressTreeTablePanel_ADDRESS_TREE_TABLE" value="286" type="GuiHandlerData"/>
<property name="ApplyRSBMultiAutomationDialog_CHECKBOX_TREE" value="25" type="GuiHandlerData"/>
<property name="BaseDialogUtils_OPEN_IN_NEW_TAB" value="1" type="GuiHandlerData"/>
<property name="BaseDialogUtils_OPEN_IN_SPECIFIED_LAYOUT" value="2" type="GuiHandlerData"/>
<property name="BaseDialog_CANCEL" value="263" type="GuiHandlerData"/>
<property name="BaseDialog_CLOSE" value="7" type="GuiHandlerData"/>
<property name="BaseDialog_OK" value="619" type="GuiHandlerData"/>
<property name="BaseDialog_YES" value="49" type="GuiHandlerData"/>
<property name="BaseReportTab_RERUN" value="12" type="GuiHandlerData"/>
<property name="CheckTimingResultTreeTablePanel_CHECK_TIMING_RESULT_TREE_TABLE" value="10" type="GuiHandlerData"/>
<property name="CheckTimingSectionPanel_CHECK_TIMING_SELECTION_TABLE" value="2" type="GuiHandlerData"/>
<property name="ClkConfigMainPanel_TABBED_PANE" value="2" type="GuiHandlerData"/>
<property name="ClkConfigTreeTablePanel_CLK_CONFIG_TREE_TABLE" value="59" type="GuiHandlerData"/>
<property name="ClockNetworksReportView_CLOCK_NETWORK_TREE" value="20" type="GuiHandlerData"/>
<property name="ClockSummaryTreeTablePanel_CLOCK_SUMMARY_TREE_TABLE" value="28" type="GuiHandlerData"/>
<property name="ClosePlanner_CANCEL" value="1" type="GuiHandlerData"/>
<property name="ClosePlanner_YES" value="3" type="GuiHandlerData"/>
<property name="CmdMsgDialog_COPY_MESSAGE" value="7" type="GuiHandlerData"/>
<property name="CmdMsgDialog_MESSAGES" value="112" type="GuiHandlerData"/>
<property name="CmdMsgDialog_OK" value="142" type="GuiHandlerData"/>
<property name="CmdMsgDialog_OPEN_MESSAGES_VIEW" value="7" type="GuiHandlerData"/>
<property name="CommandsInput_TYPE_TCL_COMMAND_HERE" value="5" type="GuiHandlerData"/>
<property name="ConfirmSaveTextEditsDialog_NO" value="2" type="GuiHandlerData"/>
<property name="ConstraintsChooserPanel_ADD_EXISTING_OR_CREATE_NEW_CONSTRAINTS" value="3" type="GuiHandlerData"/>
<property name="ConstraintsChooserPanel_ADD_FILES_BELOW_TO_THIS_CONSTRAINT_SET" value="1" type="GuiHandlerData"/>
<property name="ConstraintsChooserPanel_CREATE_FILE" value="4" type="GuiHandlerData"/>
<property name="ConstraintsChooserPanel_FILE_TABLE" value="18" type="GuiHandlerData"/>
<property name="CoreAndInterfacesBaseTreeTablePanel_REFRESH_ALL_REPOSITORIES" value="2" type="GuiHandlerData"/>
<property name="CoreAndInterfacesBaseTreeTablePanel_REFRESH_REPOSITORY" value="1" type="GuiHandlerData"/>
<property name="CoreTreeTablePanel_ADD_IP_TO_REPOSITORY" value="1" type="GuiHandlerData"/>
<property name="CoreTreeTablePanel_CORE_TREE_TABLE" value="521" type="GuiHandlerData"/>
<property name="CoreTreeTablePanel_DELETE_IP" value="1" type="GuiHandlerData"/>
<property name="CreateConstraintsFilePanel_FILE_NAME" value="5" type="GuiHandlerData"/>
<property name="CreateNewDiagramDialog_DESIGN_NAME" value="5" type="GuiHandlerData"/>
<property name="CreateRSBInterfaceDialog_TABLE" value="8" type="GuiHandlerData"/>
<property name="CreateRSBPortDialog_CREATE_VECTOR" value="7" type="GuiHandlerData"/>
<property name="CreateRSBPortDialog_DIRECTION" value="15" type="GuiHandlerData"/>
<property name="CreateRSBPortDialog_FREQUENCY" value="1" type="GuiHandlerData"/>
<property name="CreateRSBPortDialog_FROM" value="7" type="GuiHandlerData"/>
<property name="CreateRSBPortDialog_PORT_NAME" value="19" type="GuiHandlerData"/>
<property name="CreateRSBPortDialog_TYPE" value="18" type="GuiHandlerData"/>
<property name="CustomizeCoreDialog_DOCUMENTATION" value="1" type="GuiHandlerData"/>
<property name="CustomizeCoreDialog_IP_LOCATION" value="2" type="GuiHandlerData"/>
<property name="DDRConfigTreeTablePanel_DDR_CONFIG_TREE_TABLE" value="15" type="GuiHandlerData"/>
<property name="DebugView_DEBUG_CORES_TREE_TABLE" value="12" type="GuiHandlerData"/>
<property name="DebugWizard_ADVANCED_TRIGGER" value="4" type="GuiHandlerData"/>
<property name="DebugWizard_CAPTURE_CONTROL" value="5" type="GuiHandlerData"/>
<property name="DebugWizard_CHIPSCOPE_TREE_TABLE" value="7" type="GuiHandlerData"/>
<property name="DebugWizard_FIND_NETS_TO_ADD" value="3" type="GuiHandlerData"/>
<property name="DebugWizard_NETLIST_VIEW" value="1" type="GuiHandlerData"/>
<property name="DebugWizard_SAMPLE_OF_DATA_DEPTH" value="2" type="GuiHandlerData"/>
<property name="DesignTimingSumSectionPanel_WORST_HOLD_SLACK" value="1" type="GuiHandlerData"/>
<property name="DesignTimingSumSectionPanel_WORST_NEGATIVE_SLACK" value="3" type="GuiHandlerData"/>
<property name="EditCreateClockTablePanel_EDIT_CREATE_CLOCK_TABLE" value="23" type="GuiHandlerData"/>
<property name="EditCreateGeneratedClockTablePanel_EDIT_CREATE_GENERATED_CLOCK_TABLE" value="1" type="GuiHandlerData"/>
<property name="EditIODelayTablePanel_EDIT_IO_DELAY_TABLE" value="16" type="GuiHandlerData"/>
<property name="ExistingConstraintsListPanel_COPY_TEXT_FROM_SELECTED_CONSTRAINTS" value="1" type="GuiHandlerData"/>
<property name="ExpReportTreePanel_EXP_REPORT_TREE_TABLE" value="19" type="GuiHandlerData"/>
<property name="ExpRunTreePanel_EXP_RUN_TREE_TABLE" value="44" type="GuiHandlerData"/>
<property name="FPGAChooser_CATEGORY" value="2" type="GuiHandlerData"/>
<property name="FPGAChooser_FAMILY" value="2" type="GuiHandlerData"/>
<property name="FPGAChooser_FPGA_TABLE" value="7" type="GuiHandlerData"/>
<property name="FPGAChooser_PACKAGE" value="1" type="GuiHandlerData"/>
<property name="FPGAChooser_SPEED" value="1" type="GuiHandlerData"/>
<property name="FileSetPanel_FILE_SET_PANEL_TREE" value="808" type="GuiHandlerData"/>
<property name="FlowNavigatorTreePanel_FLOW_NAVIGATOR_TREE" value="370" type="GuiHandlerData"/>
<property name="FlowNavigatorTreePanel_OPEN" value="2" type="GuiHandlerData"/>
<property name="FromTargetSpecifierPanel_SPECIFY_START_POINTS" value="3" type="GuiHandlerData"/>
<property name="GICTreeTablePanel_GIC_TREE_TABLE" value="19" type="GuiHandlerData"/>
<property name="GenSettingTreeTablePanel_GEN_SETTING_TREE_TABLE" value="81" type="GuiHandlerData"/>
<property name="GetObjectsDialog_FIND" value="12" type="GuiHandlerData"/>
<property name="GetObjectsPanel_APPEND" value="4" type="GuiHandlerData"/>
<property name="GetObjectsPanel_SET" value="2" type="GuiHandlerData"/>
<property name="GettingStartedView_CREATE_NEW_PROJECT" value="2" type="GuiHandlerData"/>
<property name="GettingStartedView_OPEN_PROJECT" value="3" type="GuiHandlerData"/>
<property name="GraphicalView_ZOOM_FIT" value="4" type="GuiHandlerData"/>
<property name="GraphicalView_ZOOM_OUT" value="4" type="GuiHandlerData"/>
<property name="HACGCCheckBox_VALUE_OF_SPECIFIED_PARAMETER" value="1" type="GuiHandlerData"/>
<property name="HACGCComboBox_VALUE_OF_SPECIFIED_PARAMETER" value="16" type="GuiHandlerData"/>
<property name="HACGCComboBox_VALUE_OF_SPECIFIED_PARAMETER_MANUAL" value="11" type="GuiHandlerData"/>
<property name="HACGCIPSymbol_SHOW_DISABLED_PORTS" value="11" type="GuiHandlerData"/>
<property name="HACGCTabbedPane_TABBED_PANE" value="1" type="GuiHandlerData"/>
<property name="HCodeEditor_BLANK_OPERATIONS" value="1" type="GuiHandlerData"/>
<property name="HCodeEditor_SEARCH_TEXT_COMBO_BOX" value="14" type="GuiHandlerData"/>
<property name="HDualList_FIND_RESULTS" value="21" type="GuiHandlerData"/>
<property name="HDualList_MOVE_SELECTED_ITEMS_TO_RIGHT" value="6" type="GuiHandlerData"/>
<property name="HFolderChooserHelpers_UP_ONE_LEVEL" value="4" type="GuiHandlerData"/>
<property name="HInputHandler_TOGGLE_BLOCK_COMMENTS" value="1" type="GuiHandlerData"/>
<property name="HPopupTitle_CLOSE" value="8" type="GuiHandlerData"/>
<property name="HTable_SET_ELIDING_FOR_TABLE_CELLS" value="9" type="GuiHandlerData"/>
<property name="HardwareDashboardView_SHOW_DASHBOARD_OPTIONS" value="1" type="GuiHandlerData"/>
<property name="HardwareIlaWaveformView_RUN_TRIGGER_FOR_THIS_ILA_CORE" value="21" type="GuiHandlerData"/>
<property name="HardwareIlaWaveformView_RUN_TRIGGER_IMMEDIATE_FOR_THIS_ILA_CORE" value="9" type="GuiHandlerData"/>
<property name="HardwareIlaWaveformView_STOP_TRIGGER_FOR_THIS_ILA_CORE" value="5" type="GuiHandlerData"/>
<property name="HardwareIlaWaveformView_TOGGLE_AUTO_RE_TRIGGER_MODE" value="12" type="GuiHandlerData"/>
<property name="HardwareTreePanel_HARDWARE_TREE_TABLE" value="51" type="GuiHandlerData"/>
<property name="HardwareView_EXPAND_NEXT_LEVEL" value="1" type="GuiHandlerData"/>
<property name="IODelayCreationPanel_DELAY_VALUE" value="5" type="GuiHandlerData"/>
<property name="IODelayCreationPanel_DELAY_VALUE_SPECIFIES" value="5" type="GuiHandlerData"/>
<property name="IODelayCreationPanel_DELAY_VALUE_SPECIFIES_RISE_FALL_DELAY" value="2" type="GuiHandlerData"/>
<property name="IODelayCreationPanel_SPECIFY_CLOCK_PIN_OR_PORT" value="3" type="GuiHandlerData"/>
<property name="IODelayCreationPanel_SPECIFY_LIST_OF_PORTS" value="3" type="GuiHandlerData"/>
<property name="IPIComponentName_COMPONENT_NAME" value="3" type="GuiHandlerData"/>
<property name="IPStatusSectionPanel_UPGRADE_SELECTED" value="13" type="GuiHandlerData"/>
<property name="IPStatusTablePanel_IP_STATUS_TABLE" value="31" type="GuiHandlerData"/>
<property name="IPStatusTablePanel_MORE_INFO" value="11" type="GuiHandlerData"/>
<property name="IctElementSummarySectionPanel_HOLD" value="1" type="GuiHandlerData"/>
<property name="IlaProbeTablePanel_ADD_PROBE" value="4" type="GuiHandlerData"/>
<property name="IlaProbeTablePanel_ADD_PROBES" value="1" type="GuiHandlerData"/>
<property name="IlaProbeTablePanel_SET_TRIGGER_CONDITION_TO_GLOBAL" value="1" type="GuiHandlerData"/>
<property name="InstanceMenu_FLOORPLANNING" value="2" type="GuiHandlerData"/>
<property name="IntraClocksSectionPanel_INTRA_CLOCKS_SECTION_TABLE" value="40" type="GuiHandlerData"/>
<property name="LabtoolsMenu_NAME" value="9" type="GuiHandlerData"/>
<property name="LanguageTemplatesDialog_TEMPLATES_TREE" value="174" type="GuiHandlerData"/>
<property name="LogMonitor_MONITOR" value="5" type="GuiHandlerData"/>
<property name="LogPanel_LOG_NAVIGATOR" value="4" type="GuiHandlerData"/>
<property name="MIOConfigTreeTablePanel_MIO_CONFIG_TREE_TABLE" value="89" type="GuiHandlerData"/>
<property name="MIOTablePagePanel_MIO_TABLE" value="21" type="GuiHandlerData"/>
<property name="MainMenuMgr_CHECKPOINT" value="202" type="GuiHandlerData"/>
<property name="MainMenuMgr_CONSTRAINTS" value="16" type="GuiHandlerData"/>
<property name="MainMenuMgr_EDIT" value="26" type="GuiHandlerData"/>
<property name="MainMenuMgr_EXPORT" value="278" type="GuiHandlerData"/>
<property name="MainMenuMgr_FILE" value="398" type="GuiHandlerData"/>
<property name="MainMenuMgr_FLOORPLANNING" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_FLOW" value="36" type="GuiHandlerData"/>
<property name="MainMenuMgr_HELP" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_IMPORT" value="47" type="GuiHandlerData"/>
<property name="MainMenuMgr_IO" value="4" type="GuiHandlerData"/>
<property name="MainMenuMgr_IO_PLANNING" value="2" type="GuiHandlerData"/>
<property name="MainMenuMgr_IP" value="205" type="GuiHandlerData"/>
<property name="MainMenuMgr_OPEN_BLOCK_DESIGN" value="3" type="GuiHandlerData"/>
<property name="MainMenuMgr_PROJECT" value="203" type="GuiHandlerData"/>
<property name="MainMenuMgr_REPORTS" value="46" type="GuiHandlerData"/>
<property name="MainMenuMgr_SETTINGS" value="3" type="GuiHandlerData"/>
<property name="MainMenuMgr_TEXT_EDITOR" value="208" type="GuiHandlerData"/>
<property name="MainMenuMgr_TIMING" value="5" type="GuiHandlerData"/>
<property name="MainMenuMgr_TOOLS" value="53" type="GuiHandlerData"/>
<property name="MainMenuMgr_UNSELECT_TYPE" value="1" type="GuiHandlerData"/>
<property name="MainMenuMgr_VIEW" value="32" type="GuiHandlerData"/>
<property name="MainMenuMgr_WINDOW" value="66" type="GuiHandlerData"/>
<property name="MainToolbarMgr_RUN" value="6" type="GuiHandlerData"/>
<property name="MainWinMenuMgr_LAYOUT" value="50" type="GuiHandlerData"/>
<property name="MainWinMenuMgr_LOAD" value="2" type="GuiHandlerData"/>
<property name="MessageWithOptionDialog_DONT_SHOW_THIS_DIALOG_AGAIN" value="1" type="GuiHandlerData"/>
<property name="MsgTreePanel_DISCARD_USER_CREATED_MESSAGES" value="10" type="GuiHandlerData"/>
<property name="MsgTreePanel_MESSAGE_SEVERITY" value="24" type="GuiHandlerData"/>
<property name="MsgTreePanel_MESSAGE_VIEW_TREE" value="477" type="GuiHandlerData"/>
<property name="MsgTreePanel_SUPPRESS_MESSAGES_WITH_THIS_ID" value="1" type="GuiHandlerData"/>
<property name="MsgView_CRITICAL_WARNINGS" value="1" type="GuiHandlerData"/>
<property name="NavigableTimingReportTab_TIMING_REPORT_NAVIGATION_TREE" value="284" type="GuiHandlerData"/>
<property name="NetlistTreeView_NETLIST_TREE" value="73" type="GuiHandlerData"/>
<property name="NewExportHardwareDialog_EXPORT_TO" value="4" type="GuiHandlerData"/>
<property name="NewExportHardwareDialog_INCLUDE_BITSTREAM" value="35" type="GuiHandlerData"/>
<property name="PACommandNames_ADDRESSEDITOR_WINDOW" value="5" type="GuiHandlerData"/>
<property name="PACommandNames_ADD_PROBES_TO_WAVEFORM" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_ADD_SOURCES" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_ASSIGN_ADDRESS" value="18" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_CONNECT_PORTS" value="18" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_CONNECT_TARGET" value="64" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_FIT_SELECTION" value="4" type="GuiHandlerData"/>
<property name="PACommandNames_AUTO_UPDATE_HIER" value="10" type="GuiHandlerData"/>
<property name="PACommandNames_CLOSE_SERVER" value="11" type="GuiHandlerData"/>
<property name="PACommandNames_CLOSE_TARGET" value="4" type="GuiHandlerData"/>
<property name="PACommandNames_CREATE_HARDWARE_DASHBOARDS" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_CREATE_TOP_HDL" value="7" type="GuiHandlerData"/>
<property name="PACommandNames_DISABLE_AUTO_RETRIGGER" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_EDIT_CONSTRAINT_SETS" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_ENABLE_AUTO_RETRIGGER" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_EXIT" value="8" type="GuiHandlerData"/>
<property name="PACommandNames_EXPORT_BD_TCL" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_EXPORT_HARDWARE" value="58" type="GuiHandlerData"/>
<property name="PACommandNames_EXPORT_ILA_DATA" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_GENERATE_COMPOSITE_FILE" value="30" type="GuiHandlerData"/>
<property name="PACommandNames_GOTO_IMPLEMENTED_DESIGN" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_IP_SETTINGS" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_LANGUAGE_TEMPLATES" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_LAUNCH_HARDWARE" value="106" type="GuiHandlerData"/>
<property name="PACommandNames_LOG_WINDOW" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_MAKE_CONNECTION" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_MESSAGE_WINDOW" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_OPEN_HARDWARE_MANAGER" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_OPEN_TARGET" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_OPEN_TARGET_WIZARD" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_PACKAGE_PINS_WINDOW" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_PORTS_WINDOW" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_PROGRAM_FPGA" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_REFRESH_SERVER" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_REGENERATE_LAYOUT" value="11" type="GuiHandlerData"/>
<property name="PACommandNames_RELOAD_RTL_DESIGN" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_REPORTS_WINDOW" value="5" type="GuiHandlerData"/>
<property name="PACommandNames_REPORT_CLOCK_NETWORKS" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_REPORT_IP_STATUS" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_RESET_COMPOSITE_FILE" value="7" type="GuiHandlerData"/>
<property name="PACommandNames_RUN_BITGEN" value="27" type="GuiHandlerData"/>
<property name="PACommandNames_RUN_IMPLEMENTATION" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_RUN_SYNTHESIS" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_RUN_TRIGGER" value="5" type="GuiHandlerData"/>
<property name="PACommandNames_SAVE_DESIGN" value="10" type="GuiHandlerData"/>
<property name="PACommandNames_SAVE_RSB_DESIGN" value="57" type="GuiHandlerData"/>
<property name="PACommandNames_SCHEMATIC" value="8" type="GuiHandlerData"/>
<property name="PACommandNames_SELECT_AREA" value="7" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RUN" value="3" type="GuiHandlerData"/>
<property name="PACommandNames_SIMULATION_RUN_BEHAVIORAL" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_STOP_TRIGGER" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_TCL_CONSOLE_WINDOW" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_TIMING_RESULTS_WINDOW" value="2" type="GuiHandlerData"/>
<property name="PACommandNames_TRIGGER_IMMEDIATE" value="4" type="GuiHandlerData"/>
<property name="PACommandNames_UNMAP_SEGMENT" value="4" type="GuiHandlerData"/>
<property name="PACommandNames_VALIDATE_RSB_DESIGN" value="88" type="GuiHandlerData"/>
<property name="PACommandNames_ZOOM_FIT" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_ZOOM_IN" value="1" type="GuiHandlerData"/>
<property name="PACommandNames_ZOOM_OUT" value="1" type="GuiHandlerData"/>
<property name="PAViews_ADDRESS_EDITOR" value="22" type="GuiHandlerData"/>
<property name="PAViews_CODE" value="61" type="GuiHandlerData"/>
<property name="PAViews_DASHBOARD" value="4" type="GuiHandlerData"/>
<property name="PAViews_DEVICE" value="13" type="GuiHandlerData"/>
<property name="PAViews_IP_CATALOG" value="4" type="GuiHandlerData"/>
<property name="PAViews_PATH_TABLE" value="4" type="GuiHandlerData"/>
<property name="PAViews_PROJECT_SUMMARY" value="94" type="GuiHandlerData"/>
<property name="PAViews_SCHEMATIC" value="8" type="GuiHandlerData"/>
<property name="PAViews_SYSTEM" value="8" type="GuiHandlerData"/>
<property name="PAViews_TIMING_CONSTRAINTS" value="6" type="GuiHandlerData"/>
<property name="PackageTreePanel_PACKAGE_TREE_PANEL" value="3" type="GuiHandlerData"/>
<property name="PathMenu_SET_FALSE_PATH" value="3" type="GuiHandlerData"/>
<property name="PathMenu_SET_MAXIMUM_DELAY" value="2" type="GuiHandlerData"/>
<property name="PathMenu_SET_MULTICYCLE_PATH" value="2" type="GuiHandlerData"/>
<property name="PathReportTableView_DESCRIPTION" value="48" type="GuiHandlerData"/>
<property name="PathReportTableView_FLOORPLANNING" value="5" type="GuiHandlerData"/>
<property name="PathReportTableView_SELECT" value="7" type="GuiHandlerData"/>
<property name="PlanAheadTab_REFRESH_IP_CATALOG" value="15" type="GuiHandlerData"/>
<property name="PrimaryClocksPanel_RECOMMENDED_CONSTRAINTS_TABLE" value="9" type="GuiHandlerData"/>
<property name="PrimitivesMenu_HIGHLIGHT_LEAF_CELLS" value="2" type="GuiHandlerData"/>
<property name="ProbesView_PROBES_TREE" value="13" type="GuiHandlerData"/>
<property name="ProgramDebugTab_AVAILABLE_TARGETS_ON_SERVER" value="1" type="GuiHandlerData"/>
<property name="ProgramDebugTab_OPEN_RECENTLY_OPENED_TARGET" value="1" type="GuiHandlerData"/>
<property name="ProgramDebugTab_OPEN_TARGET" value="30" type="GuiHandlerData"/>
<property name="ProgramDebugTab_PROGRAM_DEVICE" value="1" type="GuiHandlerData"/>
<property name="ProgramDebugTab_REFRESH_DEVICE" value="17" type="GuiHandlerData"/>
<property name="ProgramFpgaDialog_PROGRAM" value="2" type="GuiHandlerData"/>
<property name="ProgramFpgaDialog_SPECIFY_BITSTREAM_FILE" value="2" type="GuiHandlerData"/>
<property name="ProgressDialog_CANCEL" value="2" type="GuiHandlerData"/>
<property name="ProjectNameChooser_CHOOSE_PROJECT_LOCATION" value="1" type="GuiHandlerData"/>
<property name="ProjectNameChooser_PROJECT_NAME" value="1" type="GuiHandlerData"/>
<property name="ProjectSummaryPowerPanel_TABBED_PANE" value="3" type="GuiHandlerData"/>
<property name="ProjectSummaryTimingPanel_OPEN_TIMING_SUMMARY_REPORT" value="1" type="GuiHandlerData"/>
<property name="ProjectTab_CLOSE_DESIGN" value="16" type="GuiHandlerData"/>
<property name="ProjectTab_RELOAD" value="1" type="GuiHandlerData"/>
<property name="RDICommands_COPY" value="1" type="GuiHandlerData"/>
<property name="RDICommands_CUSTOM_COMMANDS" value="7" type="GuiHandlerData"/>
<property name="RDICommands_DELETE" value="15" type="GuiHandlerData"/>
<property name="RDICommands_PROPERTIES" value="12" type="GuiHandlerData"/>
<property name="RDICommands_REDO" value="15" type="GuiHandlerData"/>
<property name="RDICommands_SAVE_FILE" value="1" type="GuiHandlerData"/>
<property name="RDICommands_SETTINGS" value="5" type="GuiHandlerData"/>
<property name="RDIViews_WAVEFORM_VIEWER" value="202" type="GuiHandlerData"/>
<property name="RSBApplyAutomationBar_RUN_BLOCK_AUTOMATION" value="6" type="GuiHandlerData"/>
<property name="RSBApplyAutomationBar_RUN_CONNECTION_AUTOMATION" value="31" type="GuiHandlerData"/>
<property name="RSBExternalInterfacePropPanels_NAME" value="5" type="GuiHandlerData"/>
<property name="RSBExternalPortPropPanels_NAME" value="12" type="GuiHandlerData"/>
<property name="RemoveSourcesDialog_ALSO_DELETE" value="1" type="GuiHandlerData"/>
<property name="ReportClockNetworksDialog_RESULT_NAME" value="1" type="GuiHandlerData"/>
<property name="ReportTimingDialog_TABBED_PANE" value="7" type="GuiHandlerData"/>
<property name="ReportTimingSummaryDialog_REPORT_TIMING_SUMMARY_DIALOG_TABBED" value="13" type="GuiHandlerData"/>
<property name="RunGadget_SHOW_ERROR" value="1" type="GuiHandlerData"/>
<property name="RunGadget_SHOW_ERROR_AND_CRITICAL_WARNING_MESSAGES" value="3" type="GuiHandlerData"/>
<property name="RunGadget_SHOW_WARNING_AND_ERROR_MESSAGES_IN_MESSAGES" value="2" type="GuiHandlerData"/>
<property name="SaveProjectUtils_CANCEL" value="2" type="GuiHandlerData"/>
<property name="SaveProjectUtils_DONT_SAVE" value="1" type="GuiHandlerData"/>
<property name="SaveProjectUtils_RELOAD" value="1" type="GuiHandlerData"/>
<property name="SaveProjectUtils_SAVE" value="9" type="GuiHandlerData"/>
<property name="SelectAreaDialog_SELECT_ALL" value="2" type="GuiHandlerData"/>
<property name="SelectMenu_HIGHLIGHT" value="32" type="GuiHandlerData"/>
<property name="SelectMenu_MARK" value="11" type="GuiHandlerData"/>
<property name="SelectableListPanel_SELECTABLE_LIST" value="22" type="GuiHandlerData"/>
<property name="SettingsDialog_OPTIONS_TREE" value="1" type="GuiHandlerData"/>
<property name="SettingsDialog_PROJECT_TREE" value="15" type="GuiHandlerData"/>
<property name="SettingsProjectGeneralPage_CHOOSE_DEVICE_FOR_YOUR_PROJECT" value="1" type="GuiHandlerData"/>
<property name="SettingsProjectIPRepositoryPage_ADD_REPOSITORY" value="2" type="GuiHandlerData"/>
<property name="SettingsProjectIPRepositoryPage_REFRESH_ALL" value="2" type="GuiHandlerData"/>
<property name="SettingsProjectIPRepositoryPage_REPOSITORY_CHOOSER" value="5" type="GuiHandlerData"/>
<property name="SignalTreePanel_SIGNAL_TREE_TABLE" value="40" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_CLOSE_DIALOG_UNSAVED_CHANGES_WILL" value="2" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_GENERATE_OUTPUT_PRODUCTS_IMMEDIATELY" value="58" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_OUTPUT_PRODUCT_TREE" value="2" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_RESET_OUTPUT_PRODUCTS" value="7" type="GuiHandlerData"/>
<property name="SimpleOutputProductDialog_SYNTHESIZE_DESIGN_GLOBALLY" value="2" type="GuiHandlerData"/>
<property name="SmartConnect_SHOW_ADVANCED_PROPERTIES" value="1" type="GuiHandlerData"/>
<property name="SrcChooserPanel_ADD_HDL_AND_NETLIST_FILES_TO_YOUR_PROJECT" value="1" type="GuiHandlerData"/>
<property name="SrcChooserPanel_CREATE_FILE" value="1" type="GuiHandlerData"/>
<property name="SrcChooserPanel_MAKE_LOCAL_COPY_OF_THESE_FILES_INTO" value="1" type="GuiHandlerData"/>
<property name="SrcMenu_IP_HIERARCHY" value="10" type="GuiHandlerData"/>
<property name="StaleRunDialog_NO" value="2" type="GuiHandlerData"/>
<property name="StaleRunDialog_RUN_SYNTHESIS" value="3" type="GuiHandlerData"/>
<property name="StateMonitor_RESET_RUN" value="1" type="GuiHandlerData"/>
<property name="SyntheticaGettingStartedView_RECENT_PROJECTS" value="62" type="GuiHandlerData"/>
<property name="SyntheticaStateMonitor_CANCEL" value="5" type="GuiHandlerData"/>
<property name="SystemBuilderMenu_ASSIGN_ADDRESS" value="3" type="GuiHandlerData"/>
<property name="SystemBuilderMenu_CREATE_COMMENT" value="1" type="GuiHandlerData"/>
<property name="SystemBuilderMenu_CREATE_INTERFACE_PORT" value="2" type="GuiHandlerData"/>
<property name="SystemBuilderMenu_CREATE_PORT" value="19" type="GuiHandlerData"/>
<property name="SystemBuilderMenu_END_CONNECTION_MODE" value="1" type="GuiHandlerData"/>
<property name="SystemBuilderMenu_IP_DOCUMENTATION" value="18" type="GuiHandlerData"/>
<property name="SystemBuilderView_ADD_IP" value="7" type="GuiHandlerData"/>
<property name="SystemBuilderView_EXPAND_COLLAPSE" value="86" type="GuiHandlerData"/>
<property name="SystemBuilderView_ORIENTATION" value="25" type="GuiHandlerData"/>
<property name="SystemBuilderView_PINNING" value="118" type="GuiHandlerData"/>
<property name="SystemBuilderView_PIN_BLOCKS_AND_PORTS_TO_LOCATION" value="1" type="GuiHandlerData"/>
<property name="SystemBuilderView_SEARCH" value="3" type="GuiHandlerData"/>
<property name="SystemTab_BLOCKS" value="1" type="GuiHandlerData"/>
<property name="SystemTab_REPORT_IP_STATUS" value="5" type="GuiHandlerData"/>
<property name="SystemTab_SHOW_IP_STATUS" value="3" type="GuiHandlerData"/>
<property name="SystemTab_UPGRADE_LATER" value="2" type="GuiHandlerData"/>
<property name="SystemTreeView_SYSTEM_TREE" value="38" type="GuiHandlerData"/>
<property name="TargetChooserPanel_ADD_XILINX_VIRTUAL_CABLE_AS_HARDWARE" value="1" type="GuiHandlerData"/>
<property name="TaskBanner_CLOSE" value="32" type="GuiHandlerData"/>
<property name="TclConsoleView_CLEAR_ALL_OUTPUT" value="1" type="GuiHandlerData"/>
<property name="TclConsoleView_COPY" value="1" type="GuiHandlerData"/>
<property name="TclConsoleView_TCL_CONSOLE_CODE_EDITOR" value="98" type="GuiHandlerData"/>
<property name="TclFindDialog_SPECIFY_OF_OBJECTS_OPTION" value="1" type="GuiHandlerData"/>
<property name="TclObjectTreeTable_TREETABLE" value="18" type="GuiHandlerData"/>
<property name="TimingGettingStartedPanel_REPORT_TIMING" value="3" type="GuiHandlerData"/>
<property name="TimingGettingStartedPanel_REPORT_TIMING_SUMMARY" value="2" type="GuiHandlerData"/>
<property name="TimingItemFlatTablePanel_FLOORPLANNING" value="1" type="GuiHandlerData"/>
<property name="TimingItemFlatTablePanel_TABLE" value="59" type="GuiHandlerData"/>
<property name="TimingItemFlatTablePanel_VIEW_PATH_REPORT" value="2" type="GuiHandlerData"/>
<property name="TimingItemTreeTablePanel_TIMING_ITEM_TREE_TABLE" value="18" type="GuiHandlerData"/>
<property name="TouchpointSurveyDialog_NO" value="2" type="GuiHandlerData"/>
<property name="TriggerSetupPanel_TABLE" value="73" type="GuiHandlerData"/>
<property name="TriggerStatusPanel_RUN_TRIGGER_FOR_THIS_ILA_CORE" value="1" type="GuiHandlerData"/>
<property name="VioTreeTablePanel_VIO_TREE_TABLE" value="3" type="GuiHandlerData"/>
<property name="WaveformNameTree_WAVEFORM_NAME_TREE" value="66" type="GuiHandlerData"/>
<property name="WaveformView_ADD" value="2" type="GuiHandlerData"/>
<property name="XPG_ComboBox_VALUE_OF_SPECIFIED_PARAMETER" value="1" type="GuiHandlerData"/>
<property name="XPG_ComboBox_VALUE_OF_SPECIFIED_PARAMETER_MANUAL" value="1" type="GuiHandlerData"/>
<property name="XPG_IPSymbol_SHOW_DISABLED_PORTS" value="2" type="GuiHandlerData"/>
<property name="XdcCategoryTree_XDC_CATEGORY_TREE" value="38" type="GuiHandlerData"/>
<property name="XdcViewerTreeTablePanel_XDC_VIEWER_TREE_TABLE" value="24" type="GuiHandlerData"/>
</item>
<item name="Other">
<property name="GuiMode" value="108" type="GuiMode"/>
<property name="BatchMode" value="0" type="BatchMode"/>
<property name="TclMode" value="96" type="TclMode"/>
</item>
</section>
</application>
</document>
