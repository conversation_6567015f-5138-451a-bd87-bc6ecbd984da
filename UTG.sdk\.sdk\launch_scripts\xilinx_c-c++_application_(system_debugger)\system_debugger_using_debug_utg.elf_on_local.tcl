connect -url tcp:127.0.0.1:3121
source F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/ps7_init.tcl
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB II 20250308a9b201"} -index 0
rst -system
after 3000
targets -set -filter {jtag_cable_name =~ "Platform Cable USB II 20250308a9b201" && level==0} -index 1
fpga -file F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/UGT.bit
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB II 20250308a9b201"} -index 0
loadhw -hw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf -mem-ranges [list {0x40000000 0xbfffffff}]
configparams force-mem-access 1
targets -set -nocase -filter {name =~"APU*" && jtag_cable_name =~ "Platform Cable USB II 20250308a9b201"} -index 0
ps7_init
ps7_post_config
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB II 20250308a9b201"} -index 0
dow F:/UTG/UTG/UTG.sdk/UTG/Debug/UTG.elf
configparams force-mem-access 0
targets -set -nocase -filter {name =~ "ARM*#0" && jtag_cable_name =~ "Platform Cable USB II 20250308a9b201"} -index 0
con
