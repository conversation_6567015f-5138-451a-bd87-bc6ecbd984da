
`timescale 1 ns / 1 ps

	module AXI_ADC_v1_0 #
	(
		// Users to add parameters here

		// User parameters ends
		// Do not modify the parameters beyond this line

		// Parameters of Axi Master Bus Interface M00_AXI
		parameter  C_M00_AXI_TARGET_SLAVE_BASE_ADDR	= 32'h40000000,
		parameter integer C_M00_AXI_BURST_LEN	= 16,
		parameter integer C_M00_AXI_ID_WIDTH	= 1,
		parameter integer C_M00_AXI_ADDR_WIDTH	= 32,
		parameter integer C_M00_AXI_DATA_WIDTH	= 32,
		parameter integer C_M00_AXI_AWUSER_WIDTH	= 0,
		parameter integer C_M00_AXI_ARUSER_WIDTH	= 0,
		parameter integer C_M00_AXI_WUSER_WIDTH	= 0,
		parameter integer C_M00_AXI_RUSER_WIDTH	= 0,
		parameter integer C_M00_AXI_BUSER_WIDTH	= 0
	)
	(
		// Users to add ports here
		input ADC_OR,
		input ADC_DCLK,	//ADC_DCLK frequent equ ADC_SP_CLK
		input [11:0] ADC_DATA,
		output wire ADC_PDWN,
		output wire ADC_SP_CLK,
		output wire ADC_START,
		output wire ULTRASOUND_PLUS,
		// User ports ends
		// Do not modify the ports beyond this line


		// Ports of Axi Slave Bus Interface S00_AXI(实现 ADC 采集寄存器配置相关)
		input wire  s00_axi_aclk,
		input wire  s00_axi_aresetn,
		input wire [5 : 0] s00_axi_awaddr,
		input wire [2 : 0] s00_axi_awprot,
		input wire  s00_axi_awvalid,
		output wire  s00_axi_awready,
		input wire [31 : 0] s00_axi_wdata,
		input wire [3 : 0] s00_axi_wstrb,
		input wire  s00_axi_wvalid,
		output wire  s00_axi_wready,
		output wire [1 : 0] s00_axi_bresp,
		output wire  s00_axi_bvalid,
		input wire  s00_axi_bready,
		input wire [5 : 0] s00_axi_araddr,
		input wire [2 : 0] s00_axi_arprot,
		input wire  s00_axi_arvalid,
		output wire  s00_axi_arready,
		output wire [31 : 0] s00_axi_rdata,
		output wire [1 : 0] s00_axi_rresp,
		output wire  s00_axi_rvalid,
		input wire  s00_axi_rready,

		// Ports of Axi Master Bus Interface M00_AXI（ADC 数据采集)
		input wire  m00_axi_aclk,
		input wire  m00_axi_aresetn,
		output wire [C_M00_AXI_ID_WIDTH-1 : 0] m00_axi_awid,
		output wire [C_M00_AXI_ADDR_WIDTH-1 : 0] m00_axi_awaddr,
		output wire [7 : 0] m00_axi_awlen,
		output wire [2 : 0] m00_axi_awsize,
		output wire [1 : 0] m00_axi_awburst,
		output wire  m00_axi_awlock,
		output wire [3 : 0] m00_axi_awcache,
		output wire [2 : 0] m00_axi_awprot,
		output wire [3 : 0] m00_axi_awqos,
		output wire [C_M00_AXI_AWUSER_WIDTH-1 : 0] m00_axi_awuser,
		output wire  m00_axi_awvalid,
		input wire  m00_axi_awready,
		output wire [C_M00_AXI_DATA_WIDTH-1 : 0] m00_axi_wdata,
		output wire [C_M00_AXI_DATA_WIDTH/8-1 : 0] m00_axi_wstrb,
		output wire  m00_axi_wlast,
		output wire [C_M00_AXI_WUSER_WIDTH-1 : 0] m00_axi_wuser,
		output wire  m00_axi_wvalid,
		input wire  m00_axi_wready,
		input wire [C_M00_AXI_ID_WIDTH-1 : 0] m00_axi_bid,
		input wire [1 : 0] m00_axi_bresp,
		input wire [C_M00_AXI_BUSER_WIDTH-1 : 0] m00_axi_buser,
		input wire  m00_axi_bvalid,
		output wire  m00_axi_bready,
		output wire [C_M00_AXI_ID_WIDTH-1 : 0] m00_axi_arid,
		output wire [C_M00_AXI_ADDR_WIDTH-1 : 0] m00_axi_araddr,
		output wire [7 : 0] m00_axi_arlen,
		output wire [2 : 0] m00_axi_arsize,
		output wire [1 : 0] m00_axi_arburst,
		output wire  m00_axi_arlock,
		output wire [3 : 0] m00_axi_arcache,
		output wire [2 : 0] m00_axi_arprot,
		output wire [3 : 0] m00_axi_arqos,
		output wire [C_M00_AXI_ARUSER_WIDTH-1 : 0] m00_axi_aruser,
		output wire  m00_axi_arvalid,
		input wire  m00_axi_arready,
		input wire [C_M00_AXI_ID_WIDTH-1 : 0] m00_axi_rid,
		input wire [C_M00_AXI_DATA_WIDTH-1 : 0] m00_axi_rdata,
		input wire [1 : 0] m00_axi_rresp,
		input wire  m00_axi_rlast,
		input wire [C_M00_AXI_RUSER_WIDTH-1 : 0] m00_axi_ruser,
		input wire  m00_axi_rvalid,
		output wire  m00_axi_rready
	);
	
		//FIFO  signals
	wire [15 : 0] 	fifo_wr_data;
	wire  			fifo_wr_en;
	wire 			fifo_rst;
	wire			fifo_wr_clk;
	wire			fifo_wr_rst_busy;
	
	//adc ctrl signal 
	wire 			i_adc_fifo_rst;
	wire 			i_adc_start;
	wire 			i_adc_powerdown;
	wire [24 : 0]	i_adc_sample_count;//total sample number, unit 16bits per sample
	wire [31 : 0]   i_adc_dbg;
		
	wire [31 : 0] 	i_s2mm_transfer_bytes;//当前传输总字节
	wire [31 : 0] 	i_s2mm_transfer_base_addr;
	wire [31  : 0]	i_s2mm_transfer_status;
	wire 			i_s2mm_transfer_done;
	wire [31 : 0]	i_s2mm_transfer_expect_bytes;
	wire [31 : 0]	i_fifo_wr_data_count;
	wire [31 : 0]	i_fifo_rd_data_count;
	wire [31 : 0]	i_fifo_status;

	
//one sample is 2 bytes	
assign ADC_START = i_adc_start;
assign i_s2mm_transfer_expect_bytes = {6'd0,i_adc_sample_count,1'b0};//i_adc_sample_count<<1,采样个数 转采集字节数量


// Instantiation of Axi Bus Interface S00_AXI（ADC 采集寄存器控制，s00_axi_aclk时钟域）
	S_AXI # ( 
		.C_M00_AXI_TARGET_SLAVE_BASE_ADDR(C_M00_AXI_TARGET_SLAVE_BASE_ADDR),
		.C_S_AXI_DATA_WIDTH(32),
		.C_S_AXI_ADDR_WIDTH(6)
	) S_AXI_inst (
		.adc_start	 		(i_adc_start),
		.adc_powerdown		(i_adc_powerdown),
		.adc_sample_count	(i_adc_sample_count),
		.adc_fifo_rst		(i_adc_fifo_rst),
		.adc_dbg			(i_adc_dbg),
		.fifo_wr_data_count	(i_fifo_wr_data_count),
		.fifo_rd_data_count	(i_fifo_rd_data_count),
		.fifo_status  		(i_fifo_status),
		.s2mm_transfer_bytes (i_s2mm_transfer_bytes),
		.s2mm_transfer_status(i_s2mm_transfer_status),
		.s2mm_transfer_base_addr(i_s2mm_transfer_base_addr),
			
		
		.S_AXI_ACLK(s00_axi_aclk),
		.S_AXI_ARESETN(s00_axi_aresetn),
		.S_AXI_AWADDR(s00_axi_awaddr),
		.S_AXI_AWPROT(s00_axi_awprot),
		.S_AXI_AWVALID(s00_axi_awvalid),
		.S_AXI_AWREADY(s00_axi_awready),
		.S_AXI_WDATA(s00_axi_wdata),
		.S_AXI_WSTRB(s00_axi_wstrb),
		.S_AXI_WVALID(s00_axi_wvalid),
		.S_AXI_WREADY(s00_axi_wready),
		.S_AXI_BRESP(s00_axi_bresp),
		.S_AXI_BVALID(s00_axi_bvalid),
		.S_AXI_BREADY(s00_axi_bready),
		.S_AXI_ARADDR(s00_axi_araddr),
		.S_AXI_ARPROT(s00_axi_arprot),
		.S_AXI_ARVALID(s00_axi_arvalid),
		.S_AXI_ARREADY(s00_axi_arready),
		.S_AXI_RDATA(s00_axi_rdata),
		.S_AXI_RRESP(s00_axi_rresp),
		.S_AXI_RVALID(s00_axi_rvalid),
		.S_AXI_RREADY(s00_axi_rready)
	);

// Instantiation of Axi Bus Interface M00_AXI（ADC 采集处理，m00_axi_aclk时钟域）
	M_AXIS_S2MM # ( 
		.C_M_TARGET_SLAVE_BASE_ADDR(C_M00_AXI_TARGET_SLAVE_BASE_ADDR),
		.C_M_AXI_BURST_LEN(C_M00_AXI_BURST_LEN),
		.C_M_AXI_ID_WIDTH(C_M00_AXI_ID_WIDTH),
		.C_M_AXI_ADDR_WIDTH(C_M00_AXI_ADDR_WIDTH),
		.C_M_AXI_DATA_WIDTH(C_M00_AXI_DATA_WIDTH),
		.C_M_AXI_AWUSER_WIDTH(C_M00_AXI_AWUSER_WIDTH),
		.C_M_AXI_ARUSER_WIDTH(C_M00_AXI_ARUSER_WIDTH),
		.C_M_AXI_WUSER_WIDTH(C_M00_AXI_WUSER_WIDTH),
		.C_M_AXI_RUSER_WIDTH(C_M00_AXI_RUSER_WIDTH),
		.C_M_AXI_BUSER_WIDTH(C_M00_AXI_BUSER_WIDTH)
	) M_AXIS_S2MM_inst (
		.fifo_rst					(fifo_rst),
		.fifo_wr_clk				(fifo_wr_clk),	
		.fifo_wr_en 				(fifo_wr_en),
		.fifo_wr_data				(fifo_wr_data),
		.fifo_wr_rst_busy			(fifo_wr_rst_busy),
		.fifo_wr_data_count			(i_fifo_wr_data_count),
		.fifo_rd_data_count			(i_fifo_rd_data_count),
		.fifo_status				(i_fifo_status),
		.s2mm_transfer_start		(i_adc_start),
		.s2mm_transfer_expect_bytes (i_s2mm_transfer_expect_bytes),
		.s2mm_transfer_base_addr 	(i_s2mm_transfer_base_addr ),
		.s2mm_transfer_bytes_now	(i_s2mm_transfer_bytes),
		.s2mm_transfer_status		(i_s2mm_transfer_status),
		
		.M_AXI_ACLK(m00_axi_aclk),
		.M_AXI_ARESETN(m00_axi_aresetn),
		.M_AXI_AWID(m00_axi_awid),
		.M_AXI_AWADDR(m00_axi_awaddr),
		.M_AXI_AWLEN(m00_axi_awlen),
		.M_AXI_AWSIZE(m00_axi_awsize),
		.M_AXI_AWBURST(m00_axi_awburst),
		.M_AXI_AWLOCK(m00_axi_awlock),
		.M_AXI_AWCACHE(m00_axi_awcache),
		.M_AXI_AWPROT(m00_axi_awprot),
		.M_AXI_AWQOS(m00_axi_awqos),
		.M_AXI_AWUSER(m00_axi_awuser),
		.M_AXI_AWVALID(m00_axi_awvalid),
		.M_AXI_AWREADY(m00_axi_awready),
		.M_AXI_WDATA(m00_axi_wdata),
		.M_AXI_WSTRB(m00_axi_wstrb),
		.M_AXI_WLAST(m00_axi_wlast),
		.M_AXI_WUSER(m00_axi_wuser),
		.M_AXI_WVALID(m00_axi_wvalid),
		.M_AXI_WREADY(m00_axi_wready),
		.M_AXI_BID(m00_axi_bid),
		.M_AXI_BRESP(m00_axi_bresp),
		.M_AXI_BUSER(m00_axi_buser),
		.M_AXI_BVALID(m00_axi_bvalid),
		.M_AXI_BREADY(m00_axi_bready),
		.M_AXI_ARID(m00_axi_arid),
		.M_AXI_ARADDR(m00_axi_araddr),
		.M_AXI_ARLEN(m00_axi_arlen),
		.M_AXI_ARSIZE(m00_axi_arsize),
		.M_AXI_ARBURST(m00_axi_arburst),
		.M_AXI_ARLOCK(m00_axi_arlock),
		.M_AXI_ARCACHE(m00_axi_arcache),
		.M_AXI_ARPROT(m00_axi_arprot),
		.M_AXI_ARQOS(m00_axi_arqos),
		.M_AXI_ARUSER(m00_axi_aruser),
		.M_AXI_ARVALID(m00_axi_arvalid),
		.M_AXI_ARREADY(m00_axi_arready),
		.M_AXI_RID(m00_axi_rid),
		.M_AXI_RDATA(m00_axi_rdata),
		.M_AXI_RRESP(m00_axi_rresp),
		.M_AXI_RLAST(m00_axi_rlast),
		.M_AXI_RUSER(m00_axi_ruser),
		.M_AXI_RVALID(m00_axi_rvalid),
		.M_AXI_RREADY(m00_axi_rready)
	);

	// Add user logic here
	
	adc_top adc_inst
	(
		.rst_n 				(s00_axi_aresetn),
		.sys_clk			(s00_axi_aclk),	
		.adc_fifo_rst		(i_adc_fifo_rst),
		.adc_start 			(i_adc_start),
		.adc_powerdown		(i_adc_powerdown),
		.adc_sample_num		(i_adc_sample_count),
		
		//output to fifo
		.fifo_rst			(fifo_rst),
		.fifo_wr_clk		(fifo_wr_clk),
		.fifo_wr_data		(fifo_wr_data),
		.fifo_wr_en			(fifo_wr_en),
		.fifo_wr_rst_busy	(fifo_wr_rst_busy),
		.adc_clk_cnts_dbg	(i_adc_dbg),
		//adc Interface
		.ADC_OR				(ADC_OR),
		.ADC_DCLK			(ADC_DCLK),
		.ADC_DATA			(ADC_DATA),
		.ADC_PDWN			(ADC_PDWN),
		.ADC_SP_CLK			(ADC_SP_CLK),
		.ULTRASOUND_PLUS	(ULTRASOUND_PLUS)
	);
	// User logic ends

	endmodule
