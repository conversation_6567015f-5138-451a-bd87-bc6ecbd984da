!SESSION 2025-08-18 13:41:27.287 -----------------------------------------------
eclipse.buildId=2018.3
java.version=1.8.0_112
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -data F:/UTG/UTG/UTG.sdk

This is a continuation of log file F:\UTG\UTG\UTG.sdk\.metadata\.bak_0.log
Created Time: 2025-08-18 13:43:12.906

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:12.906
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:12.907
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:12.931
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:12.933
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:12.947
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.731
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.733
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.748
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.848
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.850
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.864
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.874
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.876
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.891
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.913
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.915
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:13.929
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:14.487
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:14.489
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:14.505
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:15.091
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:15.093
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:15.109
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:15.773
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:15.776
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:15.791
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.712
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.714
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.730
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.814
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.815
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.816
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.816
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.817
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.817
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.818
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.893
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.895
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:16.909
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:17.573
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:17.576
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:17.592
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.181
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.183
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.201
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.510
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.512
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.529
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.831
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.834
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:18.852
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:19.546
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:19.548
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:19.574
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:19.616
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:19.618
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:19.618
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:19.618
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:20.934
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:20.935
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:20.935
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:20.936
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.166
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.167
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.167
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.167
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.591
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.592
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.592
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:21.592
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:22.237
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:22.240
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:22.261
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:22.945
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:22.948
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:22.965
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:23.708
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:23.711
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:23.728
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:24.400
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:24.402
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:24.419
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:25.899
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 13:43:25.943
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.closeConsoleOutputStream(ConsoleOutputSniffer.java:160)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.close(ConsoleOutputSniffer.java:68)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.close(ProcessClosure.java:98)
	at org.eclipse.cdt.internal.core.ProcessClosure.isAlive(ProcessClosure.java:193)
	at org.eclipse.cdt.core.CommandLauncher.waitAndRead(CommandLauncher.java:259)
	at org.eclipse.cdt.internal.core.BuildRunnerHelper.build(BuildRunnerHelper.java:273)
	at org.eclipse.cdt.make.core.MakeBuilder.invokeMake(MakeBuilder.java:219)
	at org.eclipse.cdt.make.core.MakeBuilder.build(MakeBuilder.java:108)
	at org.eclipse.core.internal.events.BuildManager$2.run(BuildManager.java:735)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:206)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:246)
	at org.eclipse.core.internal.events.BuildManager$1.run(BuildManager.java:301)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:304)
	at org.eclipse.core.internal.events.BuildManager.basicBuildLoop(BuildManager.java:360)
	at org.eclipse.core.internal.events.BuildManager.build(BuildManager.java:383)
	at org.eclipse.core.internal.events.AutoBuildJob.doBuild(AutoBuildJob.java:144)
	at org.eclipse.core.internal.events.AutoBuildJob.run(AutoBuildJob.java:235)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:55)

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.336
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.340
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.340
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.342
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.8",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.343
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.351
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Result: [null, {"AXI_ADC_0": {"hier_name": "AXI_ADC_0",
"type": "AXI_ADC",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_dynclk_0": {"hier_name": "axi_dynclk_0",
"type": "axi_dynclk",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_smc": {"hier_name": "axi_smc",
"type": "smartconnect",
"version": "1.0",
"ip_type": "BUS",
},
"axi_smc1": {"hier_name": "axi_smc1",
"type": "smartconnect",
"version": "1.0",
"ip_type": "BUS",
},
"axi_vdma_0": {"hier_name": "axi_vdma_0",
"type": "axi_vdma",
"version": "6.3",
"ip_type": "PERIPHERAL",
},
"misc_0": {"hier_name": "misc_0",
"type": "misc",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"processing_system7_0": {"hier_name": "processing_system7_0",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"ps7_0_axi_periph": {"hier_name": "ps7_0_axi_periph",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"rst_ps7_0_100M": {"hier_name": "rst_ps7_0_100M",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"rst_ps7_0_100M1": {"hier_name": "rst_ps7_0_100M1",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"v_axi4s_vid_out_0": {"hier_name": "v_axi4s_vid_out_0",
"type": "v_axi4s_vid_out",
"version": "4.0",
"ip_type": "PERIPHERAL",
},
"v_tc_0": {"hier_name": "v_tc_0",
"type": "v_tc",
"version": "6.1",
"ip_type": "PERIPHERAL",
},
"xlconcat_0": {"hier_name": "xlconcat_0",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"xlconstant_0": {"hier_name": "xlconstant_0",
"type": "xlconstant",
"version": "1.1",
"ip_type": "PERIPHERAL",
},
"xlconstant_1": {"hier_name": "xlconstant_1",
"type": "xlconstant",
"version": "1.1",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_sd_0": {"hier_name": "ps7_sd_0",
"type": "ps7_sdio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_i2c_0": {"hier_name": "ps7_i2c_0",
"type": "ps7_i2c",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.353
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.354
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Result: [null, {"device": "7z020",
"family": "zynq",
"timestamp": "Fri Aug  1 16:38:37 2025",
"vivado_version": "2018.3",
"part": "xc7z020clg400-2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.355
!MESSAGE XSCT Command: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:47.356
!MESSAGE XSCT command with result: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:49.031
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:49.034
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:49.034
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:49.036
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.8",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:49.036
!MESSAGE XSCT Command: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 13:43:49.038
!MESSAGE XSCT command with result: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 1 0 2025-08-18 13:44:09.888
!MESSAGE Updating toolusage
!SESSION 2025-08-18 14:29:40.311 -----------------------------------------------
eclipse.buildId=2018.3
java.version=1.8.0_112
java.vendor=Oracle Corporation
BootLoader constants: OS=win32, ARCH=x86_64, WS=win32, NL=zh_CN
Command-line arguments:  -os win32 -ws win32 -arch x86_64 -data F:/UTG/UTG/UTG.sdk

!ENTRY org.eclipse.ui 2 0 2025-08-18 14:29:41.427
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-18 14:29:41.427
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY org.eclipse.ui 2 0 2025-08-18 14:29:41.940
!MESSAGE Warnings while parsing the commands from the 'org.eclipse.ui.commands' and 'org.eclipse.ui.actionDefinitions' extension points.
!SUBENTRY 1 org.eclipse.ui 2 0 2025-08-18 14:29:41.941
!MESSAGE Commands should really have a category: plug-in='com.xilinx.sdk.appwiz', id='com.xilinx.sdk.app.commands.ChangeAtfBuiltReferences', categoryId='com.xilinx.sdk.app.commands.category'

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:45.499
!MESSAGE XSCT Command: [::hsi::utils::init_repo], Thread: Worker-1

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:45.500
!MESSAGE XSCT Command: [set sdk::sdk_chan tcfchan#0], Thread: Thread-16

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:45.615
!MESSAGE XSCT command with result: [::hsi::utils::init_repo], Result: [null, ]. Thread: Worker-1

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:45.615
!MESSAGE XSCT command with result: [set sdk::sdk_chan tcfchan#0], Result: [null, tcfchan#0]. Thread: Thread-16

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:45.616
!MESSAGE XSCT Command: [setws F:/UTG/UTG/UTG.sdk], Thread: Thread-16

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:45.617
!MESSAGE XSCT command with result: [setws F:/UTG/UTG/UTG.sdk], Result: [null, ]. Thread: Thread-16

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:45.628
!MESSAGE XSCT Command: [::hsi::utils::openhw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Thread: Worker-2

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:29:47.261
!MESSAGE XSCT command with result: [::hsi::utils::openhw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Result: [null, ]. Thread: Worker-2

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.139
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.core.CommandLauncher.printCommandLine(CommandLauncher.java:287)
	at org.eclipse.cdt.core.CommandLauncher.waitAndRead(CommandLauncher.java:250)
	at org.eclipse.cdt.internal.core.BuildRunnerHelper.build(BuildRunnerHelper.java:273)
	at org.eclipse.cdt.make.core.MakeBuilder.invokeMake(MakeBuilder.java:219)
	at org.eclipse.cdt.make.core.MakeBuilder.build(MakeBuilder.java:108)
	at org.eclipse.core.internal.events.BuildManager$2.run(BuildManager.java:735)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:206)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:246)
	at org.eclipse.core.internal.events.BuildManager$1.run(BuildManager.java:301)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:304)
	at org.eclipse.core.internal.events.BuildManager.basicBuildLoop(BuildManager.java:360)
	at org.eclipse.core.internal.events.BuildManager.build(BuildManager.java:383)
	at org.eclipse.core.internal.events.AutoBuildJob.doBuild(AutoBuildJob.java:144)
	at org.eclipse.core.internal.events.AutoBuildJob.run(AutoBuildJob.java:235)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:55)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.159
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.160
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.186
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.188
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.217
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.220
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.241
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.243
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.265
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.267
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.291
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.293
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.316
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.318
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.340
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.343
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.365
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.367
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.389
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.391
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.414
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.416
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.444
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.446
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.471
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.474
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.499
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.501
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.528
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.530
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.596
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.598
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.620
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.622
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.644
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.646
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.668
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.670
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.692
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.694
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:02.711
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.457
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.459
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.475
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.583
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.585
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.604
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.613
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.616
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.631
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.653
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.655
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:03.670
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:04.222
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:04.224
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:04.241
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:04.822
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:04.824
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:04.841
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:05.496
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:05.497
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:05.515
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.419
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.421
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.439
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.521
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.523
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.524
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.524
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.524
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.524
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.525
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.603
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.606
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:06.622
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:07.274
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:07.277
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:07.295
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:07.870
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:07.873
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:07.891
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:08.195
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:08.198
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:08.216
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:08.511
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:08.513
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:08.530
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:09.214
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:09.216
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:09.243
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:09.285
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:09.287
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:09.287
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:09.287
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.576
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.576
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.576
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.577
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.805
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.806
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.806
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:10.807
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:11.225
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:11.225
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:11.226
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:11.226
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:11.859
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:11.862
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:11.879
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:12.544
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:12.547
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:12.563
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:13.299
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:13.302
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:13.318
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:13.993
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:13.996
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:14.004
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:15.464
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:15.523
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.closeConsoleOutputStream(ConsoleOutputSniffer.java:160)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.close(ConsoleOutputSniffer.java:68)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.close(ProcessClosure.java:98)
	at org.eclipse.cdt.internal.core.ProcessClosure.isAlive(ProcessClosure.java:193)
	at org.eclipse.cdt.core.CommandLauncher.waitAndRead(CommandLauncher.java:259)
	at org.eclipse.cdt.internal.core.BuildRunnerHelper.build(BuildRunnerHelper.java:273)
	at org.eclipse.cdt.make.core.MakeBuilder.invokeMake(MakeBuilder.java:219)
	at org.eclipse.cdt.make.core.MakeBuilder.build(MakeBuilder.java:108)
	at org.eclipse.core.internal.events.BuildManager$2.run(BuildManager.java:735)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:206)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:246)
	at org.eclipse.core.internal.events.BuildManager$1.run(BuildManager.java:301)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:304)
	at org.eclipse.core.internal.events.BuildManager.basicBuildLoop(BuildManager.java:360)
	at org.eclipse.core.internal.events.BuildManager.build(BuildManager.java:383)
	at org.eclipse.core.internal.events.AutoBuildJob.doBuild(AutoBuildJob.java:144)
	at org.eclipse.core.internal.events.AutoBuildJob.run(AutoBuildJob.java:235)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:55)

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.514
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.517
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.517
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.519
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.8",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.520
!MESSAGE XSCT Command: [::hsi::utils::get_all_periphs -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.528
!MESSAGE XSCT command with result: [::hsi::utils::get_all_periphs -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Result: [null, {"AXI_ADC_0": {"hier_name": "AXI_ADC_0",
"type": "AXI_ADC",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_dynclk_0": {"hier_name": "axi_dynclk_0",
"type": "axi_dynclk",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"axi_smc": {"hier_name": "axi_smc",
"type": "smartconnect",
"version": "1.0",
"ip_type": "BUS",
},
"axi_smc1": {"hier_name": "axi_smc1",
"type": "smartconnect",
"version": "1.0",
"ip_type": "BUS",
},
"axi_vdma_0": {"hier_name": "axi_vdma_0",
"type": "axi_vdma",
"version": "6.3",
"ip_type": "PERIPHERAL",
},
"misc_0": {"hier_name": "misc_0",
"type": "misc",
"version": "1.0",
"ip_type": "PERIPHERAL",
},
"processing_system7_0": {"hier_name": "processing_system7_0",
"type": "processing_system7",
"version": "5.5",
"ip_type": "",
},
"ps7_0_axi_periph": {"hier_name": "ps7_0_axi_periph",
"type": "axi_interconnect",
"version": "2.1",
"ip_type": "BUS",
},
"rst_ps7_0_100M": {"hier_name": "rst_ps7_0_100M",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"rst_ps7_0_100M1": {"hier_name": "rst_ps7_0_100M1",
"type": "proc_sys_reset",
"version": "5.0",
"ip_type": "PERIPHERAL",
},
"v_axi4s_vid_out_0": {"hier_name": "v_axi4s_vid_out_0",
"type": "v_axi4s_vid_out",
"version": "4.0",
"ip_type": "PERIPHERAL",
},
"v_tc_0": {"hier_name": "v_tc_0",
"type": "v_tc",
"version": "6.1",
"ip_type": "PERIPHERAL",
},
"xlconcat_0": {"hier_name": "xlconcat_0",
"type": "xlconcat",
"version": "2.1",
"ip_type": "PERIPHERAL",
},
"xlconstant_0": {"hier_name": "xlconstant_0",
"type": "xlconstant",
"version": "1.1",
"ip_type": "PERIPHERAL",
},
"xlconstant_1": {"hier_name": "xlconstant_1",
"type": "xlconstant",
"version": "1.1",
"ip_type": "PERIPHERAL",
},
"ps7_clockc_0": {"hier_name": "ps7_clockc_0",
"type": "ps7_clockc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_uart_0": {"hier_name": "ps7_uart_0",
"type": "ps7_uart",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pl310_0": {"hier_name": "ps7_pl310_0",
"type": "ps7_pl310",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_pmu_0": {"hier_name": "ps7_pmu_0",
"type": "ps7_pmu",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_0": {"hier_name": "ps7_qspi_0",
"type": "ps7_qspi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_qspi_linear_0": {"hier_name": "ps7_qspi_linear_0",
"type": "ps7_qspi_linear",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_axi_interconnect_0": {"hier_name": "ps7_axi_interconnect_0",
"type": "ps7_axi_interconnect",
"version": "1.00.a",
"ip_type": "BUS",
},
"ps7_cortexa9_0": {"hier_name": "ps7_cortexa9_0",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_cortexa9_1": {"hier_name": "ps7_cortexa9_1",
"type": "ps7_cortexa9",
"version": "5.2",
"ip_type": "PROCESSOR",
},
"ps7_ddr_0": {"hier_name": "ps7_ddr_0",
"type": "ps7_ddr",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_sd_0": {"hier_name": "ps7_sd_0",
"type": "ps7_sdio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_i2c_0": {"hier_name": "ps7_i2c_0",
"type": "ps7_i2c",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpio_0": {"hier_name": "ps7_gpio_0",
"type": "ps7_gpio",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ddrc_0": {"hier_name": "ps7_ddrc_0",
"type": "ps7_ddrc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dev_cfg_0": {"hier_name": "ps7_dev_cfg_0",
"type": "ps7_dev_cfg",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_xadc_0": {"hier_name": "ps7_xadc_0",
"type": "ps7_xadc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ocmc_0": {"hier_name": "ps7_ocmc_0",
"type": "ps7_ocmc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_coresight_comp_0": {"hier_name": "ps7_coresight_comp_0",
"type": "ps7_coresight_comp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_gpv_0": {"hier_name": "ps7_gpv_0",
"type": "ps7_gpv",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuc_0": {"hier_name": "ps7_scuc_0",
"type": "ps7_scuc",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_globaltimer_0": {"hier_name": "ps7_globaltimer_0",
"type": "ps7_globaltimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_intc_dist_0": {"hier_name": "ps7_intc_dist_0",
"type": "ps7_intc_dist",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_l2cachec_0": {"hier_name": "ps7_l2cachec_0",
"type": "ps7_l2cachec",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_s": {"hier_name": "ps7_dma_s",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_iop_bus_config_0": {"hier_name": "ps7_iop_bus_config_0",
"type": "ps7_iop_bus_config",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_ram_0": {"hier_name": "ps7_ram_0",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_ram_1": {"hier_name": "ps7_ram_1",
"type": "ps7_ram",
"version": "1.00.a",
"ip_type": "MEMORY_CNTLR",
},
"ps7_scugic_0": {"hier_name": "ps7_scugic_0",
"type": "ps7_scugic",
"version": "1.00.a",
"ip_type": "INTERRUPT_CNTLR",
},
"ps7_scutimer_0": {"hier_name": "ps7_scutimer_0",
"type": "ps7_scutimer",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_scuwdt_0": {"hier_name": "ps7_scuwdt_0",
"type": "ps7_scuwdt",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_slcr_0": {"hier_name": "ps7_slcr_0",
"type": "ps7_slcr",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_dma_ns": {"hier_name": "ps7_dma_ns",
"type": "ps7_dma",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_0": {"hier_name": "ps7_afi_0",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_1": {"hier_name": "ps7_afi_1",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_2": {"hier_name": "ps7_afi_2",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_afi_3": {"hier_name": "ps7_afi_3",
"type": "ps7_afi",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
"ps7_m_axi_gp0": {"hier_name": "ps7_m_axi_gp0",
"type": "ps7_m_axi_gp",
"version": "1.00.a",
"ip_type": "PERIPHERAL",
},
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.530
!MESSAGE XSCT Command: [::hsi::utils::get_design_properties -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.531
!MESSAGE XSCT command with result: [::hsi::utils::get_design_properties -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf], Result: [null, {"device": "7z020",
"family": "zynq",
"timestamp": "Fri Aug  1 16:38:37 2025",
"vivado_version": "2018.3",
"part": "xc7z020clg400-2",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.532
!MESSAGE XSCT Command: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:22.533
!MESSAGE XSCT command with result: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, ]. Thread: main

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.746
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.core.CommandLauncher.printCommandLine(CommandLauncher.java:287)
	at org.eclipse.cdt.core.CommandLauncher.waitAndRead(CommandLauncher.java:250)
	at org.eclipse.cdt.internal.core.BuildRunnerHelper.build(BuildRunnerHelper.java:273)
	at org.eclipse.cdt.make.core.MakeBuilder.invokeMake(MakeBuilder.java:219)
	at org.eclipse.cdt.make.core.MakeBuilder.build(MakeBuilder.java:108)
	at org.eclipse.core.internal.events.BuildManager$2.run(BuildManager.java:735)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:206)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:246)
	at org.eclipse.core.internal.events.BuildManager$1.run(BuildManager.java:301)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:304)
	at org.eclipse.core.internal.events.BuildManager.basicBuildLoop(BuildManager.java:360)
	at org.eclipse.core.internal.events.BuildManager.build(BuildManager.java:383)
	at org.eclipse.core.internal.events.AutoBuildJob.doBuild(AutoBuildJob.java:144)
	at org.eclipse.core.internal.events.AutoBuildJob.run(AutoBuildJob.java:235)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:55)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.765
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.766
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.795
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.798
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.823
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.825
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.848
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.849
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.871
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.873
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.895
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.897
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.921
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.924
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.947
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.950
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.972
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.974
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.996
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:28.998
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.023
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.024
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.049
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.051
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.075
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.078
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.102
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.104
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.126
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.129
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.182
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.193
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.214
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.217
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.241
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.243
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.266
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.267
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.290
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.292
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:29.306
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.052
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.054
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.069
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.171
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.174
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.187
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.197
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.199
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.214
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.235
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.237
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.252
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.801
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.803
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:30.820
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:31.401
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:31.403
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:31.419
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:32.081
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:32.083
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:32.099
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:32.997
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:32.999
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.015
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.099
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.100
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.100
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.101
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.101
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.102
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.102
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.177
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.179
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.193
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.841
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.843
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:33.861
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:34.436
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:34.439
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:34.456
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:34.764
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:34.767
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:34.784
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.074
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.076
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.093
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.782
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.785
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.809
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.851
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.851
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.853
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:35.853
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.145
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.146
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.146
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.146
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.371
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.372
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.372
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.373
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.785
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.786
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.786
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:37.786
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:38.414
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:38.416
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:38.433
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:39.102
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:39.105
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:39.121
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:39.854
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:39.856
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:39.873
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:40.536
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:40.539
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:40.556
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:42.019
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.processLine(ConsoleOutputSniffer.java:178)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.access$0(ConsoleOutputSniffer.java:174)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.checkLine(ConsoleOutputSniffer.java:99)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.write(ConsoleOutputSniffer.java:58)
	at java.io.OutputStream.write(OutputStream.java:75)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.run(ProcessClosure.java:57)

!ENTRY org.eclipse.cdt.core 4 0 2025-08-18 14:31:42.053
!MESSAGE Error
!STACK 0
java.lang.NullPointerException
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer.closeConsoleOutputStream(ConsoleOutputSniffer.java:160)
	at org.eclipse.cdt.internal.core.ConsoleOutputSniffer$ConsoleOutputStream.close(ConsoleOutputSniffer.java:68)
	at org.eclipse.cdt.internal.core.ProcessClosure$ReaderThread.close(ProcessClosure.java:98)
	at org.eclipse.cdt.internal.core.ProcessClosure.isAlive(ProcessClosure.java:193)
	at org.eclipse.cdt.core.CommandLauncher.waitAndRead(CommandLauncher.java:259)
	at org.eclipse.cdt.internal.core.BuildRunnerHelper.build(BuildRunnerHelper.java:273)
	at org.eclipse.cdt.make.core.MakeBuilder.invokeMake(MakeBuilder.java:219)
	at org.eclipse.cdt.make.core.MakeBuilder.build(MakeBuilder.java:108)
	at org.eclipse.core.internal.events.BuildManager$2.run(BuildManager.java:735)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:206)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:246)
	at org.eclipse.core.internal.events.BuildManager$1.run(BuildManager.java:301)
	at org.eclipse.core.runtime.SafeRunner.run(SafeRunner.java:42)
	at org.eclipse.core.internal.events.BuildManager.basicBuild(BuildManager.java:304)
	at org.eclipse.core.internal.events.BuildManager.basicBuildLoop(BuildManager.java:360)
	at org.eclipse.core.internal.events.BuildManager.build(BuildManager.java:383)
	at org.eclipse.core.internal.events.AutoBuildJob.doBuild(AutoBuildJob.java:144)
	at org.eclipse.core.internal.events.AutoBuildJob.run(AutoBuildJob.java:235)
	at org.eclipse.core.internal.jobs.Worker.run(Worker.java:55)

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:49.650
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:49.653
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:49.653
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:49.654
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.8",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:49.655
!MESSAGE XSCT Command: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:49.655
!MESSAGE XSCT command with result: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:51.310
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:51.313
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:51.313
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:51.315
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.8",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:51.315
!MESSAGE XSCT Command: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:51.316
!MESSAGE XSCT command with result: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:55.177
!MESSAGE XSCT Command: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:55.181
!MESSAGE XSCT command with result: [ ::hsi::utils::set_current_hw_sw F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss ], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:55.182
!MESSAGE XSCT Command: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:55.185
!MESSAGE XSCT command with result: [::hsi::utils::get_hw_sw_details -json F:/UTG/UTG/UTG.sdk/UGT_hw_platform_0/system.hdf F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, {"procname": "ps7_cortexa9_0",
"osname": "standalone",
"osver": "6.8",
}]. Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:55.185
!MESSAGE XSCT Command: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Thread: main

!ENTRY com.xilinx.sdk.utils 0 0 2025-08-18 14:31:55.187
!MESSAGE XSCT command with result: [::hsi::utils::closesw F:/UTG/UTG/UTG.sdk/UTG_bsp/system.mss], Result: [null, ]. Thread: main

!ENTRY com.xilinx.sdk.utils 1 0 2025-08-18 14:31:58.751
!MESSAGE Updating toolusage
