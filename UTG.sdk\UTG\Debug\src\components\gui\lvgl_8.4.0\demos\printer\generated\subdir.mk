################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/events_init.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/gui_guider.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrCopy.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrCopy2.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrHome.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrLoader.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintFini.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintInternet.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintMenu.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintMobile.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintUSB.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrScan.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrScanFini.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrSetup.c \
../src/components/gui/lvgl_8.4.0/demos/printer/generated/widgets_init.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/events_init.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/gui_guider.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrCopy.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrCopy2.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrHome.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrLoader.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintFini.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintInternet.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintMenu.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintMobile.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintUSB.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrScan.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrScanFini.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrSetup.o \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/widgets_init.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/events_init.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/gui_guider.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrCopy.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrCopy2.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrHome.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrLoader.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintFini.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintInternet.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintMenu.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintMobile.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrPrintUSB.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrScan.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrScanFini.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/setup_scr_scrSetup.d \
./src/components/gui/lvgl_8.4.0/demos/printer/generated/widgets_init.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/demos/printer/generated/%.o: ../src/components/gui/lvgl_8.4.0/demos/printer/generated/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


