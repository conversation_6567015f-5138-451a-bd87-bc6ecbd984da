set_property SRC_FILE_INFO {cfile:f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc rfile:../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc id:1 order:EARLY scoped_inst:u1/utg_i/processing_system7_0/inst} [current_design]
set_property SRC_FILE_INFO {cfile:F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_pins.xdc rfile:../../../UTG.srcs/constrs_1/new/utg_pins.xdc id:2} [current_design]
set_property SRC_FILE_INFO {cfile:f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_tc_0_1/utg_v_tc_0_1_clocks.xdc rfile:../../../UTG.srcs/sources_1/bd/utg/ip/utg_v_tc_0_1/utg_v_tc_0_1_clocks.xdc id:3 order:LATE scoped_inst:u1/utg_i/v_tc_0/U0} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:4 order:LATE scoped_inst:u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:5 order:LATE scoped_inst:u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:6 order:LATE scoped_inst:u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:7 order:LATE scoped_inst:u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:8 order:LATE scoped_inst:u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:9 order:LATE scoped_inst:u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:10 order:LATE scoped_inst:u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl id:11 order:LATE scoped_inst:u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl id:12 order:LATE scoped_inst:{u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory} unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl id:13 order:LATE scoped_inst:{u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory} unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl id:14 order:LATE scoped_inst:{u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory} unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl id:15 order:LATE scoped_inst:{u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory} unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl id:16 order:LATE scoped_inst:{u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory} unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl id:17 order:LATE scoped_inst:{u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory} unmanaged:yes} [current_design]
set_property SRC_FILE_INFO {cfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl rfile:C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl id:18 order:LATE scoped_inst:{u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory} unmanaged:yes} [current_design]
current_instance u1/utg_i/processing_system7_0/inst
set_property src_info {type:SCOPED_XDC file:1 line:21 export:INPUT save:INPUT read:READ} [current_design]
set_input_jitter clk_fpga_0 0.3
set_property src_info {type:SCOPED_XDC file:1 line:30 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS18" [get_ports "MIO[45]"]
set_property src_info {type:SCOPED_XDC file:1 line:31 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B15" [get_ports "MIO[45]"]
set_property src_info {type:SCOPED_XDC file:1 line:32 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[45]"]
set_property src_info {type:SCOPED_XDC file:1 line:33 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[45]"]
set_property src_info {type:SCOPED_XDC file:1 line:34 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[45]"]
set_property src_info {type:SCOPED_XDC file:1 line:35 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[45]"]
set_property src_info {type:SCOPED_XDC file:1 line:37 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS18" [get_ports "MIO[44]"]
set_property src_info {type:SCOPED_XDC file:1 line:38 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F13" [get_ports "MIO[44]"]
set_property src_info {type:SCOPED_XDC file:1 line:39 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[44]"]
set_property src_info {type:SCOPED_XDC file:1 line:40 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[44]"]
set_property src_info {type:SCOPED_XDC file:1 line:41 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[44]"]
set_property src_info {type:SCOPED_XDC file:1 line:42 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[44]"]
set_property src_info {type:SCOPED_XDC file:1 line:44 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS18" [get_ports "MIO[43]"]
set_property src_info {type:SCOPED_XDC file:1 line:45 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A9" [get_ports "MIO[43]"]
set_property src_info {type:SCOPED_XDC file:1 line:46 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[43]"]
set_property src_info {type:SCOPED_XDC file:1 line:47 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[43]"]
set_property src_info {type:SCOPED_XDC file:1 line:48 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[43]"]
set_property src_info {type:SCOPED_XDC file:1 line:49 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[43]"]
set_property src_info {type:SCOPED_XDC file:1 line:51 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS18" [get_ports "MIO[42]"]
set_property src_info {type:SCOPED_XDC file:1 line:52 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E12" [get_ports "MIO[42]"]
set_property src_info {type:SCOPED_XDC file:1 line:53 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[42]"]
set_property src_info {type:SCOPED_XDC file:1 line:54 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[42]"]
set_property src_info {type:SCOPED_XDC file:1 line:55 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[42]"]
set_property src_info {type:SCOPED_XDC file:1 line:56 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[42]"]
set_property src_info {type:SCOPED_XDC file:1 line:58 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS18" [get_ports "MIO[41]"]
set_property src_info {type:SCOPED_XDC file:1 line:59 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C17" [get_ports "MIO[41]"]
set_property src_info {type:SCOPED_XDC file:1 line:60 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[41]"]
set_property src_info {type:SCOPED_XDC file:1 line:61 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[41]"]
set_property src_info {type:SCOPED_XDC file:1 line:62 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[41]"]
set_property src_info {type:SCOPED_XDC file:1 line:63 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[41]"]
set_property src_info {type:SCOPED_XDC file:1 line:65 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS18" [get_ports "MIO[40]"]
set_property src_info {type:SCOPED_XDC file:1 line:66 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D14" [get_ports "MIO[40]"]
set_property src_info {type:SCOPED_XDC file:1 line:67 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[40]"]
set_property src_info {type:SCOPED_XDC file:1 line:68 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[40]"]
set_property src_info {type:SCOPED_XDC file:1 line:69 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[40]"]
set_property src_info {type:SCOPED_XDC file:1 line:70 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[40]"]
set_property src_info {type:SCOPED_XDC file:1 line:72 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:73 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C8" [get_ports "MIO[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:74 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:75 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:76 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:77 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "MIO[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:79 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:80 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C5" [get_ports "MIO[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:81 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:82 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:83 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:84 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "INPUT" [get_ports "MIO[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:86 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:87 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E9" [get_ports "MIO[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:88 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:89 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:90 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:91 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "INPUT" [get_ports "MIO[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:93 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:94 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A5" [get_ports "MIO[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:95 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:96 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:97 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "MIO[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:99 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:100 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A6" [get_ports "MIO[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:101 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:102 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:103 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:105 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:106 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B7" [get_ports "MIO[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:107 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:108 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:109 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:111 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:112 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D6" [get_ports "MIO[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:113 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:114 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:115 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:117 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:118 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B8" [get_ports "MIO[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:119 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:120 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:121 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "MIO[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:123 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "MIO[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:124 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A7" [get_ports "MIO[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:125 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "slow" [get_ports "MIO[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:126 export:INPUT save:INPUT read:READ} [current_design]
set_property drive "8" [get_ports "MIO[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:127 export:INPUT save:INPUT read:READ} [current_design]
set_property pullup "TRUE" [get_ports "MIO[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:128 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "MIO[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:129 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_VRP"]
set_property src_info {type:SCOPED_XDC file:1 line:130 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H5" [get_ports "DDR_VRP"]
set_property src_info {type:SCOPED_XDC file:1 line:131 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_VRP"]
set_property src_info {type:SCOPED_XDC file:1 line:132 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_VRP"]
set_property src_info {type:SCOPED_XDC file:1 line:133 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_VRN"]
set_property src_info {type:SCOPED_XDC file:1 line:134 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G5" [get_ports "DDR_VRN"]
set_property src_info {type:SCOPED_XDC file:1 line:135 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_VRN"]
set_property src_info {type:SCOPED_XDC file:1 line:136 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_VRN"]
set_property src_info {type:SCOPED_XDC file:1 line:137 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_WEB"]
set_property src_info {type:SCOPED_XDC file:1 line:138 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M5" [get_ports "DDR_WEB"]
set_property src_info {type:SCOPED_XDC file:1 line:139 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_WEB"]
set_property src_info {type:SCOPED_XDC file:1 line:140 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_WEB"]
set_property src_info {type:SCOPED_XDC file:1 line:141 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_RAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:142 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P4" [get_ports "DDR_RAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:143 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_RAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:144 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_RAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:145 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_ODT"]
set_property src_info {type:SCOPED_XDC file:1 line:146 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N5" [get_ports "DDR_ODT"]
set_property src_info {type:SCOPED_XDC file:1 line:147 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_ODT"]
set_property src_info {type:SCOPED_XDC file:1 line:148 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_ODT"]
set_property src_info {type:SCOPED_XDC file:1 line:149 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_DRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:150 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B4" [get_ports "DDR_DRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:151 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:152 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:153 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:154 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W5" [get_ports "DDR_DQS[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:155 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:156 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:157 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:158 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R2" [get_ports "DDR_DQS[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:159 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:160 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:161 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:162 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G2" [get_ports "DDR_DQS[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:163 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:164 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:165 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:166 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C2" [get_ports "DDR_DQS[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:167 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:168 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:169 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS_n[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:170 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W4" [get_ports "DDR_DQS_n[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:171 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS_n[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:172 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS_n[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:173 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS_n[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:174 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T2" [get_ports "DDR_DQS_n[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:175 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS_n[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:176 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS_n[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:177 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS_n[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:178 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F2" [get_ports "DDR_DQS_n[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:179 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS_n[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:180 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS_n[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:181 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15_T_DCI" [get_ports "DDR_DQS_n[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:182 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B2" [get_ports "DDR_DQS_n[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:183 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQS_n[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:184 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQS_n[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:185 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:186 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E3" [get_ports "DDR_DQ[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:187 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:188 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:189 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:190 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E2" [get_ports "DDR_DQ[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:191 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:192 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:193 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:194 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E1" [get_ports "DDR_DQ[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:195 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:196 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:197 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:198 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C1" [get_ports "DDR_DQ[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:199 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:200 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:201 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:202 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D1" [get_ports "DDR_DQ[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:203 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:204 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:205 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:206 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D3" [get_ports "DDR_DQ[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:207 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:208 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:209 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:210 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A4" [get_ports "DDR_DQ[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:211 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:212 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:213 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[31]"]
set_property src_info {type:SCOPED_XDC file:1 line:214 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V3" [get_ports "DDR_DQ[31]"]
set_property src_info {type:SCOPED_XDC file:1 line:215 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[31]"]
set_property src_info {type:SCOPED_XDC file:1 line:216 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[31]"]
set_property src_info {type:SCOPED_XDC file:1 line:217 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[30]"]
set_property src_info {type:SCOPED_XDC file:1 line:218 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V2" [get_ports "DDR_DQ[30]"]
set_property src_info {type:SCOPED_XDC file:1 line:219 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[30]"]
set_property src_info {type:SCOPED_XDC file:1 line:220 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[30]"]
set_property src_info {type:SCOPED_XDC file:1 line:221 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:222 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A2" [get_ports "DDR_DQ[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:223 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:224 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:225 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[29]"]
set_property src_info {type:SCOPED_XDC file:1 line:226 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W3" [get_ports "DDR_DQ[29]"]
set_property src_info {type:SCOPED_XDC file:1 line:227 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[29]"]
set_property src_info {type:SCOPED_XDC file:1 line:228 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[29]"]
set_property src_info {type:SCOPED_XDC file:1 line:229 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[28]"]
set_property src_info {type:SCOPED_XDC file:1 line:230 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y2" [get_ports "DDR_DQ[28]"]
set_property src_info {type:SCOPED_XDC file:1 line:231 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[28]"]
set_property src_info {type:SCOPED_XDC file:1 line:232 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[28]"]
set_property src_info {type:SCOPED_XDC file:1 line:233 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[27]"]
set_property src_info {type:SCOPED_XDC file:1 line:234 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y4" [get_ports "DDR_DQ[27]"]
set_property src_info {type:SCOPED_XDC file:1 line:235 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[27]"]
set_property src_info {type:SCOPED_XDC file:1 line:236 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[27]"]
set_property src_info {type:SCOPED_XDC file:1 line:237 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[26]"]
set_property src_info {type:SCOPED_XDC file:1 line:238 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "W1" [get_ports "DDR_DQ[26]"]
set_property src_info {type:SCOPED_XDC file:1 line:239 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[26]"]
set_property src_info {type:SCOPED_XDC file:1 line:240 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[26]"]
set_property src_info {type:SCOPED_XDC file:1 line:241 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[25]"]
set_property src_info {type:SCOPED_XDC file:1 line:242 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y3" [get_ports "DDR_DQ[25]"]
set_property src_info {type:SCOPED_XDC file:1 line:243 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[25]"]
set_property src_info {type:SCOPED_XDC file:1 line:244 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[25]"]
set_property src_info {type:SCOPED_XDC file:1 line:245 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[24]"]
set_property src_info {type:SCOPED_XDC file:1 line:246 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "V1" [get_ports "DDR_DQ[24]"]
set_property src_info {type:SCOPED_XDC file:1 line:247 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[24]"]
set_property src_info {type:SCOPED_XDC file:1 line:248 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[24]"]
set_property src_info {type:SCOPED_XDC file:1 line:249 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[23]"]
set_property src_info {type:SCOPED_XDC file:1 line:250 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U3" [get_ports "DDR_DQ[23]"]
set_property src_info {type:SCOPED_XDC file:1 line:251 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[23]"]
set_property src_info {type:SCOPED_XDC file:1 line:252 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[23]"]
set_property src_info {type:SCOPED_XDC file:1 line:253 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[22]"]
set_property src_info {type:SCOPED_XDC file:1 line:254 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U2" [get_ports "DDR_DQ[22]"]
set_property src_info {type:SCOPED_XDC file:1 line:255 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[22]"]
set_property src_info {type:SCOPED_XDC file:1 line:256 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[22]"]
set_property src_info {type:SCOPED_XDC file:1 line:257 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[21]"]
set_property src_info {type:SCOPED_XDC file:1 line:258 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "U4" [get_ports "DDR_DQ[21]"]
set_property src_info {type:SCOPED_XDC file:1 line:259 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[21]"]
set_property src_info {type:SCOPED_XDC file:1 line:260 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[21]"]
set_property src_info {type:SCOPED_XDC file:1 line:261 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[20]"]
set_property src_info {type:SCOPED_XDC file:1 line:262 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T4" [get_ports "DDR_DQ[20]"]
set_property src_info {type:SCOPED_XDC file:1 line:263 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[20]"]
set_property src_info {type:SCOPED_XDC file:1 line:264 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[20]"]
set_property src_info {type:SCOPED_XDC file:1 line:265 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:266 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B3" [get_ports "DDR_DQ[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:267 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:268 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:269 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[19]"]
set_property src_info {type:SCOPED_XDC file:1 line:270 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R1" [get_ports "DDR_DQ[19]"]
set_property src_info {type:SCOPED_XDC file:1 line:271 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[19]"]
set_property src_info {type:SCOPED_XDC file:1 line:272 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[19]"]
set_property src_info {type:SCOPED_XDC file:1 line:273 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[18]"]
set_property src_info {type:SCOPED_XDC file:1 line:274 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R3" [get_ports "DDR_DQ[18]"]
set_property src_info {type:SCOPED_XDC file:1 line:275 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[18]"]
set_property src_info {type:SCOPED_XDC file:1 line:276 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[18]"]
set_property src_info {type:SCOPED_XDC file:1 line:277 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[17]"]
set_property src_info {type:SCOPED_XDC file:1 line:278 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P3" [get_ports "DDR_DQ[17]"]
set_property src_info {type:SCOPED_XDC file:1 line:279 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[17]"]
set_property src_info {type:SCOPED_XDC file:1 line:280 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[17]"]
set_property src_info {type:SCOPED_XDC file:1 line:281 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[16]"]
set_property src_info {type:SCOPED_XDC file:1 line:282 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P1" [get_ports "DDR_DQ[16]"]
set_property src_info {type:SCOPED_XDC file:1 line:283 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[16]"]
set_property src_info {type:SCOPED_XDC file:1 line:284 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[16]"]
set_property src_info {type:SCOPED_XDC file:1 line:285 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:286 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J1" [get_ports "DDR_DQ[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:287 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:288 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[15]"]
set_property src_info {type:SCOPED_XDC file:1 line:289 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:290 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H1" [get_ports "DDR_DQ[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:291 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:292 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:293 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:294 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H2" [get_ports "DDR_DQ[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:295 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:296 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:297 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:298 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J3" [get_ports "DDR_DQ[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:299 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:300 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:301 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:302 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "H3" [get_ports "DDR_DQ[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:303 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:304 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:305 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:306 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G3" [get_ports "DDR_DQ[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:307 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:308 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:309 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DQ[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:310 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C3" [get_ports "DDR_DQ[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:311 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DQ[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:312 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DQ[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:313 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DM[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:314 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "Y1" [get_ports "DDR_DM[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:315 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DM[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:316 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DM[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:317 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DM[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:318 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "T1" [get_ports "DDR_DM[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:319 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DM[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:320 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DM[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:321 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DM[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:322 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F1" [get_ports "DDR_DM[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:323 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DM[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:324 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DM[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:325 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15_T_DCI" [get_ports "DDR_DM[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:326 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "A1" [get_ports "DDR_DM[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:327 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_DM[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:328 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "BIDIR" [get_ports "DDR_DM[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:329 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_CS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:330 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N1" [get_ports "DDR_CS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:331 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_CS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:332 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_CS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:333 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_CKE"]
set_property src_info {type:SCOPED_XDC file:1 line:334 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N3" [get_ports "DDR_CKE"]
set_property src_info {type:SCOPED_XDC file:1 line:335 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_CKE"]
set_property src_info {type:SCOPED_XDC file:1 line:336 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_CKE"]
set_property src_info {type:SCOPED_XDC file:1 line:337 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15" [get_ports "DDR_Clk"]
set_property src_info {type:SCOPED_XDC file:1 line:338 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L2" [get_ports "DDR_Clk"]
set_property src_info {type:SCOPED_XDC file:1 line:339 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_Clk"]
set_property src_info {type:SCOPED_XDC file:1 line:340 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "INPUT" [get_ports "DDR_Clk"]
set_property src_info {type:SCOPED_XDC file:1 line:341 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "DIFF_SSTL15" [get_ports "DDR_Clk_n"]
set_property src_info {type:SCOPED_XDC file:1 line:342 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M2" [get_ports "DDR_Clk_n"]
set_property src_info {type:SCOPED_XDC file:1 line:343 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "FAST" [get_ports "DDR_Clk_n"]
set_property src_info {type:SCOPED_XDC file:1 line:344 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "INPUT" [get_ports "DDR_Clk_n"]
set_property src_info {type:SCOPED_XDC file:1 line:345 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_CAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:346 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "P5" [get_ports "DDR_CAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:347 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_CAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:348 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_CAS_n"]
set_property src_info {type:SCOPED_XDC file:1 line:349 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_BankAddr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:350 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J5" [get_ports "DDR_BankAddr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:351 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_BankAddr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:352 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_BankAddr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:353 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_BankAddr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:354 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "R4" [get_ports "DDR_BankAddr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:355 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_BankAddr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:356 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_BankAddr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:357 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_BankAddr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:358 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L5" [get_ports "DDR_BankAddr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:359 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_BankAddr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:360 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_BankAddr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:361 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:362 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "J4" [get_ports "DDR_Addr[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:363 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:364 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[9]"]
set_property src_info {type:SCOPED_XDC file:1 line:365 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:366 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K1" [get_ports "DDR_Addr[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:367 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:368 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[8]"]
set_property src_info {type:SCOPED_XDC file:1 line:369 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:370 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K4" [get_ports "DDR_Addr[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:371 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:372 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[7]"]
set_property src_info {type:SCOPED_XDC file:1 line:373 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:374 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L4" [get_ports "DDR_Addr[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:375 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:376 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[6]"]
set_property src_info {type:SCOPED_XDC file:1 line:377 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:378 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "L1" [get_ports "DDR_Addr[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:379 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:380 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[5]"]
set_property src_info {type:SCOPED_XDC file:1 line:381 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:382 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M4" [get_ports "DDR_Addr[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:383 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:384 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[4]"]
set_property src_info {type:SCOPED_XDC file:1 line:385 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:386 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K3" [get_ports "DDR_Addr[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:387 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:388 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[3]"]
set_property src_info {type:SCOPED_XDC file:1 line:389 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:390 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "M3" [get_ports "DDR_Addr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:391 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:392 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[2]"]
set_property src_info {type:SCOPED_XDC file:1 line:393 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:394 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "K2" [get_ports "DDR_Addr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:395 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:396 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[1]"]
set_property src_info {type:SCOPED_XDC file:1 line:397 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:398 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F4" [get_ports "DDR_Addr[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:399 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:400 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[14]"]
set_property src_info {type:SCOPED_XDC file:1 line:401 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:402 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "D4" [get_ports "DDR_Addr[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:403 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:404 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[13]"]
set_property src_info {type:SCOPED_XDC file:1 line:405 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:406 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E4" [get_ports "DDR_Addr[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:407 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:408 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[12]"]
set_property src_info {type:SCOPED_XDC file:1 line:409 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:410 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "G4" [get_ports "DDR_Addr[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:411 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:412 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[11]"]
set_property src_info {type:SCOPED_XDC file:1 line:413 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:414 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "F5" [get_ports "DDR_Addr[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:415 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:416 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[10]"]
set_property src_info {type:SCOPED_XDC file:1 line:417 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "SSTL15" [get_ports "DDR_Addr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:418 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "N2" [get_ports "DDR_Addr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:419 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "SLOW" [get_ports "DDR_Addr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:420 export:INPUT save:INPUT read:READ} [current_design]
set_property PIO_DIRECTION "OUTPUT" [get_ports "DDR_Addr[0]"]
set_property src_info {type:SCOPED_XDC file:1 line:421 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "PS_PORB"]
set_property src_info {type:SCOPED_XDC file:1 line:422 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "C7" [get_ports "PS_PORB"]
set_property src_info {type:SCOPED_XDC file:1 line:423 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "fast" [get_ports "PS_PORB"]
set_property src_info {type:SCOPED_XDC file:1 line:424 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS18" [get_ports "PS_SRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:425 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "B10" [get_ports "PS_SRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:426 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "fast" [get_ports "PS_SRSTB"]
set_property src_info {type:SCOPED_XDC file:1 line:427 export:INPUT save:INPUT read:READ} [current_design]
set_property iostandard "LVCMOS33" [get_ports "PS_CLK"]
set_property src_info {type:SCOPED_XDC file:1 line:428 export:INPUT save:INPUT read:READ} [current_design]
set_property PACKAGE_PIN "E7" [get_ports "PS_CLK"]
set_property src_info {type:SCOPED_XDC file:1 line:429 export:INPUT save:INPUT read:READ} [current_design]
set_property slew "fast" [get_ports "PS_CLK"]
current_instance
set_property src_info {type:XDC file:2 line:2 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN U18 IOSTANDARD LVCMOS33} [get_ports clk_50M]
set_property src_info {type:XDC file:2 line:3 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN N16 IOSTANDARD LVCMOS33} [get_ports sys_rst_n]
set_property src_info {type:XDC file:2 line:11 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W18 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[0]}]
set_property src_info {type:XDC file:2 line:12 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W19 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[1]}]
set_property src_info {type:XDC file:2 line:13 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN R16 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[2]}]
set_property src_info {type:XDC file:2 line:14 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN R17 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[3]}]
set_property src_info {type:XDC file:2 line:15 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W20 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[4]}]
set_property src_info {type:XDC file:2 line:16 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V20 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[5]}]
set_property src_info {type:XDC file:2 line:17 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN P18 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[6]}]
set_property src_info {type:XDC file:2 line:18 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN N17 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[7]}]
set_property src_info {type:XDC file:2 line:19 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V17 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[8]}]
set_property src_info {type:XDC file:2 line:20 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V18 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[9]}]
set_property src_info {type:XDC file:2 line:21 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN T17 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[10]}]
set_property src_info {type:XDC file:2 line:22 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN R18 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[11]}]
set_property src_info {type:XDC file:2 line:23 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y18 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[12]}]
set_property src_info {type:XDC file:2 line:24 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y19 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[13]}]
set_property src_info {type:XDC file:2 line:25 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN P15 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[14]}]
set_property src_info {type:XDC file:2 line:26 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN P16 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[15]}]
set_property src_info {type:XDC file:2 line:27 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V16 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[16]}]
set_property src_info {type:XDC file:2 line:28 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W16 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[17]}]
set_property src_info {type:XDC file:2 line:29 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN T14 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[18]}]
set_property src_info {type:XDC file:2 line:30 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN T15 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[19]}]
set_property src_info {type:XDC file:2 line:31 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y17 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[20]}]
set_property src_info {type:XDC file:2 line:32 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y16 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[21]}]
set_property src_info {type:XDC file:2 line:33 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN T16 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[22]}]
set_property src_info {type:XDC file:2 line:34 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN U17 IOSTANDARD LVCMOS33} [get_ports {LCD_RGB[23]}]
set_property src_info {type:XDC file:2 line:35 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN N18 IOSTANDARD LVCMOS33} [get_ports LCD_HS]
set_property src_info {type:XDC file:2 line:36 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN T20 IOSTANDARD LVCMOS33} [get_ports LCD_VS]
set_property src_info {type:XDC file:2 line:37 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN U20 IOSTANDARD LVCMOS33} [get_ports LCD_DE]
set_property src_info {type:XDC file:2 line:38 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN M20 IOSTANDARD LVCMOS33} [get_ports LCD_BL]
set_property src_info {type:XDC file:2 line:39 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN P19 IOSTANDARD LVCMOS33} [get_ports LCD_CLK]
set_property src_info {type:XDC file:2 line:40 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN L17 IOSTANDARD LVCMOS33} [get_ports LCD_RST]
set_property src_info {type:XDC file:2 line:42 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN P20 IOSTANDARD LVCMOS33} [get_ports TOUCH_SDA]
set_property src_info {type:XDC file:2 line:43 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN R19 IOSTANDARD LVCMOS33} [get_ports TOUCH_SCL]
set_property src_info {type:XDC file:2 line:44 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN M19 IOSTANDARD LVCMOS33} [get_ports TOUCH_RESET]
set_property src_info {type:XDC file:2 line:45 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN U19 IOSTANDARD LVCMOS33} [get_ports TOUCH_INT]
set_property src_info {type:XDC file:2 line:48 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V12 IOSTANDARD LVCMOS33} [get_ports ADC_PDWN]
set_property src_info {type:XDC file:2 line:49 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN U14 IOSTANDARD LVCMOS33} [get_ports ADC_DCLK]
set_property src_info {type:XDC file:2 line:50 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V13 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[1]}]
set_property src_info {type:XDC file:2 line:51 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y11 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[3]}]
set_property src_info {type:XDC file:2 line:52 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y13 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[5]}]
set_property src_info {type:XDC file:2 line:53 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V11 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[7]}]
set_property src_info {type:XDC file:2 line:54 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W10 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[9]}]
set_property src_info {type:XDC file:2 line:55 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y9 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[11]}]
set_property src_info {type:XDC file:2 line:57 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W13 IOSTANDARD LVCMOS33} [get_ports ADC_SP_CLK]
set_property src_info {type:XDC file:2 line:58 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN U15 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[0]}]
set_property src_info {type:XDC file:2 line:59 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN U13 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[2]}]
set_property src_info {type:XDC file:2 line:60 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W11 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[4]}]
set_property src_info {type:XDC file:2 line:61 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y12 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[6]}]
set_property src_info {type:XDC file:2 line:62 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN V10 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[8]}]
set_property src_info {type:XDC file:2 line:63 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W9 IOSTANDARD LVCMOS33} [get_ports {ADC_DATA[10]}]
set_property src_info {type:XDC file:2 line:64 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN Y8 IOSTANDARD LVCMOS33} [get_ports ADC_OR]
set_property src_info {type:XDC file:2 line:66 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN H15 IOSTANDARD LVCMOS33} [get_ports BPARD_LED0]
set_property src_info {type:XDC file:2 line:67 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN J16 IOSTANDARD LVCMOS33} [get_ports CORE_LED]
set_property src_info {type:XDC file:2 line:70 export:INPUT save:INPUT read:READ} [current_design]
set_property -dict {PACKAGE_PIN W14 IOSTANDARD LVCMOS33} [get_ports ULTRASOUND_PLUS]
current_instance u1/utg_i/v_tc_0/U0
set_property src_info {type:SCOPED_XDC file:3 line:5 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -from [get_clocks -of_objects [get_pins u1/utg_i/v_tc_0/U0/clk]] -to [all_registers -clock [get_clocks -of_objects [get_pins u1/utg_i/v_tc_0/U0/s_axi_aclk]]] -datapath_only [get_property -min PERIOD [get_clocks -of_objects [get_pins u1/utg_i/v_tc_0/U0/clk]]]
set_property src_info {type:SCOPED_XDC file:3 line:6 export:INPUT save:INPUT read:READ} [current_design]
set_max_delay -from [get_clocks -of_objects [get_pins u1/utg_i/v_tc_0/U0/s_axi_aclk]] -to [all_registers -clock [get_clocks -of_objects [get_pins u1/utg_i/v_tc_0/U0/clk]]] -datapath_only [get_property -min PERIOD [get_clocks -of_objects [get_pins u1/utg_i/v_tc_0/U0/s_axi_aclk]]]
current_instance
current_instance u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst
set_property src_info {type:SCOPED_XDC file:4 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst
set_property src_info {type:SCOPED_XDC file:5 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst
set_property src_info {type:SCOPED_XDC file:6 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst
set_property src_info {type:SCOPED_XDC file:7 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst
set_property src_info {type:SCOPED_XDC file:8 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst
set_property src_info {type:SCOPED_XDC file:9 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst
set_property src_info {type:SCOPED_XDC file:10 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst
set_property src_info {type:SCOPED_XDC file:11 line:23 export:INPUT save:NONE read:READ} [current_design]
create_waiver -internal -type CDC -id {CDC-6} -user "xpm_cdc" -desc "The CDC-6 warning is waived as it is safe in the context of XPM_CDC_GRAY." -from [get_pins -quiet {src_gray_ff_reg*/C}] -to [get_pins -quiet {dest_graysync_ff_reg*/D}]
current_instance
current_instance {u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory}
set_property src_info {type:SCOPED_XDC file:12 line:3 export:INPUT save:NONE read:READ} [current_design]
set my_var [get_property dram_emb_xdc [get_cells -hier  -filter {PRIMITIVE_SUBGROUP==LUTRAM || PRIMITIVE_SUBGROUP==dram || PRIMITIVE_SUBGROUP==uram || PRIMITIVE_SUBGROUP==BRAM}]]
current_instance
current_instance {u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory}
set_property src_info {type:SCOPED_XDC file:13 line:3 export:INPUT save:NONE read:READ} [current_design]
set my_var [get_property dram_emb_xdc [get_cells -hier  -filter {PRIMITIVE_SUBGROUP==LUTRAM || PRIMITIVE_SUBGROUP==dram || PRIMITIVE_SUBGROUP==uram || PRIMITIVE_SUBGROUP==BRAM}]]
current_instance
current_instance {u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory}
set_property src_info {type:SCOPED_XDC file:14 line:3 export:INPUT save:NONE read:READ} [current_design]
set my_var [get_property dram_emb_xdc [get_cells -hier  -filter {PRIMITIVE_SUBGROUP==LUTRAM || PRIMITIVE_SUBGROUP==dram || PRIMITIVE_SUBGROUP==uram || PRIMITIVE_SUBGROUP==BRAM}]]
current_instance
current_instance {u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory}
set_property src_info {type:SCOPED_XDC file:15 line:3 export:INPUT save:NONE read:READ} [current_design]
set my_var [get_property dram_emb_xdc [get_cells -hier  -filter {PRIMITIVE_SUBGROUP==LUTRAM || PRIMITIVE_SUBGROUP==dram || PRIMITIVE_SUBGROUP==uram || PRIMITIVE_SUBGROUP==BRAM}]]
current_instance
current_instance {u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory}
set_property src_info {type:SCOPED_XDC file:16 line:3 export:INPUT save:NONE read:READ} [current_design]
set my_var [get_property dram_emb_xdc [get_cells -hier  -filter {PRIMITIVE_SUBGROUP==LUTRAM || PRIMITIVE_SUBGROUP==dram || PRIMITIVE_SUBGROUP==uram || PRIMITIVE_SUBGROUP==BRAM}]]
current_instance
current_instance {u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory}
set_property src_info {type:SCOPED_XDC file:17 line:3 export:INPUT save:NONE read:READ} [current_design]
set my_var [get_property dram_emb_xdc [get_cells -hier  -filter {PRIMITIVE_SUBGROUP==LUTRAM || PRIMITIVE_SUBGROUP==dram || PRIMITIVE_SUBGROUP==uram || PRIMITIVE_SUBGROUP==BRAM}]]
current_instance
current_instance {u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory}
set_property src_info {type:SCOPED_XDC file:18 line:3 export:INPUT save:NONE read:READ} [current_design]
set my_var [get_property dram_emb_xdc [get_cells -hier  -filter {PRIMITIVE_SUBGROUP==LUTRAM || PRIMITIVE_SUBGROUP==dram || PRIMITIVE_SUBGROUP==uram || PRIMITIVE_SUBGROUP==BRAM}]]
