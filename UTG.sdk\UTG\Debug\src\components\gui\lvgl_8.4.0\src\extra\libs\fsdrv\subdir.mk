################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_fatfs.c \
../src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_littlefs.c \
../src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_posix.c \
../src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_stdio.c \
../src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_win32.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_fatfs.o \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_littlefs.o \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_posix.o \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_stdio.o \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_win32.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_fatfs.d \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_littlefs.d \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_posix.d \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_stdio.d \
./src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/lv_fs_win32.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/%.o: ../src/components/gui/lvgl_8.4.0/src/extra/libs/fsdrv/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


