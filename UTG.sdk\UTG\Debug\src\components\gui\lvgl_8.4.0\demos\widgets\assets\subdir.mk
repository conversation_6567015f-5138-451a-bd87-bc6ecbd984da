################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_clothes.c \
../src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_demo_widgets_avatar.c \
../src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_lvgl_logo.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_clothes.o \
./src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_demo_widgets_avatar.o \
./src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_lvgl_logo.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_clothes.d \
./src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_demo_widgets_avatar.d \
./src/components/gui/lvgl_8.4.0/demos/widgets/assets/img_lvgl_logo.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/demos/widgets/assets/%.o: ../src/components/gui/lvgl_8.4.0/demos/widgets/assets/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


