Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
---------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Aug  4 14:38:11 2025
| Host         : wantong running 64-bit major release  (build 9200)
| Command      : report_utilization -file UGT_utilization_placed.rpt -pb UGT_utilization_placed.pb
| Design       : UGT
| Device       : 7z020clg400-2
| Design State : Fully Placed
---------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Slice Logic Distribution
3. Memory
4. DSP
5. IO and GT Specific
6. Clocking
7. Specific Feature
8. Primitives
9. Black Boxes
10. Instantiated Netlists

1. Slice Logic
--------------

+----------------------------+------+-------+-----------+-------+
|          Site Type         | Used | Fixed | Available | Util% |
+----------------------------+------+-------+-----------+-------+
| Slice LUTs                 | 5829 |     0 |     53200 | 10.96 |
|   LUT as Logic             | 5360 |     0 |     53200 | 10.08 |
|   LUT as Memory            |  469 |     0 |     17400 |  2.70 |
|     LUT as Distributed RAM |  260 |     0 |           |       |
|     LUT as Shift Register  |  209 |     0 |           |       |
| Slice Registers            | 9542 |     0 |    106400 |  8.97 |
|   Register as Flip Flop    | 9542 |     0 |    106400 |  8.97 |
|   Register as Latch        |    0 |     0 |    106400 |  0.00 |
| F7 Muxes                   |  220 |     0 |     26600 |  0.83 |
| F8 Muxes                   |   11 |     0 |     13300 |  0.08 |
+----------------------------+------+-------+-----------+-------+


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 9     |          Yes |           - |          Set |
| 67    |          Yes |           - |        Reset |
| 386   |          Yes |         Set |            - |
| 9080  |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Slice Logic Distribution
---------------------------

+--------------------------------------------+------+-------+-----------+-------+
|                  Site Type                 | Used | Fixed | Available | Util% |
+--------------------------------------------+------+-------+-----------+-------+
| Slice                                      | 2994 |     0 |     13300 | 22.51 |
|   SLICEL                                   | 2069 |     0 |           |       |
|   SLICEM                                   |  925 |     0 |           |       |
| LUT as Logic                               | 5360 |     0 |     53200 | 10.08 |
|   using O5 output only                     |    0 |       |           |       |
|   using O6 output only                     | 4228 |       |           |       |
|   using O5 and O6                          | 1132 |       |           |       |
| LUT as Memory                              |  469 |     0 |     17400 |  2.70 |
|   LUT as Distributed RAM                   |  260 |     0 |           |       |
|     using O5 output only                   |    0 |       |           |       |
|     using O6 output only                   |    0 |       |           |       |
|     using O5 and O6                        |  260 |       |           |       |
|   LUT as Shift Register                    |  209 |     0 |           |       |
|     using O5 output only                   |    8 |       |           |       |
|     using O6 output only                   |  150 |       |           |       |
|     using O5 and O6                        |   51 |       |           |       |
| Slice Registers                            | 9542 |     0 |    106400 |  8.97 |
|   Register driven from within the Slice    | 4736 |       |           |       |
|   Register driven from outside the Slice   | 4806 |       |           |       |
|     LUT in front of the register is unused | 4023 |       |           |       |
|     LUT in front of the register is used   |  783 |       |           |       |
| Unique Control Sets                        |  428 |       |     13300 |  3.22 |
+--------------------------------------------+------+-------+-----------+-------+
* Note: Available Control Sets calculated as Slice Registers / 8, Review the Control Sets Report for more information regarding control sets.


3. Memory
---------

+-------------------+------+-------+-----------+-------+
|     Site Type     | Used | Fixed | Available | Util% |
+-------------------+------+-------+-----------+-------+
| Block RAM Tile    |   10 |     0 |       140 |  7.14 |
|   RAMB36/FIFO*    |   10 |     0 |       140 |  7.14 |
|     RAMB36E1 only |   10 |       |           |       |
|   RAMB18          |    0 |     0 |       280 |  0.00 |
+-------------------+------+-------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


4. DSP
------

+-----------+------+-------+-----------+-------+
| Site Type | Used | Fixed | Available | Util% |
+-----------+------+-------+-----------+-------+
| DSPs      |    0 |     0 |       220 |  0.00 |
+-----------+------+-------+-----------+-------+


5. IO and GT Specific
---------------------

+-----------------------------+------+-------+-----------+-------+
|          Site Type          | Used | Fixed | Available | Util% |
+-----------------------------+------+-------+-----------+-------+
| Bonded IOB                  |   54 |    54 |       125 | 43.20 |
|   IOB Master Pads           |   26 |       |           |       |
|   IOB Slave Pads            |   27 |       |           |       |
| Bonded IPADs                |    0 |     0 |         2 |  0.00 |
| Bonded IOPADs               |    0 |     0 |       130 |  0.00 |
| PHY_CONTROL                 |    0 |     0 |         4 |  0.00 |
| PHASER_REF                  |    0 |     0 |         4 |  0.00 |
| OUT_FIFO                    |    0 |     0 |        16 |  0.00 |
| IN_FIFO                     |    0 |     0 |        16 |  0.00 |
| IDELAYCTRL                  |    0 |     0 |         4 |  0.00 |
| IBUFDS                      |    0 |     0 |       121 |  0.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |        16 |  0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |        16 |  0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    0 |     0 |       200 |  0.00 |
| ILOGIC                      |    0 |     0 |       125 |  0.00 |
| OLOGIC                      |    1 |     1 |       125 |  0.80 |
|   OUTFF_ODDR_Register       |    1 |     1 |           |       |
+-----------------------------+------+-------+-----------+-------+


6. Clocking
-----------

+------------+------+-------+-----------+-------+
|  Site Type | Used | Fixed | Available | Util% |
+------------+------+-------+-----------+-------+
| BUFGCTRL   |    4 |     0 |        32 | 12.50 |
| BUFIO      |    0 |     0 |        16 |  0.00 |
| MMCME2_ADV |    1 |     0 |         4 | 25.00 |
| PLLE2_ADV  |    1 |     0 |         4 | 25.00 |
| BUFMRCE    |    0 |     0 |         8 |  0.00 |
| BUFHCE     |    0 |     0 |        72 |  0.00 |
| BUFR       |    1 |     0 |        16 |  6.25 |
+------------+------+-------+-----------+-------+


7. Specific Feature
-------------------

+-------------+------+-------+-----------+-------+
|  Site Type  | Used | Fixed | Available | Util% |
+-------------+------+-------+-----------+-------+
| BSCANE2     |    0 |     0 |         4 |  0.00 |
| CAPTUREE2   |    0 |     0 |         1 |  0.00 |
| DNA_PORT    |    0 |     0 |         1 |  0.00 |
| EFUSE_USR   |    0 |     0 |         1 |  0.00 |
| FRAME_ECCE2 |    0 |     0 |         1 |  0.00 |
| ICAPE2      |    0 |     0 |         2 |  0.00 |
| STARTUPE2   |    0 |     0 |         1 |  0.00 |
| XADC        |    0 |     0 |         1 |  0.00 |
+-------------+------+-------+-----------+-------+


8. Primitives
-------------

+------------+------+----------------------+
|  Ref Name  | Used |  Functional Category |
+------------+------+----------------------+
| FDRE       | 9080 |         Flop & Latch |
| LUT3       | 1827 |                  LUT |
| LUT6       | 1664 |                  LUT |
| LUT5       | 1146 |                  LUT |
| LUT4       |  908 |                  LUT |
| LUT2       |  736 |                  LUT |
| RAMD32     |  390 |   Distributed Memory |
| FDSE       |  386 |         Flop & Latch |
| CARRY4     |  254 |           CarryLogic |
| MUXF7      |  220 |                MuxFx |
| LUT1       |  211 |                  LUT |
| SRL16E     |  177 |   Distributed Memory |
| RAMS32     |  130 |   Distributed Memory |
| BIBUF      |  130 |                   IO |
| SRLC32E    |   83 |   Distributed Memory |
| FDCE       |   67 |         Flop & Latch |
| OBUF       |   34 |                   IO |
| IBUF       |   20 |                   IO |
| MUXF8      |   11 |                MuxFx |
| RAMB36E1   |   10 |         Block Memory |
| FDPE       |    9 |         Flop & Latch |
| OBUFT      |    5 |                   IO |
| BUFG       |    4 |                Clock |
| PS7        |    1 | Specialized Resource |
| PLLE2_ADV  |    1 |                Clock |
| ODDR       |    1 |                   IO |
| MMCME2_ADV |    1 |                Clock |
| BUFR       |    1 |                Clock |
+------------+------+----------------------+


9. Black Boxes
--------------

+----------+------+
| Ref Name | Used |
+----------+------+


10. Instantiated Netlists
-------------------------

+-----------+------+
|  Ref Name | Used |
+-----------+------+
| clk_wiz_0 |    1 |
+-----------+------+


