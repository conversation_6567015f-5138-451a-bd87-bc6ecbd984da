# 
# Synthesis run script generated by <PERSON><PERSON>
# 

set TIME_start [clock seconds] 
proc create_report { reportName command } {
  set status "."
  append status $reportName ".fail"
  if { [file exists $status] } {
    eval file delete [glob $status]
  }
  send_msg_id runtcl-4 info "Executing : $command"
  set retval [eval catch { $command } msg]
  if { $retval != 0 } {
    set fp [open $status w]
    close $fp
    send_msg_id runtcl-5 warning "$msg"
  }
}
set_param xicom.use_bs_reader 1
set_msg_config  -id {Ipptcl 7-5}  -suppress 
create_project -in_memory -part xc7z020clg400-2

set_param project.singleFileAddWarning.threshold 0
set_param project.compositeFile.enableAutoGeneration 0
set_param synth.vivado.isSynthRun true
set_msg_config -source 4 -id {IP_Flow 19-2162} -severity warning -new_severity info
set_property webtalk.parent_dir F:/UTG/UTG/UTG.cache/wt [current_project]
set_property parent.project_path F:/UTG/UTG/UTG.xpr [current_project]
set_property XPM_LIBRARIES {XPM_CDC XPM_FIFO XPM_MEMORY} [current_project]
set_property default_lib xil_defaultlib [current_project]
set_property target_language Verilog [current_project]
set_property ip_repo_paths f:/UTG/UTG/ip_repo [current_project]
update_ip_catalog
set_property ip_output_repo f:/UTG/UTG/UTG.cache/ip [current_project]
set_property ip_cache_permissions {read write} [current_project]
read_verilog -library xil_defaultlib {
  F:/UTG/UTG/UTG.srcs/sources_1/bd/utg/hdl/utg_wrapper.v
  F:/UTG/UTG/UTG.srcs/sources_1/imports/UTG/Top_Module.v
}
add_files F:/UTG/UTG/UTG.srcs/sources_1/bd/utg/utg.bd
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_vdma_0_0/utg_axi_vdma_0_0.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_vdma_0_0/utg_axi_vdma_0_0_clocks.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_vdma_0_0/utg_axi_vdma_0_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_axi4s_vid_out_0_2/utg_v_axi4s_vid_out_0_2_clocks.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_axi4s_vid_out_0_2/utg_v_axi4s_vid_out_0_2_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_tc_0_1/utg_v_tc_0_1_clocks.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_tc_0_1/utg_v_tc_0_1_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_xbar_1/utg_xbar_1_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_1/utg_rst_ps7_0_100M_1_board.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_1/utg_rst_ps7_0_100M_1.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_1/utg_rst_ps7_0_100M_1_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_1/bd_64d3_psr_aclk_0_board.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_1/bd_64d3_psr_aclk_0.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_5/bd_64d3_s00a2s_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_6/bd_64d3_sarn_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_7/bd_64d3_srn_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_8/bd_64d3_m00s2a_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_1/bd_a552_psr_aclk_0_board.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_1/bd_a552_psr_aclk_0.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_5/bd_a552_s00a2s_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_6/bd_a552_sarn_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_7/bd_a552_srn_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_8/bd_a552_sawn_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_9/bd_a552_swn_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_10/bd_a552_sbn_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_11/bd_a552_m00s2a_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_0/utg_rst_ps7_0_100M_0_board.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_0/utg_rst_ps7_0_100M_0.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_0/utg_rst_ps7_0_100M_0_ooc.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_auto_pc_0/utg_auto_pc_0_ooc.xdc]
set_property used_in_implementation false [get_files -all F:/UTG/UTG/UTG.srcs/sources_1/bd/utg/utg_ooc.xdc]

read_ip -quiet F:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xci
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_board.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc]
set_property used_in_implementation false [get_files -all f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_ooc.xdc]

# Mark all dcp files as not used in implementation to prevent them from being
# stitched into the results of this synthesis run. Any black boxes in the
# design are intentionally left as such for best results. Dcp files will be
# stitched into the design at a later time, either when this synthesis run is
# opened, or when it is stitched into a dependent implementation run.
foreach dcp [get_files -quiet -all -filter file_type=="Design\ Checkpoint"] {
  set_property used_in_implementation false $dcp
}
read_xdc F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_timing.xdc
set_property used_in_implementation false [get_files F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_timing.xdc]

read_xdc F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_pins.xdc
set_property used_in_implementation false [get_files F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_pins.xdc]

read_xdc dont_touch.xdc
set_property used_in_implementation false [get_files dont_touch.xdc]
set_param ips.enableIPCacheLiteLoad 1
close [open __synthesis_is_running__ w]

synth_design -top UGT -part xc7z020clg400-2


# disable binary constraint mode for synth run checkpoints
set_param constraints.enableBinaryConstraints false
write_checkpoint -force -noxdef UGT.dcp
create_report "synth_1_synth_report_utilization_0" "report_utilization -file UGT_utilization_synth.rpt -pb UGT_utilization_synth.pb"
file delete __synthesis_is_running__
close [open __synthesis_is_complete__ w]
