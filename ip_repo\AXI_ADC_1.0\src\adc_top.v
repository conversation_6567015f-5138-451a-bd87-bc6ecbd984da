
	`timescale 1 ns / 1 ps
	module adc_top
	(
		input rst_n,
		//s00_axi_aclk 时钟
		input sys_clk,
		input adc_fifo_rst,
		input adc_start,
		input adc_powerdown,
		input [24 : 0] adc_sample_num,
	
		//ADC_DCLK 时钟
		output 	reg			 fifo_rst,
		output				 fifo_wr_clk,
		output  reg [15 : 0] fifo_wr_data,
		output  reg 		 fifo_wr_en,
		input				 fifo_wr_rst_busy,
		output  wire [31 : 0] adc_clk_cnts_dbg,//this is for debug
		//adc signal Interface
		input ADC_OR,//
		input ADC_DCLK,
		input [11:0] ADC_DATA,
		output wire ADC_PDWN,// 1,power down; 0, normal
		output wire ADC_SP_CLK,//sample clk
		output reg ULTRASOUND_PLUS
	 );
	 
reg [11:0] adc_sp_data; 
reg [24:0] adc_sp_cnts;
reg [24:0] adc_sample_num_d1;
reg [15:0] adc_clk_cnts;

reg	   adc_start_d1; 
reg	   adc_start_d2; 
reg    adc_en;
wire   adc_start_pos;

reg [8:0]  ultrasound_div_cnts;
reg 	   ultrasound_st;

assign adc_start_pos = ((!adc_start_d2) & adc_start_d1);//rising edge



assign fifo_wr_clk = ADC_DCLK;
assign ADC_SP_CLK = sys_clk;
//assign ADC_PDWN = adc_powerdown;// 1,powerdown; 0,normal
assign ADC_PDWN = ultrasound_st? 1'b0 : adc_powerdown;// 1,powerdown; 0,normal

assign adc_clk_cnts_dbg = {16'd0,adc_clk_cnts};

 always @(posedge ADC_DCLK)
 begin
  fifo_rst <= adc_fifo_rst;
 end
 
 always @(posedge ADC_DCLK)
 begin	
	 if(adc_start_pos)
		begin
			adc_clk_cnts <=  16'd0;
		end
	else if(fifo_wr_en)
		begin			
			adc_clk_cnts <= adc_clk_cnts + 16'd1;
		end
 end


 always @(posedge ADC_DCLK or negedge rst_n)
 begin
	if(!rst_n) 
		begin
			adc_start_d1 <= 1'b0;
			adc_start_d2 <= 1'b0;
			adc_sample_num_d1 <= 25'd0;
		end
	else
		begin
			adc_start_d1 <= adc_start;
			adc_start_d2 <= adc_start_d1;
			
			adc_sample_num_d1 <= adc_sample_num ;//+ 32'd32;
		end
end


 always @(posedge ADC_DCLK or negedge rst_n)
 begin
	if(!rst_n)
		begin
		adc_sp_cnts <=25'd0;
		adc_en <= 1'b0;
		end
	else if(~adc_en) 
		begin
		adc_sp_cnts <= 25'd1;
		adc_en <= adc_start_pos & (~fifo_wr_rst_busy);
		end
	else 
		begin
		if(adc_sp_cnts >=adc_sample_num_d1)//Sample one more 
			begin
			adc_sp_cnts <= adc_sp_cnts;
			adc_en <= 1'b0;	
			end
		else
			begin
			adc_sp_cnts <= adc_sp_cnts + 25'd1;
			adc_en <= 1'b1;
			end
		end
 end
 

//risedge sample data
always @(posedge ADC_DCLK)
 begin
	 adc_sp_data <= ADC_DATA;
 end


always @(posedge ADC_DCLK or negedge rst_n)
 begin
 if(!rst_n) 
		begin
			fifo_wr_data <= 16'd0;
			fifo_wr_en <= 1'b0;
		end
else
	begin
			fifo_wr_data <={4'b0000,adc_sp_data};
			fifo_wr_en <= adc_en;
	end
end

// ultrasound puls signal gen

 always @(posedge ADC_DCLK or negedge rst_n)
 begin	
	 if(!rst_n) 
		begin
			ultrasound_div_cnts <= 9'h1ff;
			ultrasound_st <= 1'b0;
		end
	else if(adc_start_pos)
		begin			
			ultrasound_div_cnts <= 9'd0;
			ultrasound_st<=1'b1;
		end
	else if(ultrasound_div_cnts<9'h1ff)
		begin
			ultrasound_div_cnts <= ultrasound_div_cnts + 9'd1;
			ultrasound_st<=1'b1;
		end
	else
		begin
		ultrasound_div_cnts <= ultrasound_div_cnts;
		ultrasound_st<=1'b0;
		end
 end
 
 always @(posedge ADC_DCLK)
 begin	
	if(ultrasound_div_cnts<=9'd49)
		begin
			ULTRASOUND_PLUS <= 1'b1;
		end
	else if(ultrasound_div_cnts<=9'd99)
		begin
			ULTRASOUND_PLUS <= 1'b0;
		end
	else if(ultrasound_div_cnts<=9'd149)
		begin
			ULTRASOUND_PLUS <= 1'b1;
		end
	else if(ultrasound_div_cnts<=9'd199)
		begin
			ULTRASOUND_PLUS <= 1'b0;
		end
	else if(ultrasound_div_cnts<=9'd249)
		begin
			ULTRASOUND_PLUS <= 1'b1;
		end
	else if(ultrasound_div_cnts<=9'd299)
		begin
			ULTRASOUND_PLUS <= 1'b0;
		end
	else if(ultrasound_div_cnts<=9'd349)
		begin
			ULTRASOUND_PLUS <= 1'b1;
		end
	else if(ultrasound_div_cnts<=9'd399)
		begin
			ULTRASOUND_PLUS <= 1'b0;
		end
	else if(ultrasound_div_cnts<=9'd449)
		begin
			ULTRASOUND_PLUS <= 1'b1;
		end
	else
		begin
			ULTRASOUND_PLUS <= 1'b0;
		end
 end
 
 
endmodule
 