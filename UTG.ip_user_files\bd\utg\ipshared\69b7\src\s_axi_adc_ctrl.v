
module s_axi_adc_ctrl(
    input   wire      sys_clk        ,  
    input   wire      sys_rst_n      ,  
    input   wire [31:0] slv_in_reg0       , 
	input   wire [31:0] slv_in_reg1       , 
	input   wire [31:0] slv_in_reg2       , 
	input   wire [31:0] slv_in_reg3       , 
	input   wire [31:0] slv_in_reg4       , 
	input   wire [31:0] slv_in_reg5       , 
	input   wire [31:0] slv_in_reg6       , 
	input   wire [31:0] slv_in_reg7       , 
	input   wire [31:0] slv_in_reg8       , 
	input   wire [31:0] slv_in_reg9       , 
	input   wire [31:0] slv_in_reg10       , 
	input   wire [31:0] slv_in_reg11       , 
	input   wire [31:0] slv_in_reg12       , 
	input   wire [31:0] slv_in_reg13       , 
	input   wire [31:0] slv_in_reg14       , 
	input   wire [31:0] slv_in_reg15       , 
	
	output   wire [31:0] slv_out_reg0     , 
	output   wire [31:0] slv_out_reg1     , 
	output   wire [31:0] slv_out_reg2     , 
	output   wire [31:0] slv_out_reg3     , 
	output   wire [31:0] slv_out_reg4     , 
	output   wire [31:0] slv_out_reg5     , 
	output   wire [31:0] slv_out_reg6     , 
	output   wire [31:0] slv_out_reg7     , 
	output   wire [31:0] slv_out_reg8     , 
	output   wire [31:0] slv_out_reg9     , 
	output   wire [31:0] slv_out_reg10     , 
	output   wire [31:0] slv_out_reg11     , 
	output   wire [31:0] slv_out_reg12     , 
	output   wire [31:0] slv_out_reg13     , 
	output   wire [31:0] slv_out_reg14     , 
	output   wire [31:0] slv_out_reg15     , 
	
	output  wire  		adc_start,  //start adc 
	output  wire		adc_powerdown,
	output  wire [24:0] adc_sample_count,
	output  wire  		adc_fifo_rst,
	input   wire [31:0]	adc_dbg,
    
	
	input   wire [31:0]	fifo_wr_data_count,//for debug
	input   wire [31:0]	fifo_rd_data_count,
	input   wire [31:0]  fifo_status,
	input	wire[31:0]	s2mm_transfer_bytes,//
	input   wire[31:0]  s2mm_transfer_status,
	output	wire[31:0]  s2mm_transfer_base_addr
	
    
);

//*****************************************************
//**                  rtl code
//*****************************************************



assign adc_start = slv_in_reg0[0];
assign adc_fifo_rst = slv_in_reg1[0];
assign adc_powerdown = slv_in_reg2[0];// 0,active,1,not active
assign adc_sample_count = slv_in_reg3[24:0];//limit max count to 0x01FF_FFFF
assign s2mm_transfer_base_addr = slv_in_reg5;


assign slv_out_reg0 = slv_in_reg0;
assign slv_out_reg1 = slv_in_reg1;
assign slv_out_reg2 = slv_in_reg2;
assign slv_out_reg3 = {7'd0,slv_in_reg3[24:0]};//limit max count to 0x01FF_FFFF
assign slv_out_reg4 = adc_dbg;

assign slv_out_reg5 = slv_in_reg5;
assign slv_out_reg6 = s2mm_transfer_bytes;//
assign slv_out_reg7 = s2mm_transfer_status;
assign slv_out_reg8 = fifo_wr_data_count;
assign slv_out_reg9 = fifo_rd_data_count;
assign slv_out_reg10 = fifo_status;
assign slv_out_reg11 = slv_in_reg11;
assign slv_out_reg12 = slv_in_reg12;
assign slv_out_reg13 = slv_in_reg13;
assign slv_out_reg14 = slv_in_reg14;
assign slv_out_reg15 = slv_in_reg15;
endmodule
