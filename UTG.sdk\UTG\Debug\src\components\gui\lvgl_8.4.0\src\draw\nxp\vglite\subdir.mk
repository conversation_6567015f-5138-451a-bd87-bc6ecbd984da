################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite.c \
../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_arc.c \
../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_blend.c \
../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_line.c \
../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_rect.c \
../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_vglite_buf.c \
../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_vglite_utils.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite.o \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_arc.o \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_blend.o \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_line.o \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_rect.o \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_vglite_buf.o \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_vglite_utils.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite.d \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_arc.d \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_blend.d \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_line.d \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_draw_vglite_rect.d \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_vglite_buf.d \
./src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/lv_vglite_utils.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/%.o: ../src/components/gui/lvgl_8.4.0/src/draw/nxp/vglite/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


