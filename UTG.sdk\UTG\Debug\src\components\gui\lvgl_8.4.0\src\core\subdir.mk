################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/src/core/lv_disp.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_event.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_group.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_indev.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_indev_scroll.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj_class.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj_draw.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj_pos.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj_scroll.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj_style.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj_style_gen.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_obj_tree.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_refr.c \
../src/components/gui/lvgl_8.4.0/src/core/lv_theme.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/src/core/lv_disp.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_event.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_group.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_indev.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_indev_scroll.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_class.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_draw.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_pos.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_scroll.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_style.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_style_gen.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_tree.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_refr.o \
./src/components/gui/lvgl_8.4.0/src/core/lv_theme.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/src/core/lv_disp.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_event.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_group.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_indev.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_indev_scroll.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_class.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_draw.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_pos.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_scroll.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_style.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_style_gen.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_obj_tree.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_refr.d \
./src/components/gui/lvgl_8.4.0/src/core/lv_theme.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/src/core/%.o: ../src/components/gui/lvgl_8.4.0/src/core/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


