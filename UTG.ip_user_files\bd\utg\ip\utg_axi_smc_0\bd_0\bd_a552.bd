{"design": {"design_info": {"boundary_crc": "0x604098B03DB75720", "design_src": "SBD", "device": "xc7z020clg400-2", "name": "bd_a552", "scoped": "true", "synth_flow_mode": "None", "tool_version": "2018.3", "validated": "true"}, "design_tree": {"clk_map": {"one": "", "psr_aclk": ""}, "s00_entry_pipeline": {"s00_mmu": "", "s00_transaction_regulator": "", "s00_si_converter": ""}, "s00_axi2sc": "", "s00_nodes": {"s00_ar_node": "", "s00_r_node": "", "s00_aw_node": "", "s00_w_node": "", "s00_b_node": ""}, "m00_sc2axi": "", "m00_exit_pipeline": {"m00_exit": ""}}, "interface_ports": {"S00_AXI": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0", "parameters": {"ADDR_WIDTH": {"value": "32", "value_src": "auto_prop"}, "ARUSER_WIDTH": {"value": "0", "value_src": "auto_prop"}, "AWUSER_WIDTH": {"value": "0", "value_src": "auto_prop"}, "BUSER_WIDTH": {"value": "0", "value_src": "auto_prop"}, "CLK_DOMAIN": {"value": "utg_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop"}, "DATA_WIDTH": {"value": "32", "value_src": "auto_prop"}, "FREQ_HZ": {"value": "100000000", "value_src": "user_prop"}, "HAS_BRESP": {"value": "1", "value_src": "const_prop"}, "HAS_BURST": {"value": "1", "value_src": "const_prop"}, "HAS_CACHE": {"value": "1", "value_src": "const_prop"}, "HAS_LOCK": {"value": "1", "value_src": "const_prop"}, "HAS_PROT": {"value": "1", "value_src": "const_prop"}, "HAS_QOS": {"value": "1", "value_src": "const_prop"}, "HAS_REGION": {"value": "0", "value_src": "constant"}, "HAS_RRESP": {"value": "1", "value_src": "const_prop"}, "HAS_WSTRB": {"value": "1", "value_src": "const_prop"}, "ID_WIDTH": {"value": "1", "value_src": "auto_prop"}, "INSERT_VIP": {"value": "0", "value_src": "default"}, "MAX_BURST_LENGTH": {"value": "256", "value_src": "default"}, "NUM_READ_OUTSTANDING": {"value": "2"}, "NUM_READ_THREADS": {"value": "1", "value_src": "default"}, "NUM_WRITE_OUTSTANDING": {"value": "16"}, "NUM_WRITE_THREADS": {"value": "1", "value_src": "default"}, "PHASE": {"value": "0.000", "value_src": "default"}, "PROTOCOL": {"value": "AXI4", "value_src": "const_prop"}, "READ_WRITE_MODE": {"value": "READ_WRITE", "value_src": "const_prop"}, "RUSER_BITS_PER_BYTE": {"value": "0", "value_src": "default"}, "RUSER_WIDTH": {"value": "0", "value_src": "auto_prop"}, "SUPPORTS_NARROW_BURST": {"value": "0", "value_src": "const_prop"}, "WUSER_BITS_PER_BYTE": {"value": "0", "value_src": "default"}, "WUSER_WIDTH": {"value": "0", "value_src": "auto_prop"}}}, "M00_AXI": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0", "parameters": {"ADDR_WIDTH": {"value": "32", "value_src": "default"}, "ARUSER_WIDTH": {"value": "0", "value_src": "default"}, "AWUSER_WIDTH": {"value": "0", "value_src": "default"}, "BUSER_WIDTH": {"value": "0", "value_src": "default"}, "CLK_DOMAIN": {"value": "utg_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop"}, "DATA_WIDTH": {"value": "64", "value_src": "ip"}, "FREQ_HZ": {"value": "100000000", "value_src": "user_prop"}, "HAS_BRESP": {"value": "1", "value_src": "default"}, "HAS_BURST": {"value": "1", "value_src": "default"}, "HAS_CACHE": {"value": "1", "value_src": "default"}, "HAS_LOCK": {"value": "1", "value_src": "default"}, "HAS_PROT": {"value": "1", "value_src": "default"}, "HAS_QOS": {"value": "1", "value_src": "default"}, "HAS_REGION": {"value": "0", "value_src": "constant"}, "HAS_RRESP": {"value": "1", "value_src": "default"}, "HAS_WSTRB": {"value": "1", "value_src": "default"}, "ID_WIDTH": {"value": "0", "value_src": "default"}, "INSERT_VIP": {"value": "0", "value_src": "default"}, "MAX_BURST_LENGTH": {"value": "16"}, "NUM_READ_OUTSTANDING": {"value": "2"}, "NUM_READ_THREADS": {"value": "1", "value_src": "default"}, "NUM_WRITE_OUTSTANDING": {"value": "16"}, "NUM_WRITE_THREADS": {"value": "1", "value_src": "default"}, "PHASE": {"value": "0.000", "value_src": "default"}, "PROTOCOL": {"value": "AXI3", "value_src": "ip"}, "READ_WRITE_MODE": {"value": "READ_WRITE", "value_src": "default"}, "RUSER_BITS_PER_BYTE": {"value": "0"}, "RUSER_WIDTH": {"value": "0", "value_src": "default"}, "SUPPORTS_NARROW_BURST": {"value": "0"}, "WUSER_BITS_PER_BYTE": {"value": "0"}, "WUSER_WIDTH": {"value": "0", "value_src": "default"}}}}, "ports": {"aclk": {"type": "clk", "direction": "I", "parameters": {"ASSOCIATED_BUSIF": {"value": "M00_AXI:S00_AXI"}, "ASSOCIATED_CLKEN": {"value": "s_sc_aclken", "value_src": "constant"}, "CLK_DOMAIN": {"value": "utg_processing_system7_0_0_FCLK_CLK0", "value_src": "default_prop"}, "FREQ_HZ": {"value": "100000000", "value_src": "user_prop"}, "INSERT_VIP": {"value": "0", "value_src": "default"}, "PHASE": {"value": "0.000", "value_src": "default"}}}, "aresetn": {"type": "rst", "direction": "I", "parameters": {"INSERT_VIP": {"value": "0", "value_src": "default"}, "POLARITY": {"value": "ACTIVE_LOW"}}}}, "components": {"clk_map": {"ports": {"S00_ACLK": {"type": "clk", "direction": "O"}, "S00_ARESETN": {"type": "rst", "direction": "O", "left": "0", "right": "0"}, "M00_ACLK": {"type": "clk", "direction": "O"}, "M00_ARESETN": {"type": "rst", "direction": "O", "left": "0", "right": "0"}, "swbd_aclk": {"type": "clk", "direction": "O"}, "swbd_aresetn": {"type": "rst", "direction": "O", "left": "0", "right": "0"}, "aresetn": {"type": "rst", "direction": "I"}, "aresetn_out": {"type": "rst", "direction": "O"}, "aclk": {"type": "clk", "direction": "I"}}, "components": {"one": {"vlnv": "xilinx.com:ip:xlconstant:1.1", "xci_name": "bd_a552_one_0"}, "psr_aclk": {"vlnv": "xilinx.com:ip:proc_sys_reset:5.0", "xci_name": "bd_a552_psr_aclk_0", "parameters": {"C_AUX_RESET_HIGH": {"value": "0"}, "C_AUX_RST_WIDTH": {"value": "1"}}}}, "nets": {"clk_map_aresetn_net": {"ports": ["aresetn", "psr_aclk/aux_reset_in"]}, "clk_map_aclk_net": {"ports": ["aclk", "S00_ACLK", "M00_ACLK", "swbd_aclk", "psr_aclk/slowest_sync_clk"]}, "psr_aclk_interconnect_aresetn": {"ports": ["psr_aclk/interconnect_aresetn", "S00_ARESETN", "M00_ARESETN", "swbd_aresetn"]}, "one_dout": {"ports": ["one/dout", "psr_aclk/ext_reset_in"]}}}, "s00_entry_pipeline": {"interface_ports": {"s_axi": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "m_axi": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"aclk": {"type": "clk", "direction": "I"}, "aresetn": {"type": "rst", "direction": "I"}}, "components": {"s00_mmu": {"vlnv": "xilinx.com:ip:sc_mmu:1.0", "xci_name": "bd_a552_s00mmu_0", "parameters": {"ADDR_WIDTH": {"value": "32"}, "ID_WIDTH": {"value": "1"}, "IS_CASCADED": {"value": "0"}, "MSC000_ROUTE": {"value": "0b1"}, "MSC_ROUTE_WIDTH": {"value": "1"}, "NUM_MSC": {"value": "1"}, "NUM_READ_OUTSTANDING": {"value": "2"}, "NUM_SEG": {"value": "1"}, "NUM_WRITE_OUTSTANDING": {"value": "16"}, "RDATA_WIDTH": {"value": "32"}, "READ_WRITE_MODE": {"value": "READ_WRITE"}, "SEG000_BASE_ADDR": {"value": "0x0000000010000000"}, "SEG000_SECURE_READ": {"value": "0"}, "SEG000_SECURE_WRITE": {"value": "0"}, "SEG000_SEP_ROUTE": {"value": "0b0000000000000000000000000000000000000000000000000000000000000000"}, "SEG000_SIZE": {"value": "28"}, "SEG000_SUPPORTS_READ": {"value": "1"}, "SEG000_SUPPORTS_WRITE": {"value": "1"}, "SUPPORTS_NARROW": {"value": "0"}, "S_ARUSER_WIDTH": {"value": "0"}, "S_AWUSER_WIDTH": {"value": "0"}, "S_BUSER_WIDTH": {"value": "0"}, "S_PROTOCOL": {"value": "AXI4"}, "S_RUSER_WIDTH": {"value": "0"}, "S_WUSER_WIDTH": {"value": "0"}, "WDATA_WIDTH": {"value": "32"}}}, "s00_transaction_regulator": {"vlnv": "xilinx.com:ip:sc_transaction_regulator:1.0", "xci_name": "bd_a552_s00tr_0", "parameters": {"ADDR_WIDTH": {"value": "32"}, "IS_CASCADED": {"value": "0"}, "MEP_IDENTIFIER": {"value": "0"}, "MEP_IDENTIFIER_WIDTH": {"value": "1"}, "NUM_READ_OUTSTANDING": {"value": "2"}, "NUM_WRITE_OUTSTANDING": {"value": "16"}, "RDATA_WIDTH": {"value": "32"}, "READ_WRITE_MODE": {"value": "READ_WRITE"}, "SEP_ROUTE_WIDTH": {"value": "1"}, "SUPPORTS_READ_DEADLOCK": {"value": "0"}, "SUPPORTS_WRITE_DEADLOCK": {"value": "0"}, "S_ID_WIDTH": {"value": "1"}, "WDATA_WIDTH": {"value": "32"}}}, "s00_si_converter": {"vlnv": "xilinx.com:ip:sc_si_converter:1.0", "xci_name": "bd_a552_s00sic_0", "parameters": {"ADDR_WIDTH": {"value": "32"}, "HAS_BURST": {"value": "1"}, "ID_WIDTH": {"value": "1"}, "IS_CASCADED": {"value": "0"}, "LIMIT_READ_LENGTH": {"value": "0"}, "LIMIT_WRITE_LENGTH": {"value": "0"}, "MAX_RUSER_BITS_PER_BYTE": {"value": "0"}, "MAX_WUSER_BITS_PER_BYTE": {"value": "0"}, "MEP_IDENTIFIER_WIDTH": {"value": "1"}, "MSC000_RDATA_WIDTH": {"value": "64"}, "MSC000_WDATA_WIDTH": {"value": "64"}, "NUM_MSC": {"value": "1"}, "NUM_READ_OUTSTANDING": {"value": "2"}, "NUM_READ_THREADS": {"value": "1"}, "NUM_SEG": {"value": "1"}, "NUM_WRITE_OUTSTANDING": {"value": "16"}, "NUM_WRITE_THREADS": {"value": "1"}, "RDATA_WIDTH": {"value": "32"}, "READ_WRITE_MODE": {"value": "READ_WRITE"}, "SEP000_PROTOCOL": {"value": "AXI3"}, "SEP000_RDATA_WIDTH": {"value": "64"}, "SEP000_WDATA_WIDTH": {"value": "64"}, "SUPPORTS_NARROW": {"value": "0"}, "S_RUSER_BITS_PER_BYTE": {"value": "0"}, "S_WUSER_BITS_PER_BYTE": {"value": "0"}, "WDATA_WIDTH": {"value": "32"}}}}, "interface_nets": {"s_axi_1": {"interface_ports": ["s_axi", "s00_mmu/S_AXI"]}, "s00_transaction_regulator_M_AXI": {"interface_ports": ["s00_transaction_regulator/M_AXI", "s00_si_converter/S_AXI"]}, "s00_si_converter_M_AXI": {"interface_ports": ["m_axi", "s00_si_converter/M_AXI"]}, "s00_mmu_M_AXI": {"interface_ports": ["s00_mmu/M_AXI", "s00_transaction_regulator/S_AXI"]}}, "nets": {"aclk_1": {"ports": ["aclk", "s00_mmu/aclk", "s00_si_converter/aclk", "s00_transaction_regulator/aclk"]}, "aresetn_1": {"ports": ["aresetn", "s00_mmu/aresetn", "s00_si_converter/aresetn", "s00_transaction_regulator/aresetn"]}}}, "s00_axi2sc": {"vlnv": "xilinx.com:ip:sc_axi2sc:1.0", "xci_name": "bd_a552_s00a2s_0", "parameters": {"AXI_ADDR_WIDTH": {"value": "32"}, "AXI_ID_WIDTH": {"value": "1"}, "AXI_RDATA_WIDTH": {"value": "32"}, "AXI_WDATA_WIDTH": {"value": "32"}, "MSC_ROUTE_WIDTH": {"value": "1"}, "READ_WRITE_MODE": {"value": "READ_WRITE"}, "SC_ADDR_WIDTH": {"value": "32"}, "SC_ARUSER_WIDTH": {"value": "0"}, "SC_AWUSER_WIDTH": {"value": "0"}, "SC_BUSER_WIDTH": {"value": "0"}, "SC_ID_WIDTH": {"value": "1"}, "SC_RDATA_WIDTH": {"value": "64"}, "SC_RUSER_BITS_PER_BYTE": {"value": "0"}, "SC_WDATA_WIDTH": {"value": "64"}, "SC_WUSER_BITS_PER_BYTE": {"value": "0"}, "SSC_ROUTE_WIDTH": {"value": "1"}}}, "s00_nodes": {"interface_ports": {"M_SC_AR": {"mode": "Master", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "S_SC_AR": {"mode": "Slave", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "M_SC_R": {"mode": "Master", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "S_SC_R": {"mode": "Slave", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "M_SC_AW": {"mode": "Master", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "S_SC_AW": {"mode": "Slave", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "M_SC_W": {"mode": "Master", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "S_SC_W": {"mode": "Slave", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "M_SC_B": {"mode": "Master", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}, "S_SC_B": {"mode": "Slave", "vlnv": "xilinx.com:interface:sc_rtl:1.0"}}, "ports": {"s_sc_clk": {"type": "clk", "direction": "I"}, "s_sc_resetn": {"type": "rst", "direction": "I"}, "m_sc_clk": {"type": "clk", "direction": "I"}, "m_sc_resetn": {"type": "rst", "direction": "I"}}, "components": {"s00_ar_node": {"vlnv": "xilinx.com:ip:sc_node:1.0", "xci_name": "bd_a552_sarn_0", "parameters": {"ACLKEN_CONVERSION": {"value": "0"}, "ACLK_RELATIONSHIP": {"value": "1"}, "ADDR_WIDTH": {"value": "32"}, "CHANNEL": {"value": "2"}, "ID_WIDTH": {"value": "1"}, "M00_NUM_BYTES": {"value": "8"}, "M01_NUM_BYTES": {"value": "8"}, "M02_NUM_BYTES": {"value": "8"}, "M03_NUM_BYTES": {"value": "8"}, "M04_NUM_BYTES": {"value": "8"}, "M05_NUM_BYTES": {"value": "8"}, "M06_NUM_BYTES": {"value": "8"}, "M07_NUM_BYTES": {"value": "8"}, "M08_NUM_BYTES": {"value": "8"}, "M09_NUM_BYTES": {"value": "8"}, "M10_NUM_BYTES": {"value": "8"}, "M11_NUM_BYTES": {"value": "8"}, "M12_NUM_BYTES": {"value": "8"}, "M13_NUM_BYTES": {"value": "8"}, "M14_NUM_BYTES": {"value": "8"}, "M15_NUM_BYTES": {"value": "8"}, "MAX_PAYLD_BYTES": {"value": "8"}, "M_SEND_PIPELINE": {"value": "0"}, "NUM_MI": {"value": "1"}, "NUM_OUTSTANDING": {"value": "2"}, "NUM_SI": {"value": "1"}, "PAYLD_WIDTH": {"value": "138"}, "S00_NUM_BYTES": {"value": "4"}, "S01_NUM_BYTES": {"value": "4"}, "S02_NUM_BYTES": {"value": "4"}, "S03_NUM_BYTES": {"value": "4"}, "S04_NUM_BYTES": {"value": "4"}, "S05_NUM_BYTES": {"value": "4"}, "S06_NUM_BYTES": {"value": "4"}, "S07_NUM_BYTES": {"value": "4"}, "S08_NUM_BYTES": {"value": "4"}, "S09_NUM_BYTES": {"value": "4"}, "S10_NUM_BYTES": {"value": "4"}, "S11_NUM_BYTES": {"value": "4"}, "S12_NUM_BYTES": {"value": "4"}, "S13_NUM_BYTES": {"value": "4"}, "S14_NUM_BYTES": {"value": "4"}, "S15_NUM_BYTES": {"value": "4"}, "SC_ROUTE_WIDTH": {"value": "1"}, "USER_WIDTH": {"value": "0"}}}, "s00_r_node": {"vlnv": "xilinx.com:ip:sc_node:1.0", "xci_name": "bd_a552_srn_0", "parameters": {"ACLKEN_CONVERSION": {"value": "0"}, "ACLK_RELATIONSHIP": {"value": "1"}, "ADDR_WIDTH": {"value": "32"}, "CHANNEL": {"value": "0"}, "ID_WIDTH": {"value": "1"}, "M00_NUM_BYTES": {"value": "4"}, "M01_NUM_BYTES": {"value": "4"}, "M02_NUM_BYTES": {"value": "4"}, "M03_NUM_BYTES": {"value": "4"}, "M04_NUM_BYTES": {"value": "4"}, "M05_NUM_BYTES": {"value": "4"}, "M06_NUM_BYTES": {"value": "4"}, "M07_NUM_BYTES": {"value": "4"}, "M08_NUM_BYTES": {"value": "4"}, "M09_NUM_BYTES": {"value": "4"}, "M10_NUM_BYTES": {"value": "4"}, "M11_NUM_BYTES": {"value": "4"}, "M12_NUM_BYTES": {"value": "4"}, "M13_NUM_BYTES": {"value": "4"}, "M14_NUM_BYTES": {"value": "4"}, "M15_NUM_BYTES": {"value": "4"}, "MAX_PAYLD_BYTES": {"value": "8"}, "M_SEND_PIPELINE": {"value": "0"}, "NUM_MI": {"value": "1"}, "NUM_OUTSTANDING": {"value": "2"}, "NUM_SI": {"value": "1"}, "PAYLD_WIDTH": {"value": "83"}, "S00_NUM_BYTES": {"value": "8"}, "S01_NUM_BYTES": {"value": "8"}, "S02_NUM_BYTES": {"value": "8"}, "S03_NUM_BYTES": {"value": "8"}, "S04_NUM_BYTES": {"value": "8"}, "S05_NUM_BYTES": {"value": "8"}, "S06_NUM_BYTES": {"value": "8"}, "S07_NUM_BYTES": {"value": "8"}, "S08_NUM_BYTES": {"value": "8"}, "S09_NUM_BYTES": {"value": "8"}, "S10_NUM_BYTES": {"value": "8"}, "S11_NUM_BYTES": {"value": "8"}, "S12_NUM_BYTES": {"value": "8"}, "S13_NUM_BYTES": {"value": "8"}, "S14_NUM_BYTES": {"value": "8"}, "S15_NUM_BYTES": {"value": "8"}, "SC_ROUTE_WIDTH": {"value": "1"}, "USER_BITS_PER_BYTE": {"value": "0"}}}, "s00_aw_node": {"vlnv": "xilinx.com:ip:sc_node:1.0", "xci_name": "bd_a552_sawn_0", "parameters": {"ACLKEN_CONVERSION": {"value": "0"}, "ACLK_RELATIONSHIP": {"value": "1"}, "ADDR_WIDTH": {"value": "32"}, "CHANNEL": {"value": "3"}, "ID_WIDTH": {"value": "1"}, "M00_NUM_BYTES": {"value": "8"}, "M01_NUM_BYTES": {"value": "8"}, "M02_NUM_BYTES": {"value": "8"}, "M03_NUM_BYTES": {"value": "8"}, "M04_NUM_BYTES": {"value": "8"}, "M05_NUM_BYTES": {"value": "8"}, "M06_NUM_BYTES": {"value": "8"}, "M07_NUM_BYTES": {"value": "8"}, "M08_NUM_BYTES": {"value": "8"}, "M09_NUM_BYTES": {"value": "8"}, "M10_NUM_BYTES": {"value": "8"}, "M11_NUM_BYTES": {"value": "8"}, "M12_NUM_BYTES": {"value": "8"}, "M13_NUM_BYTES": {"value": "8"}, "M14_NUM_BYTES": {"value": "8"}, "M15_NUM_BYTES": {"value": "8"}, "MAX_PAYLD_BYTES": {"value": "8"}, "M_SEND_PIPELINE": {"value": "0"}, "NUM_MI": {"value": "1"}, "NUM_OUTSTANDING": {"value": "16"}, "NUM_SI": {"value": "1"}, "PAYLD_WIDTH": {"value": "138"}, "S00_NUM_BYTES": {"value": "4"}, "S01_NUM_BYTES": {"value": "4"}, "S02_NUM_BYTES": {"value": "4"}, "S03_NUM_BYTES": {"value": "4"}, "S04_NUM_BYTES": {"value": "4"}, "S05_NUM_BYTES": {"value": "4"}, "S06_NUM_BYTES": {"value": "4"}, "S07_NUM_BYTES": {"value": "4"}, "S08_NUM_BYTES": {"value": "4"}, "S09_NUM_BYTES": {"value": "4"}, "S10_NUM_BYTES": {"value": "4"}, "S11_NUM_BYTES": {"value": "4"}, "S12_NUM_BYTES": {"value": "4"}, "S13_NUM_BYTES": {"value": "4"}, "S14_NUM_BYTES": {"value": "4"}, "S15_NUM_BYTES": {"value": "4"}, "SC_ROUTE_WIDTH": {"value": "1"}, "USER_WIDTH": {"value": "0"}}}, "s00_w_node": {"vlnv": "xilinx.com:ip:sc_node:1.0", "xci_name": "bd_a552_swn_0", "parameters": {"ACLKEN_CONVERSION": {"value": "0"}, "ACLK_RELATIONSHIP": {"value": "1"}, "ADDR_WIDTH": {"value": "32"}, "CHANNEL": {"value": "1"}, "ID_WIDTH": {"value": "1"}, "M00_NUM_BYTES": {"value": "8"}, "M01_NUM_BYTES": {"value": "8"}, "M02_NUM_BYTES": {"value": "8"}, "M03_NUM_BYTES": {"value": "8"}, "M04_NUM_BYTES": {"value": "8"}, "M05_NUM_BYTES": {"value": "8"}, "M06_NUM_BYTES": {"value": "8"}, "M07_NUM_BYTES": {"value": "8"}, "M08_NUM_BYTES": {"value": "8"}, "M09_NUM_BYTES": {"value": "8"}, "M10_NUM_BYTES": {"value": "8"}, "M11_NUM_BYTES": {"value": "8"}, "M12_NUM_BYTES": {"value": "8"}, "M13_NUM_BYTES": {"value": "8"}, "M14_NUM_BYTES": {"value": "8"}, "M15_NUM_BYTES": {"value": "8"}, "MAX_PAYLD_BYTES": {"value": "8"}, "M_SEND_PIPELINE": {"value": "0"}, "NUM_MI": {"value": "1"}, "NUM_OUTSTANDING": {"value": "16"}, "NUM_SI": {"value": "1"}, "PAYLD_WIDTH": {"value": "88"}, "S00_NUM_BYTES": {"value": "4"}, "S01_NUM_BYTES": {"value": "4"}, "S02_NUM_BYTES": {"value": "4"}, "S03_NUM_BYTES": {"value": "4"}, "S04_NUM_BYTES": {"value": "4"}, "S05_NUM_BYTES": {"value": "4"}, "S06_NUM_BYTES": {"value": "4"}, "S07_NUM_BYTES": {"value": "4"}, "S08_NUM_BYTES": {"value": "4"}, "S09_NUM_BYTES": {"value": "4"}, "S10_NUM_BYTES": {"value": "4"}, "S11_NUM_BYTES": {"value": "4"}, "S12_NUM_BYTES": {"value": "4"}, "S13_NUM_BYTES": {"value": "4"}, "S14_NUM_BYTES": {"value": "4"}, "S15_NUM_BYTES": {"value": "4"}, "SC_ROUTE_WIDTH": {"value": "1"}, "USER_BITS_PER_BYTE": {"value": "0"}}}, "s00_b_node": {"vlnv": "xilinx.com:ip:sc_node:1.0", "xci_name": "bd_a552_sbn_0", "parameters": {"ACLKEN_CONVERSION": {"value": "0"}, "ACLK_RELATIONSHIP": {"value": "1"}, "ADDR_WIDTH": {"value": "32"}, "CHANNEL": {"value": "4"}, "ID_WIDTH": {"value": "1"}, "M00_NUM_BYTES": {"value": "4"}, "M01_NUM_BYTES": {"value": "4"}, "M02_NUM_BYTES": {"value": "4"}, "M03_NUM_BYTES": {"value": "4"}, "M04_NUM_BYTES": {"value": "4"}, "M05_NUM_BYTES": {"value": "4"}, "M06_NUM_BYTES": {"value": "4"}, "M07_NUM_BYTES": {"value": "4"}, "M08_NUM_BYTES": {"value": "4"}, "M09_NUM_BYTES": {"value": "4"}, "M10_NUM_BYTES": {"value": "4"}, "M11_NUM_BYTES": {"value": "4"}, "M12_NUM_BYTES": {"value": "4"}, "M13_NUM_BYTES": {"value": "4"}, "M14_NUM_BYTES": {"value": "4"}, "M15_NUM_BYTES": {"value": "4"}, "MAX_PAYLD_BYTES": {"value": "8"}, "M_SEND_PIPELINE": {"value": "0"}, "NUM_MI": {"value": "1"}, "NUM_OUTSTANDING": {"value": "16"}, "NUM_SI": {"value": "1"}, "PAYLD_WIDTH": {"value": "5"}, "S00_NUM_BYTES": {"value": "8"}, "S01_NUM_BYTES": {"value": "8"}, "S02_NUM_BYTES": {"value": "8"}, "S03_NUM_BYTES": {"value": "8"}, "S04_NUM_BYTES": {"value": "8"}, "S05_NUM_BYTES": {"value": "8"}, "S06_NUM_BYTES": {"value": "8"}, "S07_NUM_BYTES": {"value": "8"}, "S08_NUM_BYTES": {"value": "8"}, "S09_NUM_BYTES": {"value": "8"}, "S10_NUM_BYTES": {"value": "8"}, "S11_NUM_BYTES": {"value": "8"}, "S12_NUM_BYTES": {"value": "8"}, "S13_NUM_BYTES": {"value": "8"}, "S14_NUM_BYTES": {"value": "8"}, "S15_NUM_BYTES": {"value": "8"}, "SC_ROUTE_WIDTH": {"value": "1"}, "USER_WIDTH": {"value": "0"}}}}, "interface_nets": {"s00_ar_node_M_SC": {"interface_ports": ["M_SC_AR", "s00_ar_node/M_SC"]}, "S_SC_AR_1": {"interface_ports": ["S_SC_AR", "s00_ar_node/S_SC"]}, "S_SC_B_1": {"interface_ports": ["S_SC_B", "s00_b_node/S_SC"]}, "s00_b_node_M_SC": {"interface_ports": ["M_SC_B", "s00_b_node/M_SC"]}, "s00_r_node_M_SC": {"interface_ports": ["M_SC_R", "s00_r_node/M_SC"]}, "S_SC_R_1": {"interface_ports": ["S_SC_R", "s00_r_node/S_SC"]}, "s00_w_node_M_SC": {"interface_ports": ["M_SC_W", "s00_w_node/M_SC"]}, "S_SC_W_1": {"interface_ports": ["S_SC_W", "s00_w_node/S_SC"]}, "s00_aw_node_M_SC": {"interface_ports": ["M_SC_AW", "s00_aw_node/M_SC"]}, "S_SC_AW_1": {"interface_ports": ["S_SC_AW", "s00_aw_node/S_SC"]}}, "nets": {"s_sc_clk_1": {"ports": ["s_sc_clk", "s00_ar_node/s_sc_aclk", "s00_aw_node/s_sc_aclk", "s00_w_node/s_sc_aclk", "s00_r_node/m_sc_aclk", "s00_b_node/m_sc_aclk"]}, "m_sc_clk_1": {"ports": ["m_sc_clk", "s00_ar_node/m_sc_aclk", "s00_aw_node/m_sc_aclk", "s00_w_node/m_sc_aclk", "s00_r_node/s_sc_aclk", "s00_b_node/s_sc_aclk"]}, "s_sc_resetn_1": {"ports": ["s_sc_resetn", "s00_ar_node/s_sc_aresetn", "s00_aw_node/s_sc_aresetn", "s00_w_node/s_sc_aresetn", "s00_r_node/m_sc_aresetn", "s00_b_node/m_sc_aresetn"]}, "m_sc_resetn_1": {"ports": ["m_sc_resetn", "s00_ar_node/m_sc_aresetn", "s00_aw_node/m_sc_aresetn", "s00_w_node/m_sc_aresetn", "s00_r_node/s_sc_aresetn", "s00_b_node/s_sc_aresetn"]}}}, "m00_sc2axi": {"vlnv": "xilinx.com:ip:sc_sc2axi:1.0", "xci_name": "bd_a552_m00s2a_0", "parameters": {"AXI_ADDR_WIDTH": {"value": "32"}, "AXI_ID_WIDTH": {"value": "1"}, "AXI_RDATA_WIDTH": {"value": "64"}, "AXI_WDATA_WIDTH": {"value": "64"}, "MSC_ROUTE_WIDTH": {"value": "1"}, "READ_WRITE_MODE": {"value": "READ_WRITE"}, "SC_ADDR_WIDTH": {"value": "32"}, "SC_ARUSER_WIDTH": {"value": "0"}, "SC_AWUSER_WIDTH": {"value": "0"}, "SC_BUSER_WIDTH": {"value": "0"}, "SC_ID_WIDTH": {"value": "1"}, "SC_RDATA_WIDTH": {"value": "64"}, "SC_RUSER_BITS_PER_BYTE": {"value": "0"}, "SC_WDATA_WIDTH": {"value": "64"}, "SC_WUSER_BITS_PER_BYTE": {"value": "0"}, "SSC_ROUTE_WIDTH": {"value": "1"}}}, "m00_exit_pipeline": {"interface_ports": {"s_axi": {"mode": "Slave", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}, "m_axi": {"mode": "Master", "vlnv": "xilinx.com:interface:aximm_rtl:1.0"}}, "ports": {"aclk": {"type": "clk", "direction": "I"}, "aresetn": {"type": "rst", "direction": "I"}}, "components": {"m00_exit": {"vlnv": "xilinx.com:ip:sc_exit:1.0", "xci_name": "bd_a552_m00e_0", "parameters": {"ADDR_WIDTH": {"value": "32"}, "HAS_BURST": {"value": "1"}, "HAS_LOCK": {"value": "0"}, "IS_CASCADED": {"value": "0"}, "MAX_RUSER_BITS_PER_BYTE": {"value": "0"}, "MAX_WUSER_BITS_PER_BYTE": {"value": "0"}, "MEP_IDENTIFIER_WIDTH": {"value": "1"}, "M_ARUSER_WIDTH": {"value": "0"}, "M_AWUSER_WIDTH": {"value": "0"}, "M_BUSER_WIDTH": {"value": "0"}, "M_ID_WIDTH": {"value": "0"}, "M_MAX_BURST_LENGTH": {"value": "16"}, "M_PROTOCOL": {"value": "AXI3"}, "M_RUSER_BITS_PER_BYTE": {"value": "0"}, "M_RUSER_WIDTH": {"value": "0"}, "M_WUSER_BITS_PER_BYTE": {"value": "0"}, "M_WUSER_WIDTH": {"value": "0"}, "NUM_MSC": {"value": "1"}, "NUM_READ_OUTSTANDING": {"value": "2"}, "NUM_WRITE_OUTSTANDING": {"value": "16"}, "RDATA_WIDTH": {"value": "64"}, "READ_WRITE_MODE": {"value": "READ_WRITE"}, "SSC000_ROUTE": {"value": "0b1"}, "SSC001_ROUTE": {"value": "0b0"}, "SSC_ROUTE_WIDTH": {"value": "1"}, "S_ID_WIDTH": {"value": "1"}, "WDATA_WIDTH": {"value": "64"}}}}, "interface_nets": {"s_axi_1": {"interface_ports": ["s_axi", "m00_exit/S_AXI"]}, "m00_exit_M_AXI": {"interface_ports": ["m_axi", "m00_exit/M_AXI"]}}, "nets": {"aclk_1": {"ports": ["aclk", "m00_exit/aclk"]}, "aresetn_1": {"ports": ["aresetn", "m00_exit/aresetn"]}}}}, "interface_nets": {"S_SC_AR_1": {"interface_ports": ["s00_nodes/S_SC_AR", "s00_axi2sc/M_SC_AR"]}, "S_SC_W_1": {"interface_ports": ["s00_nodes/S_SC_W", "s00_axi2sc/M_SC_W"]}, "S00_AXI_1": {"interface_ports": ["S00_AXI", "s00_entry_pipeline/s_axi"]}, "s00_nodes_M_SC_R": {"interface_ports": ["s00_nodes/M_SC_R", "s00_axi2sc/S_SC_R"]}, "s00_nodes_M_SC_B": {"interface_ports": ["s00_nodes/M_SC_B", "s00_axi2sc/S_SC_B"]}, "S_SC_AW_1": {"interface_ports": ["s00_nodes/S_SC_AW", "s00_axi2sc/M_SC_AW"]}, "s00_entry_pipeline_m_axi": {"interface_ports": ["s00_entry_pipeline/m_axi", "s00_axi2sc/S_AXI"]}, "S_SC_R_1": {"interface_ports": ["s00_nodes/S_SC_R", "m00_sc2axi/M_SC_R"]}, "s00_nodes_M_SC_AR": {"interface_ports": ["s00_nodes/M_SC_AR", "m00_sc2axi/S_SC_AR"]}, "s00_nodes_M_SC_AW": {"interface_ports": ["s00_nodes/M_SC_AW", "m00_sc2axi/S_SC_AW"]}, "S_SC_B_1": {"interface_ports": ["s00_nodes/S_SC_B", "m00_sc2axi/M_SC_B"]}, "m00_sc2axi_M_AXI": {"interface_ports": ["m00_sc2axi/M_AXI", "m00_exit_pipeline/s_axi"]}, "m00_exit_pipeline_m_axi": {"interface_ports": ["M00_AXI", "m00_exit_pipeline/m_axi"]}, "s00_nodes_M_SC_W": {"interface_ports": ["s00_nodes/M_SC_W", "m00_sc2axi/S_SC_W"]}}, "nets": {"aclk_net": {"ports": ["aclk", "clk_map/aclk"]}, "aresetn_1": {"ports": ["aresetn", "clk_map/aresetn"]}, "aresetn_net": {"ports": ["clk_map/aresetn_out"]}, "swbd_aclk_net": {"ports": ["clk_map/swbd_aclk"]}, "swbd_aresetn_net": {"ports": ["clk_map/swbd_aresetn"]}, "aclk_1": {"ports": ["clk_map/S00_ACLK", "s00_entry_pipeline/aclk", "s00_axi2sc/aclk", "s00_nodes/s_sc_clk"]}, "aresetn_2": {"ports": ["clk_map/S00_ARESETN", "s00_entry_pipeline/aresetn", "s00_nodes/s_sc_resetn"]}, "m_sc_clk_1": {"ports": ["clk_map/M00_ACLK", "s00_nodes/m_sc_clk", "m00_sc2axi/aclk", "m00_exit_pipeline/aclk"]}, "m_sc_resetn_1": {"ports": ["clk_map/M00_ARESETN", "s00_nodes/m_sc_resetn", "m00_exit_pipeline/aresetn"]}}, "addressing": {"/": {"address_spaces": {"S00_AXI": {"range": "4G", "width": "32"}}, "memory_maps": {"M00_AXI": {"address_blocks": {"Reg": {"base_address": "0", "range": "64K", "width": "32", "usage": "register"}}}}}}}}