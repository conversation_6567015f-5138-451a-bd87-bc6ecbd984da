Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-----------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Aug  4 14:38:54 2025
| Host         : wantong running 64-bit major release  (build 9200)
| Command      : report_methodology -file UGT_methodology_drc_routed.rpt -pb UGT_methodology_drc_routed.pb -rpx UGT_methodology_drc_routed.rpx
| Design       : UGT
| Device       : xc7z020clg400-2
| Speed File   : -2
| Design State : Fully Routed
-----------------------------------------------------------------------------------------------------------------------------------------------

Report Methodology

Table of Contents
-----------------
1. REPORT SUMMARY
2. REPORT DETAILS

1. REPORT SUMMARY
-----------------
            Netlist: netlist
          Floorplan: design_1
      Design limits: <entire design considered>
             Max violations: <unlimited>
             Violations found: 12
+-----------+----------+------------------------------------+------------+
| Rule      | Severity | Description                        | Violations |
+-----------+----------+------------------------------------+------------+
| TIMING-9  | Warning  | Unknown CDC Logic                  | 1          |
| TIMING-10 | Warning  | Missing property on synchronizer   | 1          |
| TIMING-18 | Warning  | Missing input or output delay      | 2          |
| TIMING-24 | Warning  | Overridden Max delay datapath only | 8          |
+-----------+----------+------------------------------------+------------+

2. REPORT DETAILS
-----------------
TIMING-9#1 Warning
Unknown CDC Logic  
One or more asynchronous Clock Domain Crossing has been detected between 2 clock domains through a set_false_path or a set_clock_groups or set_max_delay -datapath_only constraint but no double-registers logic synchronizer has been found on the side of the capture clock. It is recommended to run report_cdc for a complete and detailed CDC coverage. Please consider using XPM_CDC to avoid Critical severities
Related violations: <none>

TIMING-10#1 Warning
Missing property on synchronizer  
One or more logic synchronizer has been detected between 2 clock domains but the synchronizer does not have the property ASYNC_REG defined on one or both registers. It is recommended to run report_cdc for a complete and detailed CDC coverage
Related violations: <none>

TIMING-18#1 Warning
Missing input or output delay  
An output delay is missing on ADC_PDWN relative to clock(s) ADC_DCLK, clk_fpga_0
Related violations: <none>

TIMING-18#2 Warning
Missing input or output delay  
An output delay is missing on ULTRASOUND_PLUS relative to clock(s) ADC_DCLK
Related violations: <none>

TIMING-24#1 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 12 in the Timing Constraints window in Vivado IDE) between clocks ADC_DCLK and clk_fpga_0 overrides a set_max_delay -datapath_only (position 21). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>

TIMING-24#2 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 12 in the Timing Constraints window in Vivado IDE) between clocks ADC_DCLK and clk_fpga_0 overrides a set_max_delay -datapath_only (position 23). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>

TIMING-24#3 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 12 in the Timing Constraints window in Vivado IDE) between clocks clk_fpga_0 and ADC_DCLK overrides a set_max_delay -datapath_only (position 19). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>

TIMING-24#4 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 12 in the Timing Constraints window in Vivado IDE) between clocks clk_fpga_0 and ADC_DCLK overrides a set_max_delay -datapath_only (position 25). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>

TIMING-24#5 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 13 in the Timing Constraints window in Vivado IDE) between clocks ADC_DCLK and clk_fpga_0 overrides a set_max_delay -datapath_only (position 21). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>

TIMING-24#6 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 13 in the Timing Constraints window in Vivado IDE) between clocks ADC_DCLK and clk_fpga_0 overrides a set_max_delay -datapath_only (position 23). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>

TIMING-24#7 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 14 in the Timing Constraints window in Vivado IDE) between clocks clk_fpga_0 and ADC_DCLK overrides a set_max_delay -datapath_only (position 19). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>

TIMING-24#8 Warning
Overridden Max delay datapath only  
A set_clock_groups or a set_false path (see constraint position 14 in the Timing Constraints window in Vivado IDE) between clocks clk_fpga_0 and ADC_DCLK overrides a set_max_delay -datapath_only (position 25). It is not recommended to override a set_max_delay -datapath_only constraint. Replace the set_clock_groups or set_false_path between clocks with point-to-point set_false_path constraints
Related violations: <none>


