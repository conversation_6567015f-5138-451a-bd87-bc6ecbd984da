################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_alpha16.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_argb.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_chroma_keyed.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_indexed16.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_rgb.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_rgb565a8.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_12_compr_az.c.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_16_compr_az.c.c \
../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_28_compr_az.c.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_alpha16.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_argb.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_chroma_keyed.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_indexed16.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_rgb.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_rgb565a8.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_12_compr_az.c.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_16_compr_az.c.o \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_28_compr_az.c.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_alpha16.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_argb.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_chroma_keyed.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_indexed16.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_rgb.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/img_benchmark_cogwheel_rgb565a8.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_12_compr_az.c.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_16_compr_az.c.d \
./src/components/gui/lvgl_8.4.0/demos/benchmark/assets/lv_font_bechmark_montserrat_28_compr_az.c.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/demos/benchmark/assets/%.o: ../src/components/gui/lvgl_8.4.0/demos/benchmark/assets/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


