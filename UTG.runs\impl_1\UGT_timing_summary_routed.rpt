Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Aug  4 14:38:58 2025
| Host         : wantong running 64-bit major release  (build 9200)
| Command      : report_timing_summary -max_paths 10 -file UGT_timing_summary_routed.rpt -pb UGT_timing_summary_routed.pb -rpx UGT_timing_summary_routed.rpx -warn_on_violation
| Design       : UGT
| Device       : 7z020-clg400
| Speed File   : -2  PRODUCTION 1.11 2014-09-11
--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Timing Summary Report

------------------------------------------------------------------------------------------------
| Timer Settings
| --------------
------------------------------------------------------------------------------------------------

  Enable Multi Corner Analysis               :  Yes
  Enable Pessimism Removal                   :  Yes
  Pessimism Removal Resolution               :  Nearest Common Node
  Enable Input Delay Default Clock           :  No
  Enable Preset / Clear Arcs                 :  No
  Disable Flight Delays                      :  No
  Ignore I/O Paths                           :  No
  Timing Early Launch at Borrowing Latches   :  false

  Corner  Analyze    Analyze    
  Name    Max Paths  Min Paths  
  ------  ---------  ---------  
  Slow    Yes        Yes        
  Fast    Yes        Yes        



check_timing report

Table of Contents
-----------------
1. checking no_clock
2. checking constant_clock
3. checking pulse_width_clock
4. checking unconstrained_internal_endpoints
5. checking no_input_delay
6. checking no_output_delay
7. checking multiple_clock
8. checking generated_clocks
9. checking loops
10. checking partial_input_delay
11. checking partial_output_delay
12. checking latch_loops

1. checking no_clock
--------------------
 There are 0 register/latch pins with no clock.


2. checking constant_clock
--------------------------
 There are 0 register/latch pins with constant_clock.


3. checking pulse_width_clock
-----------------------------
 There are 0 register/latch pins which need pulse_width check


4. checking unconstrained_internal_endpoints
--------------------------------------------
 There are 0 pins that are not constrained for maximum delay.

 There are 0 pins that are not constrained for maximum delay due to constant clock.


5. checking no_input_delay
--------------------------
 There are 0 input ports with no input delay specified.

 There are 0 input ports with no input delay but user has a false path constraint.


6. checking no_output_delay
---------------------------
 There are 30 ports with no output delay specified. (HIGH)

 There are 0 ports with no output delay but user has a false path constraint

 There are 0 ports with no output delay but with a timing clock defined on it or propagating through it


7. checking multiple_clock
--------------------------
 There are 0 register/latch pins with multiple clocks.


8. checking generated_clocks
----------------------------
 There are 0 generated clocks that are not connected to a clock source.


9. checking loops
-----------------
 There are 0 combinational loops in the design.


10. checking partial_input_delay
--------------------------------
 There are 12 input ports with partial input delay specified. (HIGH)


11. checking partial_output_delay
---------------------------------
 There are 0 ports with partial output delay specified.


12. checking latch_loops
------------------------
 There are 0 combinational latch loops in the design through latch input



------------------------------------------------------------------------------------------------
| Design Timing Summary
| ---------------------
------------------------------------------------------------------------------------------------

    WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
    -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
      2.508        0.000                      0                24772        0.040        0.000                      0                24665        0.530        0.000                       0                 10357  


All user specified timing constraints are met.


------------------------------------------------------------------------------------------------
| Clock Summary
| -------------
------------------------------------------------------------------------------------------------

Clock                 Waveform(ns)         Period(ns)      Frequency(MHz)
-----                 ------------         ----------      --------------
ADC_DCLK              {0.000 4.000}        8.000           125.000         
clk_50M               {0.000 10.000}       20.000          50.000          
  clk_out2_clk_wiz_0  {0.000 4.000}        8.000           125.000         
  clkfbout_clk_wiz_0  {0.000 10.000}       20.000          50.000          
clk_fpga_0            {0.000 5.000}        10.000          100.000         
  I                   {0.000 1.000}        2.000           500.000         
    LCD_CLK_OBUF      {0.000 4.000}        10.000          100.000         
  mmcm_fbclk_out      {0.000 5.000}        10.000          100.000         


------------------------------------------------------------------------------------------------
| Intra Clock Table
| -----------------
------------------------------------------------------------------------------------------------

Clock                     WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints     WPWS(ns)     TPWS(ns)  TPWS Failing Endpoints  TPWS Total Endpoints  
-----                     -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------     --------     --------  ----------------------  --------------------  
ADC_DCLK                    2.508        0.000                      0                  578        0.120        0.000                      0                  578        3.146        0.000                       0                   309  
clk_50M                                                                                                                                                                 7.000        0.000                       0                     1  
  clk_out2_clk_wiz_0                                                                                                                                                    6.408        0.000                       0                     3  
  clkfbout_clk_wiz_0                                                                                                                                                   18.408        0.000                       0                     3  
clk_fpga_0                  3.095        0.000                      0                19328        0.040        0.000                      0                19328        2.500        0.000                       0                  7534  
  I                                                                                                                                                                     0.530        0.000                       0                     2  
    LCD_CLK_OBUF            3.409        0.000                      0                 4759        0.103        0.000                      0                 4759        3.146        0.000                       0                  2503  
  mmcm_fbclk_out                                                                                                                                                        8.751        0.000                       0                     2  


------------------------------------------------------------------------------------------------
| Inter Clock Table
| -----------------
------------------------------------------------------------------------------------------------

From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  
LCD_CLK_OBUF  clk_fpga_0          8.498        0.000                      0                   44                                                                        
clk_fpga_0    LCD_CLK_OBUF        8.776        0.000                      0                   63                                                                        


------------------------------------------------------------------------------------------------
| Other Path Groups Table
| -----------------------
------------------------------------------------------------------------------------------------

Path Group    From Clock    To Clock          WNS(ns)      TNS(ns)  TNS Failing Endpoints  TNS Total Endpoints      WHS(ns)      THS(ns)  THS Failing Endpoints  THS Total Endpoints  
----------    ----------    --------          -------      -------  ---------------------  -------------------      -------      -------  ---------------------  -------------------  


------------------------------------------------------------------------------------------------
| Timing Details
| --------------
------------------------------------------------------------------------------------------------


---------------------------------------------------------------------------------------------------
From Clock:  ADC_DCLK
  To Clock:  ADC_DCLK

Setup :            0  Failing Endpoints,  Worst Slack        2.508ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.120ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack        3.146ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             2.508ns  (required time - arrival time)
  Source:                 ADC_DATA[1]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[1]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.670ns  (logic 0.526ns (31.491%)  route 1.144ns (68.509%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.627ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.627ns = ( 9.627 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    V13                                               0.000     5.400 r  ADC_DATA[1] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[1]
    V13                  IBUF (Prop_ibuf_I_O)         0.526     5.926 r  ADC_DATA_IBUF[1]_inst/O
                         net (fo=1, routed)           1.144     7.070    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[1]
    SLICE_X83Y76         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[1]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.571     9.627    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X83Y76         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[1]/C
                         clock pessimism              0.000     9.627    
                         clock uncertainty           -0.035     9.592    
    SLICE_X83Y76         FDRE (Setup_fdre_C_D)       -0.014     9.578    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[1]
  -------------------------------------------------------------------
                         required time                          9.578    
                         arrival time                          -7.070    
  -------------------------------------------------------------------
                         slack                                  2.508    

Slack (MET) :             2.525ns  (required time - arrival time)
  Source:                 ADC_DATA[2]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[2]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.631ns  (logic 0.525ns (32.161%)  route 1.107ns (67.839%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.634ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.634ns = ( 9.634 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    U13                                               0.000     5.400 r  ADC_DATA[2] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[2]
    U13                  IBUF (Prop_ibuf_I_O)         0.525     5.925 r  ADC_DATA_IBUF[2]_inst/O
                         net (fo=1, routed)           1.107     7.031    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[2]
    SLICE_X82Y83         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[2]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.578     9.634    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X82Y83         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[2]/C
                         clock pessimism              0.000     9.634    
                         clock uncertainty           -0.035     9.599    
    SLICE_X82Y83         FDRE (Setup_fdre_C_D)       -0.043     9.556    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[2]
  -------------------------------------------------------------------
                         required time                          9.556    
                         arrival time                          -7.031    
  -------------------------------------------------------------------
                         slack                                  2.525    

Slack (MET) :             2.568ns  (required time - arrival time)
  Source:                 ADC_DATA[0]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[0]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.643ns  (logic 0.540ns (32.886%)  route 1.103ns (67.114%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.661ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.661ns = ( 9.661 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    U15                                               0.000     5.400 r  ADC_DATA[0] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[0]
    U15                  IBUF (Prop_ibuf_I_O)         0.540     5.940 r  ADC_DATA_IBUF[0]_inst/O
                         net (fo=1, routed)           1.103     7.043    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[0]
    SLICE_X91Y57         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[0]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.605     9.661    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X91Y57         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[0]/C
                         clock pessimism              0.000     9.661    
                         clock uncertainty           -0.035     9.626    
    SLICE_X91Y57         FDRE (Setup_fdre_C_D)       -0.014     9.612    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[0]
  -------------------------------------------------------------------
                         required time                          9.612    
                         arrival time                          -7.043    
  -------------------------------------------------------------------
                         slack                                  2.568    

Slack (MET) :             2.855ns  (required time - arrival time)
  Source:                 ADC_DATA[6]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[6]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.298ns  (logic 0.503ns (38.731%)  route 0.795ns (61.269%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.640ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.640ns = ( 9.640 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    Y12                                               0.000     5.400 r  ADC_DATA[6] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[6]
    Y12                  IBUF (Prop_ibuf_I_O)         0.503     5.903 r  ADC_DATA_IBUF[6]_inst/O
                         net (fo=1, routed)           0.795     6.698    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[6]
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[6]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.584     9.640    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[6]/C
                         clock pessimism              0.000     9.640    
                         clock uncertainty           -0.035     9.605    
    SLICE_X8Y23          FDRE (Setup_fdre_C_D)       -0.051     9.554    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[6]
  -------------------------------------------------------------------
                         required time                          9.554    
                         arrival time                          -6.698    
  -------------------------------------------------------------------
                         slack                                  2.855    

Slack (MET) :             2.881ns  (required time - arrival time)
  Source:                 ADC_DATA[5]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[5]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.281ns  (logic 0.504ns (39.372%)  route 0.776ns (60.628%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.640ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.640ns = ( 9.640 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    Y13                                               0.000     5.400 r  ADC_DATA[5] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[5]
    Y13                  IBUF (Prop_ibuf_I_O)         0.504     5.904 r  ADC_DATA_IBUF[5]_inst/O
                         net (fo=1, routed)           0.776     6.681    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[5]
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[5]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.584     9.640    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[5]/C
                         clock pessimism              0.000     9.640    
                         clock uncertainty           -0.035     9.605    
    SLICE_X8Y23          FDRE (Setup_fdre_C_D)       -0.043     9.562    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[5]
  -------------------------------------------------------------------
                         required time                          9.562    
                         arrival time                          -6.681    
  -------------------------------------------------------------------
                         slack                                  2.881    

Slack (MET) :             2.940ns  (required time - arrival time)
  Source:                 ADC_DATA[7]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[7]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.226ns  (logic 0.462ns (37.710%)  route 0.764ns (62.290%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.640ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.640ns = ( 9.640 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    V11                                               0.000     5.400 r  ADC_DATA[7] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[7]
    V11                  IBUF (Prop_ibuf_I_O)         0.462     5.862 r  ADC_DATA_IBUF[7]_inst/O
                         net (fo=1, routed)           0.764     6.626    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[7]
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[7]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.584     9.640    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[7]/C
                         clock pessimism              0.000     9.640    
                         clock uncertainty           -0.035     9.605    
    SLICE_X8Y23          FDRE (Setup_fdre_C_D)       -0.038     9.567    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[7]
  -------------------------------------------------------------------
                         required time                          9.567    
                         arrival time                          -6.626    
  -------------------------------------------------------------------
                         slack                                  2.940    

Slack (MET) :             2.951ns  (required time - arrival time)
  Source:                 ADC_DATA[4]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[4]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.210ns  (logic 0.486ns (40.121%)  route 0.725ns (59.879%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.639ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.639ns = ( 9.639 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    W11                                               0.000     5.400 r  ADC_DATA[4] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[4]
    W11                  IBUF (Prop_ibuf_I_O)         0.486     5.886 r  ADC_DATA_IBUF[4]_inst/O
                         net (fo=1, routed)           0.725     6.610    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[4]
    SLICE_X8Y25          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[4]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.583     9.639    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X8Y25          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[4]/C
                         clock pessimism              0.000     9.639    
                         clock uncertainty           -0.035     9.604    
    SLICE_X8Y25          FDRE (Setup_fdre_C_D)       -0.043     9.561    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[4]
  -------------------------------------------------------------------
                         required time                          9.561    
                         arrival time                          -6.610    
  -------------------------------------------------------------------
                         slack                                  2.951    

Slack (MET) :             3.025ns  (required time - arrival time)
  Source:                 ADC_DATA[8]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[8]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.143ns  (logic 0.460ns (40.275%)  route 0.683ns (59.725%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.647ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.647ns = ( 9.647 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    V10                                               0.000     5.400 r  ADC_DATA[8] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[8]
    V10                  IBUF (Prop_ibuf_I_O)         0.460     5.860 r  ADC_DATA_IBUF[8]_inst/O
                         net (fo=1, routed)           0.683     6.543    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[8]
    SLICE_X10Y16         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[8]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.591     9.647    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X10Y16         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[8]/C
                         clock pessimism              0.000     9.647    
                         clock uncertainty           -0.035     9.612    
    SLICE_X10Y16         FDRE (Setup_fdre_C_D)       -0.043     9.569    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[8]
  -------------------------------------------------------------------
                         required time                          9.569    
                         arrival time                          -6.543    
  -------------------------------------------------------------------
                         slack                                  3.025    

Slack (MET) :             3.027ns  (required time - arrival time)
  Source:                 ADC_DATA[3]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[3]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.158ns  (logic 0.489ns (42.224%)  route 0.669ns (57.776%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.640ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.640ns = ( 9.640 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    Y11                                               0.000     5.400 r  ADC_DATA[3] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[3]
    Y11                  IBUF (Prop_ibuf_I_O)         0.489     5.889 r  ADC_DATA_IBUF[3]_inst/O
                         net (fo=1, routed)           0.669     6.558    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[3]
    SLICE_X9Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[3]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.584     9.640    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X9Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[3]/C
                         clock pessimism              0.000     9.640    
                         clock uncertainty           -0.035     9.605    
    SLICE_X9Y23          FDRE (Setup_fdre_C_D)       -0.019     9.586    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[3]
  -------------------------------------------------------------------
                         required time                          9.586    
                         arrival time                          -6.558    
  -------------------------------------------------------------------
                         slack                                  3.027    

Slack (MET) :             3.037ns  (required time - arrival time)
  Source:                 ADC_DATA[9]
                            (input port clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[9]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Setup (Max at Fast Process Corner)
  Requirement:            8.000ns  (ADC_DCLK rise@8.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        1.129ns  (logic 0.489ns (43.340%)  route 0.640ns (56.660%))
  Logic Levels:           1  (IBUF=1)
  Input Delay:            5.400ns
  Clock Path Skew:        1.640ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    1.640ns = ( 9.640 - 8.000 ) 
    Source Clock Delay      (SCD):    0.000ns
    Clock Pessimism Removal (CPR):    0.000ns
  Clock Uncertainty:      0.035ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.000ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
                         input delay                  5.400     5.400    
    W10                                               0.000     5.400 r  ADC_DATA[9] (IN)
                         net (fo=0)                   0.000     5.400    ADC_DATA[9]
    W10                  IBUF (Prop_ibuf_I_O)         0.489     5.889 r  ADC_DATA_IBUF[9]_inst/O
                         net (fo=1, routed)           0.640     6.529    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DATA[9]
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[9]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      8.000     8.000 r  
    U14                                               0.000     8.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     8.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     8.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     9.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     9.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.584     9.640    u1/utg_i/AXI_ADC_0/inst/adc_inst/ADC_DCLK
    SLICE_X8Y23          FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[9]/C
                         clock pessimism              0.000     9.640    
                         clock uncertainty           -0.035     9.605    
    SLICE_X8Y23          FDRE (Setup_fdre_C_D)       -0.038     9.567    u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_data_reg[9]
  -------------------------------------------------------------------
                         required time                          9.567    
                         arrival time                          -6.529    
  -------------------------------------------------------------------
                         slack                                  3.037    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.120ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][6]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.122ns
    Source Clock Delay      (SCD):    1.608ns
    Clock Pessimism Removal (CPR):    0.514ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.552     1.608    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X45Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X45Y28         FDRE (Prop_fdre_C_Q)         0.141     1.749 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]/Q
                         net (fo=1, routed)           0.055     1.804    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff[0][6]
    SLICE_X45Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][6]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.817     2.122    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X45Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][6]/C
                         clock pessimism             -0.514     1.608    
    SLICE_X45Y28         FDRE (Hold_fdre_C_D)         0.076     1.684    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][6]
  -------------------------------------------------------------------
                         required time                         -1.684    
                         arrival time                           1.804    
  -------------------------------------------------------------------
                         slack                                  0.120    

Slack (MET) :             0.120ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][7]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.153ns
    Source Clock Delay      (SCD):    1.638ns
    Clock Pessimism Removal (CPR):    0.515ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.582     1.638    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X31Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X31Y30         FDRE (Prop_fdre_C_Q)         0.141     1.779 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/Q
                         net (fo=1, routed)           0.055     1.834    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff[0][7]
    SLICE_X31Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][7]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.848     2.153    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X31Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][7]/C
                         clock pessimism             -0.515     1.638    
    SLICE_X31Y30         FDRE (Hold_fdre_C_D)         0.076     1.714    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][7]
  -------------------------------------------------------------------
                         required time                         -1.714    
                         arrival time                           1.834    
  -------------------------------------------------------------------
                         slack                                  0.120    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][3]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.122ns
    Source Clock Delay      (SCD):    1.608ns
    Clock Pessimism Removal (CPR):    0.514ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.552     1.608    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X45Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X45Y28         FDRE (Prop_fdre_C_Q)         0.141     1.749 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]/Q
                         net (fo=1, routed)           0.055     1.804    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff[0][3]
    SLICE_X45Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][3]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.817     2.122    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X45Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][3]/C
                         clock pessimism             -0.514     1.608    
    SLICE_X45Y28         FDRE (Hold_fdre_C_D)         0.075     1.683    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][3]
  -------------------------------------------------------------------
                         required time                         -1.683    
                         arrival time                           1.804    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][4]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.121ns
    Source Clock Delay      (SCD):    1.608ns
    Clock Pessimism Removal (CPR):    0.513ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.552     1.608    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X45Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X45Y27         FDRE (Prop_fdre_C_Q)         0.141     1.749 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]/Q
                         net (fo=1, routed)           0.055     1.804    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff[0][4]
    SLICE_X45Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][4]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.816     2.121    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X45Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][4]/C
                         clock pessimism             -0.513     1.608    
    SLICE_X45Y27         FDRE (Hold_fdre_C_D)         0.075     1.683    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][4]
  -------------------------------------------------------------------
                         required time                         -1.683    
                         arrival time                           1.804    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][7]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.123ns
    Source Clock Delay      (SCD):    1.609ns
    Clock Pessimism Removal (CPR):    0.514ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.553     1.609    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X43Y29         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y29         FDRE (Prop_fdre_C_Q)         0.141     1.750 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]/Q
                         net (fo=1, routed)           0.055     1.805    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff[0][7]
    SLICE_X43Y29         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][7]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.818     2.123    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X43Y29         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][7]/C
                         clock pessimism             -0.514     1.609    
    SLICE_X43Y29         FDRE (Hold_fdre_C_D)         0.075     1.684    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][7]
  -------------------------------------------------------------------
                         required time                         -1.684    
                         arrival time                           1.805    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][11]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.122ns
    Source Clock Delay      (SCD):    1.610ns
    Clock Pessimism Removal (CPR):    0.512ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.554     1.610    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X33Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y27         FDRE (Prop_fdre_C_Q)         0.141     1.751 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/Q
                         net (fo=1, routed)           0.055     1.806    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff[0][11]
    SLICE_X33Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][11]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.817     2.122    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X33Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][11]/C
                         clock pessimism             -0.512     1.610    
    SLICE_X33Y27         FDRE (Hold_fdre_C_D)         0.075     1.685    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][11]
  -------------------------------------------------------------------
                         required time                         -1.685    
                         arrival time                           1.806    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][12]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][12]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.153ns
    Source Clock Delay      (SCD):    1.638ns
    Clock Pessimism Removal (CPR):    0.515ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.582     1.638    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X31Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][12]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X31Y30         FDRE (Prop_fdre_C_Q)         0.141     1.779 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][12]/Q
                         net (fo=1, routed)           0.055     1.834    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff[0][12]
    SLICE_X31Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][12]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.848     2.153    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X31Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][12]/C
                         clock pessimism             -0.515     1.638    
    SLICE_X31Y30         FDRE (Hold_fdre_C_D)         0.075     1.713    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[1][12]
  -------------------------------------------------------------------
                         required time                         -1.713    
                         arrival time                           1.834    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][11]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.124ns
    Source Clock Delay      (SCD):    1.610ns
    Clock Pessimism Removal (CPR):    0.514ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.554     1.610    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X43Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y30         FDRE (Prop_fdre_C_Q)         0.141     1.751 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]/Q
                         net (fo=1, routed)           0.055     1.806    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff[0][11]
    SLICE_X43Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][11]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.819     2.124    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X43Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][11]/C
                         clock pessimism             -0.514     1.610    
    SLICE_X43Y30         FDRE (Hold_fdre_C_D)         0.075     1.685    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][11]
  -------------------------------------------------------------------
                         required time                         -1.685    
                         arrival time                           1.806    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.123ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][2]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.199ns  (logic 0.141ns (70.848%)  route 0.058ns (29.152%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.122ns
    Source Clock Delay      (SCD):    1.608ns
    Clock Pessimism Removal (CPR):    0.514ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.552     1.608    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X44Y28         FDRE (Prop_fdre_C_Q)         0.141     1.749 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]/Q
                         net (fo=1, routed)           0.058     1.807    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff[0][2]
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][2]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.817     2.122    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][2]/C
                         clock pessimism             -0.514     1.608    
    SLICE_X44Y28         FDRE (Hold_fdre_C_D)         0.076     1.684    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][2]
  -------------------------------------------------------------------
                         required time                         -1.684    
                         arrival time                           1.807    
  -------------------------------------------------------------------
                         slack                                  0.123    

Slack (MET) :             0.124ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][9]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK  {rise@0.000ns fall@4.000ns period=8.000ns})
  Path Group:             ADC_DCLK
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (ADC_DCLK rise@0.000ns - ADC_DCLK rise@0.000ns)
  Data Path Delay:        0.202ns  (logic 0.141ns (69.956%)  route 0.061ns (30.044%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.122ns
    Source Clock Delay      (SCD):    1.608ns
    Clock Pessimism Removal (CPR):    0.514ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.356     0.356 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.674     1.030    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.026     1.056 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.552     1.608    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X44Y28         FDRE (Prop_fdre_C_Q)         0.141     1.749 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]/Q
                         net (fo=1, routed)           0.061     1.810    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff[0][9]
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][9]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         0.545     0.545 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           0.731     1.276    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.029     1.305 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         0.817     2.122    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][9]/C
                         clock pessimism             -0.514     1.608    
    SLICE_X44Y28         FDRE (Hold_fdre_C_D)         0.078     1.686    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][9]
  -------------------------------------------------------------------
                         required time                         -1.686    
                         arrival time                           1.810    
  -------------------------------------------------------------------
                         slack                                  0.124    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         ADC_DCLK
Waveform(ns):       { 0.000 4.000 }
Period(ns):         8.000
Sources:            { ADC_DCLK }

Check Type        Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.170         8.000       5.830      RAMB36_X1Y5     u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.170         8.000       5.830      RAMB36_X1Y6     u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.170         8.000       5.830      RAMB36_X2Y5     u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.170         8.000       5.830      RAMB36_X2Y6     u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.170         8.000       5.830      RAMB36_X0Y5     u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.170         8.000       5.830      RAMB36_X0Y6     u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/CLKARDCLK
Min Period        n/a     BUFG/I              n/a            1.592         8.000       6.408      BUFGCTRL_X0Y18  ADC_DCLK_IBUF_BUFG_inst/I
Min Period        n/a     FDRE/C              n/a            1.000         8.000       7.000      SLICE_X38Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrp_inst/count_value_i_reg[1]/C
Min Period        n/a     FDRE/C              n/a            1.000         8.000       7.000      SLICE_X38Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrp_inst/count_value_i_reg[2]/C
Min Period        n/a     FDRE/C              n/a            1.000         8.000       7.000      SLICE_X38Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrp_inst/count_value_i_reg[3]/C
Low Pulse Width   Fast    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X38Y42    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/rst_d2_reg_srl3/CLK
Low Pulse Width   Slow    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X38Y42    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/rst_d2_reg_srl3/CLK
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X35Y34    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrpp1_inst/count_value_i_reg[10]/C
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X35Y34    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrpp1_inst/count_value_i_reg[11]/C
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X35Y35    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrpp1_inst/count_value_i_reg[12]/C
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X35Y35    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrpp1_inst/count_value_i_reg[13]/C
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X33Y34    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gaf_wptr_p3.wrpp3_inst/count_value_i_reg[10]/C
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X33Y34    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gaf_wptr_p3.wrpp3_inst/count_value_i_reg[11]/C
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X33Y35    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gaf_wptr_p3.wrpp3_inst/count_value_i_reg[12]/C
Low Pulse Width   Fast    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X33Y35    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gaf_wptr_p3.wrpp3_inst/count_value_i_reg[13]/C
High Pulse Width  Slow    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X38Y42    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/rst_d2_reg_srl3/CLK
High Pulse Width  Fast    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X38Y42    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/rst_d2_reg_srl3/CLK
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X38Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrp_inst/count_value_i_reg[1]/C
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X38Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrp_inst/count_value_i_reg[2]/C
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X38Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/wrp_inst/count_value_i_reg[3]/C
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X44Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]/C
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X44Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]/C
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X44Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]/C
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X45Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]/C
High Pulse Width  Slow    FDRE/C              n/a            0.500         4.000       3.500      SLICE_X45Y28    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]/C



---------------------------------------------------------------------------------------------------
From Clock:  clk_50M
  To Clock:  clk_50M

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        7.000ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_50M
Waveform(ns):       { 0.000 10.000 }
Period(ns):         20.000
Sources:            { clk_50M }

Check Type        Corner  Lib Pin           Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period        n/a     PLLE2_ADV/CLKIN1  n/a            1.249         20.000      18.751     PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKIN1
Max Period        n/a     PLLE2_ADV/CLKIN1  n/a            52.633        20.000      32.633     PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Slow    PLLE2_ADV/CLKIN1  n/a            3.000         10.000      7.000      PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKIN1
Low Pulse Width   Fast    PLLE2_ADV/CLKIN1  n/a            3.000         10.000      7.000      PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Slow    PLLE2_ADV/CLKIN1  n/a            3.000         10.000      7.000      PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKIN1
High Pulse Width  Fast    PLLE2_ADV/CLKIN1  n/a            3.000         10.000      7.000      PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKIN1



---------------------------------------------------------------------------------------------------
From Clock:  clk_out2_clk_wiz_0
  To Clock:  clk_out2_clk_wiz_0

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        6.408ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_out2_clk_wiz_0
Waveform(ns):       { 0.000 4.000 }
Period(ns):         8.000
Sources:            { clk_gen/inst/plle2_adv_inst/CLKOUT1 }

Check Type  Corner  Lib Pin            Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period  n/a     BUFG/I             n/a            1.592         8.000       6.408      BUFGCTRL_X0Y16  clk_gen/inst/clkout2_buf/I
Min Period  n/a     ODDR/C             n/a            1.474         8.000       6.526      OLOGIC_X1Y91    ODDR_inst/C
Min Period  n/a     PLLE2_ADV/CLKOUT1  n/a            1.249         8.000       6.751      PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKOUT1
Max Period  n/a     PLLE2_ADV/CLKOUT1  n/a            160.000       8.000       152.000    PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKOUT1



---------------------------------------------------------------------------------------------------
From Clock:  clkfbout_clk_wiz_0
  To Clock:  clkfbout_clk_wiz_0

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack       18.408ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clkfbout_clk_wiz_0
Waveform(ns):       { 0.000 10.000 }
Period(ns):         20.000
Sources:            { clk_gen/inst/plle2_adv_inst/CLKFBOUT }

Check Type  Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location        Pin
Min Period  n/a     BUFG/I              n/a            1.592         20.000      18.408     BUFGCTRL_X0Y19  clk_gen/inst/clkf_buf/I
Min Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            1.249         20.000      18.751     PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKFBOUT
Min Period  n/a     PLLE2_ADV/CLKFBIN   n/a            1.249         20.000      18.751     PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBIN   n/a            52.633        20.000      32.633     PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKFBIN
Max Period  n/a     PLLE2_ADV/CLKFBOUT  n/a            160.000       20.000      140.000    PLLE2_ADV_X1Y1  clk_gen/inst/plle2_adv_inst/CLKFBOUT



---------------------------------------------------------------------------------------------------
From Clock:  clk_fpga_0
  To Clock:  clk_fpga_0

Setup :            0  Failing Endpoints,  Worst Slack        3.095ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.040ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack        2.500ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             3.095ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[0]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.423ns  (logic 1.696ns (26.405%)  route 4.727ns (73.595%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.908     8.276    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y47         LUT5 (Prop_lut5_I0_O)        0.274     8.550 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1/O
                         net (fo=8, routed)           0.415     8.965    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[0]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[0]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y45         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[0]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.965    
  -------------------------------------------------------------------
                         slack                                  3.095    

Slack (MET) :             3.095ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[1]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.423ns  (logic 1.696ns (26.405%)  route 4.727ns (73.595%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.908     8.276    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y47         LUT5 (Prop_lut5_I0_O)        0.274     8.550 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1/O
                         net (fo=8, routed)           0.415     8.965    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[1]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[1]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y45         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[1]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.965    
  -------------------------------------------------------------------
                         slack                                  3.095    

Slack (MET) :             3.095ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[2]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.423ns  (logic 1.696ns (26.405%)  route 4.727ns (73.595%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.908     8.276    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y47         LUT5 (Prop_lut5_I0_O)        0.274     8.550 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1/O
                         net (fo=8, routed)           0.415     8.965    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[2]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[2]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y45         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[2]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.965    
  -------------------------------------------------------------------
                         slack                                  3.095    

Slack (MET) :             3.095ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[3]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.423ns  (logic 1.696ns (26.405%)  route 4.727ns (73.595%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.908     8.276    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y47         LUT5 (Prop_lut5_I0_O)        0.274     8.550 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1/O
                         net (fo=8, routed)           0.415     8.965    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[3]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[3]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y45         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[3]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.965    
  -------------------------------------------------------------------
                         slack                                  3.095    

Slack (MET) :             3.095ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[5]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.423ns  (logic 1.696ns (26.405%)  route 4.727ns (73.595%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.908     8.276    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y47         LUT5 (Prop_lut5_I0_O)        0.274     8.550 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1/O
                         net (fo=8, routed)           0.415     8.965    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[5]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[5]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y45         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[5]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.965    
  -------------------------------------------------------------------
                         slack                                  3.095    

Slack (MET) :             3.095ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[6]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.423ns  (logic 1.696ns (26.405%)  route 4.727ns (73.595%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.908     8.276    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y47         LUT5 (Prop_lut5_I0_O)        0.274     8.550 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1/O
                         net (fo=8, routed)           0.415     8.965    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[6]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[6]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y45         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[6]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.965    
  -------------------------------------------------------------------
                         slack                                  3.095    

Slack (MET) :             3.095ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[7]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.423ns  (logic 1.696ns (26.405%)  route 4.727ns (73.595%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.908     8.276    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y47         LUT5 (Prop_lut5_I0_O)        0.274     8.550 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1/O
                         net (fo=8, routed)           0.415     8.965    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[7]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y45         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[7]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y45         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2_reg[7]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.965    
  -------------------------------------------------------------------
                         slack                                  3.095    

Slack (MET) :             3.104ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/wdata_reg[6]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.517ns  (logic 1.127ns (17.294%)  route 5.390ns (82.706%))
  Logic Levels:           0  
  Clock Path Skew:        -0.153ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.293ns = ( 12.293 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WDATA[6])
                                                      1.127     3.669 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WDATA[6]
                         net (fo=26, routed)          5.390     9.059    u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/s_axi_lite_wdata[6]
    SLICE_X76Y42         FDRE                                         r  u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/wdata_reg[6]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.311    12.293    u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/s_axi_lite_aclk
    SLICE_X76Y42         FDRE                                         r  u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/wdata_reg[6]/C
                         clock pessimism              0.097    12.390    
                         clock uncertainty           -0.154    12.236    
    SLICE_X76Y42         FDRE (Setup_fdre_C_D)       -0.073    12.163    u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/wdata_reg[6]
  -------------------------------------------------------------------
                         required time                         12.163    
                         arrival time                          -9.059    
  -------------------------------------------------------------------
                         slack                                  3.104    

Slack (MET) :             3.104ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[0]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.414ns  (logic 1.696ns (26.442%)  route 4.718ns (73.558%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.910     8.277    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y46         LUT5 (Prop_lut5_I0_O)        0.274     8.551 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[7]_i_1/O
                         net (fo=8, routed)           0.405     8.956    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[7]_i_1_n_0
    SLICE_X56Y46         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[0]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y46         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[0]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y46         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[0]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.956    
  -------------------------------------------------------------------
                         slack                                  3.104    

Slack (MET) :             3.104ns  (required time - arrival time)
  Source:                 u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
                            (rising edge-triggered cell PS7 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[2]/CE
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (clk_fpga_0 rise@10.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        6.414ns  (logic 1.696ns (26.442%)  route 4.718ns (73.558%))
  Logic Levels:           3  (LUT4=1 LUT5=2)
  Clock Path Skew:        -0.160ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    2.286ns = ( 12.286 - 10.000 ) 
    Source Clock Delay      (SCD):    2.542ns
    Clock Pessimism Removal (CPR):    0.097ns
  Clock Uncertainty:      0.154ns  ((TSJ^2 + TIJ^2)^1/2 + DJ) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Total Input Jitter      (TIJ):    0.300ns
    Discrete Jitter          (DJ):    0.000ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.463     2.542    u1/utg_i/processing_system7_0/inst/M_AXI_GP0_ACLK
    PS7_X0Y0             PS7                                          r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0ACLK
  -------------------------------------------------------------------    -------------------
    PS7_X0Y0             PS7 (Prop_ps7_MAXIGP0ACLK_MAXIGP0WVALID)
                                                      1.033     3.575 r  u1/utg_i/processing_system7_0/inst/PS7_i/MAXIGP0WVALID
                         net (fo=8, routed)           1.918     5.494    u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/s_axi_wvalid[0]
    SLICE_X46Y83         LUT5 (Prop_lut5_I3_O)        0.119     5.613 r  u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/m_axi_wvalid[3]_INST_0/O
                         net (fo=8, routed)           0.485     6.098    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_wvalid
    SLICE_X47Y81         LUT4 (Prop_lut4_I3_O)        0.270     6.368 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg0[5]_i_3/O
                         net (fo=28, routed)          1.910     8.277    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_wren__0
    SLICE_X56Y46         LUT5 (Prop_lut5_I0_O)        0.274     8.551 r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[7]_i_1/O
                         net (fo=8, routed)           0.405     8.956    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[7]_i_1_n_0
    SLICE_X56Y46         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[2]/CE
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.304    12.286    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/s00_axi_aclk
    SLICE_X56Y46         FDRE                                         r  u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[2]/C
                         clock pessimism              0.097    12.383    
                         clock uncertainty           -0.154    12.229    
    SLICE_X56Y46         FDRE (Setup_fdre_C_CE)      -0.168    12.061    u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6_reg[2]
  -------------------------------------------------------------------
                         required time                         12.061    
                         arrival time                          -8.956    
  -------------------------------------------------------------------
                         slack                                  3.104    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.040ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/rom_do_reg[38]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/DADDR_reg[6]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.330ns  (logic 0.128ns (38.847%)  route 0.202ns (61.153%))
  Logic Levels:           0  
  Clock Path Skew:        0.266ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.275ns
    Source Clock Delay      (SCD):    0.979ns
    Clock Pessimism Removal (CPR):    0.030ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.643     0.979    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/s00_axi_aclk
    SLICE_X110Y49        FDRE                                         r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/rom_do_reg[38]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X110Y49        FDRE (Prop_fdre_C_Q)         0.128     1.107 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/rom_do_reg[38]/Q
                         net (fo=1, routed)           0.202     1.308    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/p_0_in[6]
    SLICE_X110Y50        FDRE                                         r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/DADDR_reg[6]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.909     1.275    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/s00_axi_aclk
    SLICE_X110Y50        FDRE                                         r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/DADDR_reg[6]/C
                         clock pessimism             -0.030     1.245    
    SLICE_X110Y50        FDRE (Hold_fdre_C_D)         0.023     1.268    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/DADDR_reg[6]
  -------------------------------------------------------------------
                         required time                         -1.268    
                         arrival time                           1.308    
  -------------------------------------------------------------------
                         slack                                  0.040    

Slack (MET) :             0.055ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_accum_reg[data][28]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[100].srl_nx1/shift_reg_reg[0]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.254ns  (logic 0.141ns (55.504%)  route 0.113ns (44.496%))
  Logic Levels:           0  
  Clock Path Skew:        0.016ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.211ns
    Source Clock Delay      (SCD):    0.913ns
    Clock Pessimism Removal (CPR):    0.282ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.577     0.913    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/aclk
    SLICE_X28Y50         FDRE                                         r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_accum_reg[data][28]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X28Y50         FDRE (Prop_fdre_C_Q)         0.141     1.054 r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_accum_reg[data][28]/Q
                         net (fo=2, routed)           0.113     1.167    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[100].srl_nx1/w_accum_mesg[0]
    SLICE_X30Y51         SRLC32E                                      r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[100].srl_nx1/shift_reg_reg[0]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.845     1.211    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[100].srl_nx1/aclk
    SLICE_X30Y51         SRLC32E                                      r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[100].srl_nx1/shift_reg_reg[0]_srl32/CLK
                         clock pessimism             -0.282     0.929    
    SLICE_X30Y51         SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.183     1.112    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[100].srl_nx1/shift_reg_reg[0]_srl32
  -------------------------------------------------------------------
                         required time                         -1.112    
                         arrival time                           1.167    
  -------------------------------------------------------------------
                         slack                                  0.055    

Slack (MET) :             0.064ns  (arrival time - required time)
  Source:                 u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/slv_reg5_reg[27]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_rdata_reg[27]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.440ns  (logic 0.248ns (56.398%)  route 0.192ns (43.602%))
  Logic Levels:           2  (LUT6=1 MUXF7=1)
  Clock Path Skew:        0.270ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.192ns
    Source Clock Delay      (SCD):    0.892ns
    Clock Pessimism Removal (CPR):    0.030ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.556     0.892    u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/s00_axi_aclk
    SLICE_X49Y51         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/slv_reg5_reg[27]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X49Y51         FDRE (Prop_fdre_C_Q)         0.141     1.033 r  u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/slv_reg5_reg[27]/Q
                         net (fo=4, routed)           0.192     1.224    u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/m00_axi_araddr[27]
    SLICE_X51Y49         LUT6 (Prop_lut6_I2_O)        0.045     1.269 r  u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_rdata[27]_i_2/O
                         net (fo=1, routed)           0.000     1.269    u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_rdata[27]_i_2_n_0
    SLICE_X51Y49         MUXF7 (Prop_muxf7_I0_O)      0.062     1.331 r  u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_rdata_reg[27]_i_1/O
                         net (fo=1, routed)           0.000     1.331    u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/reg_data_out[27]
    SLICE_X51Y49         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_rdata_reg[27]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.826     1.192    u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/s00_axi_aclk
    SLICE_X51Y49         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_rdata_reg[27]/C
                         clock pessimism             -0.030     1.162    
    SLICE_X51Y49         FDRE (Hold_fdre_C_D)         0.105     1.267    u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_rdata_reg[27]
  -------------------------------------------------------------------
                         required time                         -1.267    
                         arrival time                           1.331    
  -------------------------------------------------------------------
                         slack                                  0.064    

Slack (MET) :             0.066ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_accum_reg[strb][0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[68].srl_nx1/shift_reg_reg[0]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.197ns
    Source Clock Delay      (SCD):    0.901ns
    Clock Pessimism Removal (CPR):    0.283ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.565     0.901    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/aclk
    SLICE_X33Y49         FDRE                                         r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_accum_reg[strb][0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y49         FDRE (Prop_fdre_C_Q)         0.141     1.042 r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_accum_reg[strb][0]/Q
                         net (fo=1, routed)           0.055     1.097    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[68].srl_nx1/w_accum_mesg[0]
    SLICE_X32Y49         SRLC32E                                      r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[68].srl_nx1/shift_reg_reg[0]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.831     1.197    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[68].srl_nx1/aclk
    SLICE_X32Y49         SRLC32E                                      r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[68].srl_nx1/shift_reg_reg[0]_srl32/CLK
                         clock pessimism             -0.283     0.914    
    SLICE_X32Y49         SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.117     1.031    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[68].srl_nx1/shift_reg_reg[0]_srl32
  -------------------------------------------------------------------
                         required time                         -1.031    
                         arrival time                           1.097    
  -------------------------------------------------------------------
                         slack                                  0.066    

Slack (MET) :             0.076ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][1]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.256ns  (logic 0.141ns (55.003%)  route 0.115ns (44.997%))
  Logic Levels:           0  
  Clock Path Skew:        0.033ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.209ns
    Source Clock Delay      (SCD):    0.912ns
    Clock Pessimism Removal (CPR):    0.264ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.576     0.912    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/s_sc_aclk
    SLICE_X29Y54         FDRE                                         r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X29Y54         FDRE (Prop_fdre_C_Q)         0.141     1.053 r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][1]/Q
                         net (fo=1, routed)           0.115     1.168    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/DIA0
    SLICE_X26Y54         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.843     1.209    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/WCLK
    SLICE_X26Y54         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMA/CLK
                         clock pessimism             -0.264     0.945    
    SLICE_X26Y54         RAMD32 (Hold_ramd32_CLK_I)
                                                      0.147     1.092    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMA
  -------------------------------------------------------------------
                         required time                         -1.092    
                         arrival time                           1.168    
  -------------------------------------------------------------------
                         slack                                  0.076    

Slack (MET) :             0.077ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/ar_reg_slice/m_vector_i_reg[1065]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_96_101/RAMB/I
                            (rising edge-triggered cell RAMD32 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.257ns  (logic 0.141ns (54.966%)  route 0.116ns (45.034%))
  Logic Levels:           0  
  Clock Path Skew:        0.034ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.188ns
    Source Clock Delay      (SCD):    0.890ns
    Clock Pessimism Removal (CPR):    0.264ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.554     0.890    u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/ar_reg_slice/aclk
    SLICE_X45Y60         FDRE                                         r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/ar_reg_slice/m_vector_i_reg[1065]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X45Y60         FDRE (Prop_fdre_C_Q)         0.141     1.031 r  u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/ar_reg_slice/m_vector_i_reg[1065]/Q
                         net (fo=1, routed)           0.116     1.146    u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_96_101/DIB0
    SLICE_X42Y61         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_96_101/RAMB/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.822     1.188    u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_96_101/WCLK
    SLICE_X42Y61         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_96_101/RAMB/CLK
                         clock pessimism             -0.264     0.924    
    SLICE_X42Y61         RAMD32 (Hold_ramd32_CLK_I)
                                                      0.146     1.070    u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_96_101/RAMB
  -------------------------------------------------------------------
                         required time                         -1.070    
                         arrival time                           1.146    
  -------------------------------------------------------------------
                         slack                                  0.077    

Slack (MET) :             0.078ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][7]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_60_65/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.257ns  (logic 0.141ns (54.966%)  route 0.116ns (45.034%))
  Logic Levels:           0  
  Clock Path Skew:        0.032ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.208ns
    Source Clock Delay      (SCD):    0.912ns
    Clock Pessimism Removal (CPR):    0.264ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.576     0.912    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/s_sc_aclk
    SLICE_X29Y56         FDRE                                         r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][7]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X29Y56         FDRE (Prop_fdre_C_Q)         0.141     1.053 r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][7]/Q
                         net (fo=1, routed)           0.116     1.168    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_60_65/DIA0
    SLICE_X26Y57         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_60_65/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.842     1.208    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_60_65/WCLK
    SLICE_X26Y57         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_60_65/RAMA/CLK
                         clock pessimism             -0.264     0.944    
    SLICE_X26Y57         RAMD32 (Hold_ramd32_CLK_I)
                                                      0.147     1.091    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_60_65/RAMA
  -------------------------------------------------------------------
                         required time                         -1.091    
                         arrival time                           1.168    
  -------------------------------------------------------------------
                         slack                                  0.078    

Slack (MET) :             0.079ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][5]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMC/I
                            (rising edge-triggered cell RAMD32 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.256ns  (logic 0.141ns (55.003%)  route 0.115ns (44.997%))
  Logic Levels:           0  
  Clock Path Skew:        0.033ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.209ns
    Source Clock Delay      (SCD):    0.912ns
    Clock Pessimism Removal (CPR):    0.264ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.576     0.912    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/s_sc_aclk
    SLICE_X29Y54         FDRE                                         r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X29Y54         FDRE (Prop_fdre_C_Q)         0.141     1.053 r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][4][userdata][5]/Q
                         net (fo=1, routed)           0.115     1.168    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/DIC0
    SLICE_X26Y54         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMC/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.843     1.209    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/WCLK
    SLICE_X26Y54         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMC/CLK
                         clock pessimism             -0.264     0.945    
    SLICE_X26Y54         RAMD32 (Hold_ramd32_CLK_I)
                                                      0.144     1.089    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_54_59/RAMC
  -------------------------------------------------------------------
                         required time                         -1.089    
                         arrival time                           1.168    
  -------------------------------------------------------------------
                         slack                                  0.079    

Slack (MET) :             0.081ns  (arrival time - required time)
  Source:                 u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][0][userdata][1]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_18_23/RAMA/I
                            (rising edge-triggered cell RAMD32 clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.260ns  (logic 0.141ns (54.238%)  route 0.119ns (45.762%))
  Logic Levels:           0  
  Clock Path Skew:        0.032ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.209ns
    Source Clock Delay      (SCD):    0.913ns
    Clock Pessimism Removal (CPR):    0.264ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.577     0.913    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/s_sc_aclk
    SLICE_X28Y52         FDRE                                         r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][0][userdata][1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X28Y52         FDRE (Prop_fdre_C_Q)         0.141     1.054 r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum_reg[bytes][0][userdata][1]/Q
                         net (fo=1, routed)           0.119     1.173    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_18_23/DIA0
    SLICE_X26Y53         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_18_23/RAMA/I
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.843     1.209    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_18_23/WCLK
    SLICE_X26Y53         RAMD32                                       r  u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_18_23/RAMA/CLK
                         clock pessimism             -0.264     0.945    
    SLICE_X26Y53         RAMD32 (Hold_ramd32_CLK_I)
                                                      0.147     1.092    u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_18_23/RAMA
  -------------------------------------------------------------------
                         required time                         -1.092    
                         arrival time                           1.173    
  -------------------------------------------------------------------
                         slack                                  0.081    

Slack (MET) :             0.081ns  (arrival time - required time)
  Source:                 u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[5]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][6]_srl32/D
                            (rising edge-triggered cell SRLC32E clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (clk_fpga_0 rise@0.000ns - clk_fpga_0 rise@0.000ns)
  Data Path Delay:        0.301ns  (logic 0.141ns (46.809%)  route 0.160ns (53.191%))
  Logic Levels:           0  
  Clock Path Skew:        0.037ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    1.209ns
    Source Clock Delay      (SCD):    0.908ns
    Clock Pessimism Removal (CPR):    0.264ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.572     0.908    u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/aclk
    SLICE_X27Y90         FDRE                                         r  u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X27Y90         FDRE (Prop_fdre_C_Q)         0.141     1.049 r  u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_arid_r_reg[5]/Q
                         net (fo=1, routed)           0.160     1.209    u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/in[6]
    SLICE_X30Y92         SRLC32E                                      r  u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][6]_srl32/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.843     1.209    u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/aclk
    SLICE_X30Y92         SRLC32E                                      r  u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][6]_srl32/CLK
                         clock pessimism             -0.264     0.945    
    SLICE_X30Y92         SRLC32E (Hold_srlc32e_CLK_D)
                                                      0.183     1.128    u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/memory_reg[31][6]_srl32
  -------------------------------------------------------------------
                         required time                         -1.128    
                         arrival time                           1.209    
  -------------------------------------------------------------------
                         slack                                  0.081    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         clk_fpga_0
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0] }

Check Type        Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location         Pin
Min Period        n/a     MMCME2_ADV/DCLK     n/a            4.999         10.000      5.001      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/DCLK
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.472         10.000      7.528      RAMB36_X3Y6      u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKARDCLK  n/a            2.472         10.000      7.528      RAMB36_X3Y7      u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/CLKARDCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X1Y5      u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/CLKBWRCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X3Y6      u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/CLKBWRCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X1Y6      u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/CLKBWRCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X2Y5      u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/CLKBWRCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X2Y6      u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/CLKBWRCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X0Y5      u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/CLKBWRCLK
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X0Y6      u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/CLKBWRCLK
Max Period        n/a     MMCME2_ADV/CLKIN1   n/a            100.000       10.000      90.000     MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKIN1
Low Pulse Width   Slow    MMCME2_ADV/DCLK     n/a            2.500         5.000       2.500      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/DCLK
Low Pulse Width   Fast    MMCME2_ADV/DCLK     n/a            2.500         5.000       2.500      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/DCLK
Low Pulse Width   Fast    MMCME2_ADV/CLKIN1   n/a            2.000         5.000       3.000      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKIN1
Low Pulse Width   Slow    MMCME2_ADV/CLKIN1   n/a            2.000         5.000       3.000      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKIN1
Low Pulse Width   Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X38Y61     u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_102_107/RAMA/CLK
Low Pulse Width   Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X38Y61     u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_102_107/RAMA_D1/CLK
Low Pulse Width   Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X38Y61     u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_102_107/RAMB/CLK
Low Pulse Width   Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X38Y61     u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_102_107/RAMB_D1/CLK
Low Pulse Width   Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X38Y61     u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_102_107/RAMC/CLK
Low Pulse Width   Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X38Y61     u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_102_107/RAMC_D1/CLK
High Pulse Width  Slow    MMCME2_ADV/DCLK     n/a            2.500         5.000       2.501      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/DCLK
High Pulse Width  Fast    MMCME2_ADV/DCLK     n/a            2.500         5.000       2.501      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/DCLK
High Pulse Width  Slow    MMCME2_ADV/CLKIN1   n/a            2.000         5.000       3.000      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKIN1
High Pulse Width  Fast    MMCME2_ADV/CLKIN1   n/a            2.000         5.000       3.000      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKIN1
High Pulse Width  Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X30Y55     u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_48_53/RAMA/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X30Y55     u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_48_53/RAMA_D1/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X30Y55     u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_48_53/RAMB/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X30Y55     u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_48_53/RAMB_D1/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X30Y55     u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_48_53/RAMC/CLK
High Pulse Width  Slow    RAMD32/CLK          n/a            1.130         5.000       3.870      SLICE_X30Y55     u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0_31_48_53/RAMC_D1/CLK



---------------------------------------------------------------------------------------------------
From Clock:  I
  To Clock:  I

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        0.530ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         I
Waveform(ns):       { 0.000 1.000 }
Period(ns):         2.000
Sources:            { u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0 }

Check Type  Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location         Pin
Min Period  n/a     BUFR/I              n/a            1.470         2.000       0.530      BUFR_X1Y5        u1/utg_i/axi_dynclk_0/U0/BUFR_inst/I
Min Period  n/a     MMCME2_ADV/CLKOUT0  n/a            1.249         2.000       0.751      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
Max Period  n/a     MMCME2_ADV/CLKOUT0  n/a            213.360       2.000       211.360    MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0



---------------------------------------------------------------------------------------------------
From Clock:  LCD_CLK_OBUF
  To Clock:  LCD_CLK_OBUF

Setup :            0  Failing Endpoints,  Worst Slack        3.409ns,  Total Violation        0.000ns
Hold  :            0  Failing Endpoints,  Worst Slack        0.103ns,  Total Violation        0.000ns
PW    :            0  Failing Endpoints,  Worst Slack        3.146ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             3.409ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[13]/R
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        6.160ns  (logic 0.484ns (7.857%)  route 5.676ns (92.143%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.012ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.767ns = ( 14.767 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.785    11.364    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X52Y54         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[13]/R
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.792    14.767    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X52Y54         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[13]/C
                         clock pessimism              0.425    15.192    
                         clock uncertainty           -0.066    15.126    
    SLICE_X52Y54         FDRE (Setup_fdre_C_R)       -0.352    14.774    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[13]
  -------------------------------------------------------------------
                         required time                         14.774    
                         arrival time                         -11.364    
  -------------------------------------------------------------------
                         slack                                  3.409    

Slack (MET) :             3.480ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[31].GEN_MUX_REG.data_out_reg_reg[31][17]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        6.361ns  (logic 0.484ns (7.609%)  route 5.877ns (92.391%))
  Logic Levels:           1  (LUT3=1)
  Clock Path Skew:        -0.124ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.656ns = ( 14.656 - 10.000 ) 
    Source Clock Delay      (SCD):    5.205ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.805     5.205    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X56Y78         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X56Y78         FDRE (Prop_fdre_C_Q)         0.379     5.584 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]/Q
                         net (fo=364, routed)         5.877    11.461    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync[2]_2[34]
    SLICE_X91Y56         LUT3 (Prop_lut3_I1_O)        0.105    11.566 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/GEN_TREE.GEN_BRANCH[31].GEN_MUX_REG.data_out_reg[31][17]_i_1/O
                         net (fo=1, routed)           0.000    11.566    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[31].GEN_MUX_REG.data_out_reg_reg[31][17]_0
    SLICE_X91Y56         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[31].GEN_MUX_REG.data_out_reg_reg[31][17]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.681    14.656    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/vid_aclk
    SLICE_X91Y56         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[31].GEN_MUX_REG.data_out_reg_reg[31][17]/C
                         clock pessimism              0.425    15.081    
                         clock uncertainty           -0.066    15.015    
    SLICE_X91Y56         FDRE (Setup_fdre_C_D)        0.032    15.047    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[31].GEN_MUX_REG.data_out_reg_reg[31][17]
  -------------------------------------------------------------------
                         required time                         15.047    
                         arrival time                         -11.566    
  -------------------------------------------------------------------
                         slack                                  3.480    

Slack (MET) :             3.504ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg_reg[4][4]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        6.437ns  (logic 0.683ns (10.611%)  route 5.754ns (89.389%))
  Logic Levels:           2  (LUT6=1 MUXF7=1)
  Clock Path Skew:        -0.053ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.727ns = ( 14.727 - 10.000 ) 
    Source Clock Delay      (SCD):    5.205ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.805     5.205    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X56Y78         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X56Y78         FDRE (Prop_fdre_C_Q)         0.379     5.584 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]/Q
                         net (fo=364, routed)         5.754    11.338    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/ipif_addr_out[0]
    SLICE_X65Y54         LUT6 (Prop_lut6_I4_O)        0.105    11.443 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg[4][4]_i_2__0/O
                         net (fo=1, routed)           0.000    11.443    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg[4][4]_i_2__0_n_0
    SLICE_X65Y54         MUXF7 (Prop_muxf7_I0_O)      0.199    11.642 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg_reg[4][4]_i_1__0/O
                         net (fo=1, routed)           0.000    11.642    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg_reg[4][4]_0
    SLICE_X65Y54         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg_reg[4][4]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.752    14.727    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/vid_aclk
    SLICE_X65Y54         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg_reg[4][4]/C
                         clock pessimism              0.425    15.152    
                         clock uncertainty           -0.066    15.086    
    SLICE_X65Y54         FDRE (Setup_fdre_C_D)        0.060    15.146    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_TREE.GEN_BRANCH[4].GEN_MUX_REG.data_out_reg_reg[4][4]
  -------------------------------------------------------------------
                         required time                         15.146    
                         arrival time                         -11.642    
  -------------------------------------------------------------------
                         slack                                  3.504    

Slack (MET) :             3.528ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][26]/S
                            (rising edge-triggered cell FDSE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        6.029ns  (logic 0.484ns (8.027%)  route 5.545ns (91.973%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.024ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.755ns = ( 14.755 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.654    11.233    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X53Y72         FDSE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][26]/S
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.780    14.755    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X53Y72         FDSE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][26]/C
                         clock pessimism              0.425    15.180    
                         clock uncertainty           -0.066    15.114    
    SLICE_X53Y72         FDSE (Setup_fdse_C_S)       -0.352    14.762    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][26]
  -------------------------------------------------------------------
                         required time                         14.762    
                         arrival time                         -11.233    
  -------------------------------------------------------------------
                         slack                                  3.528    

Slack (MET) :             3.528ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][27]/R
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        6.029ns  (logic 0.484ns (8.027%)  route 5.545ns (91.973%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.024ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.755ns = ( 14.755 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.654    11.233    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X53Y72         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][27]/R
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.780    14.755    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X53Y72         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][27]/C
                         clock pessimism              0.425    15.180    
                         clock uncertainty           -0.066    15.114    
    SLICE_X53Y72         FDRE (Setup_fdre_C_R)       -0.352    14.762    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][27]
  -------------------------------------------------------------------
                         required time                         14.762    
                         arrival time                         -11.233    
  -------------------------------------------------------------------
                         slack                                  3.528    

Slack (MET) :             3.528ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][28]/R
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        6.029ns  (logic 0.484ns (8.027%)  route 5.545ns (91.973%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.024ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.755ns = ( 14.755 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.654    11.233    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X53Y72         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][28]/R
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.780    14.755    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X53Y72         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][28]/C
                         clock pessimism              0.425    15.180    
                         clock uncertainty           -0.066    15.114    
    SLICE_X53Y72         FDRE (Setup_fdre_C_R)       -0.352    14.762    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][28]
  -------------------------------------------------------------------
                         required time                         14.762    
                         arrival time                         -11.233    
  -------------------------------------------------------------------
                         slack                                  3.528    

Slack (MET) :             3.550ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][0]/R
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        5.906ns  (logic 0.484ns (8.196%)  route 5.422ns (91.804%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.055ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.724ns = ( 14.724 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.531    11.110    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][0]/R
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.749    14.724    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][0]/C
                         clock pessimism              0.425    15.149    
                         clock uncertainty           -0.066    15.083    
    SLICE_X54Y61         FDRE (Setup_fdre_C_R)       -0.423    14.660    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][0]
  -------------------------------------------------------------------
                         required time                         14.660    
                         arrival time                         -11.110    
  -------------------------------------------------------------------
                         slack                                  3.550    

Slack (MET) :             3.550ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][12]/R
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        5.906ns  (logic 0.484ns (8.196%)  route 5.422ns (91.804%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.055ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.724ns = ( 14.724 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.531    11.110    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][12]/R
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.749    14.724    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][12]/C
                         clock pessimism              0.425    15.149    
                         clock uncertainty           -0.066    15.083    
    SLICE_X54Y61         FDRE (Setup_fdre_C_R)       -0.423    14.660    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][12]
  -------------------------------------------------------------------
                         required time                         14.660    
                         arrival time                         -11.110    
  -------------------------------------------------------------------
                         slack                                  3.550    

Slack (MET) :             3.550ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][19]/R
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        5.906ns  (logic 0.484ns (8.196%)  route 5.422ns (91.804%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.055ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.724ns = ( 14.724 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.531    11.110    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][19]/R
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.749    14.724    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][19]/C
                         clock pessimism              0.425    15.149    
                         clock uncertainty           -0.066    15.083    
    SLICE_X54Y61         FDRE (Setup_fdre_C_R)       -0.423    14.660    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][19]
  -------------------------------------------------------------------
                         required time                         14.660    
                         arrival time                         -11.110    
  -------------------------------------------------------------------
                         slack                                  3.550    

Slack (MET) :             3.550ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][20]/R
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (LCD_CLK_OBUF rise@10.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        5.906ns  (logic 0.484ns (8.196%)  route 5.422ns (91.804%))
  Logic Levels:           1  (LUT1=1)
  Clock Path Skew:        -0.055ns (DCD - SCD + CPR)
    Destination Clock Delay (DCD):    4.724ns = ( 14.724 - 10.000 ) 
    Source Clock Delay      (SCD):    5.204ns
    Clock Pessimism Removal (CPR):    0.425ns
  Clock Uncertainty:      0.066ns  ((TSJ^2 + DJ^2)^1/2) / 2 + PE
    Total System Jitter     (TSJ):    0.071ns
    Discrete Jitter          (DJ):    0.112ns
    Phase Error              (PE):    0.000ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.804     5.204    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X59Y77         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y77         FDRE (Prop_fdre_C_Q)         0.379     5.583 f  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.soft_resetn_reg/Q
                         net (fo=64, routed)          1.891     7.474    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/resetn_out
    SLICE_X89Y74         LUT1 (Prop_lut1_I0_O)        0.105     7.579 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1/O
                         net (fo=1030, routed)        3.531    11.110    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][20]/R
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                     10.000    10.000 r  
    PS7_X0Y0             PS7                          0.000    10.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905    10.906    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077    10.983 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367    12.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073    12.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827    13.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725    13.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.749    14.724    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X54Y61         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][20]/C
                         clock pessimism              0.425    15.149    
                         clock uncertainty           -0.066    15.083    
    SLICE_X54Y61         FDRE (Setup_fdre_C_R)       -0.423    14.660    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[26][20]
  -------------------------------------------------------------------
                         required time                         14.660    
                         arrival time                         -11.110    
  -------------------------------------------------------------------
                         slack                                  3.550    





Min Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             0.103ns  (arrival time - required time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[29][18]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[49].GEN_MUX_REG.data_out_reg_reg[49][18]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.237ns  (logic 0.186ns (78.333%)  route 0.051ns (21.667%))
  Logic Levels:           1  (LUT3=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.465ns
    Source Clock Delay      (SCD):    1.924ns
    Clock Pessimism Removal (CPR):    0.528ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.315     1.924    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X87Y54         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[29][18]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X87Y54         FDRE (Prop_fdre_C_Q)         0.141     2.065 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[29][18]/Q
                         net (fo=1, routed)           0.051     2.116    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/genr_regs[239]
    SLICE_X86Y54         LUT3 (Prop_lut3_I0_O)        0.045     2.161 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/GEN_TREE.GEN_BRANCH[49].GEN_MUX_REG.data_out_reg[49][18]_i_1/O
                         net (fo=1, routed)           0.000     2.161    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/p_0_in[18]
    SLICE_X86Y54         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[49].GEN_MUX_REG.data_out_reg_reg[49][18]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.354     2.465    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/vid_aclk
    SLICE_X86Y54         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[49].GEN_MUX_REG.data_out_reg_reg[49][18]/C
                         clock pessimism             -0.528     1.937    
    SLICE_X86Y54         FDRE (Hold_fdre_C_D)         0.121     2.058    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[49].GEN_MUX_REG.data_out_reg_reg[49][18]
  -------------------------------------------------------------------
                         required time                         -2.058    
                         arrival time                           2.161    
  -------------------------------------------------------------------
                         slack                                  0.103    

Slack (MET) :             0.116ns  (arrival time - required time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.intr_err_reg[27]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[32].GEN_MUX_REG.data_out_reg_reg[32][27]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.250ns  (logic 0.186ns (74.483%)  route 0.064ns (25.517%))
  Logic Levels:           1  (LUT3=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.456ns
    Source Clock Delay      (SCD):    1.918ns
    Clock Pessimism Removal (CPR):    0.525ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.309     1.918    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X55Y74         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.intr_err_reg[27]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X55Y74         FDRE (Prop_fdre_C_Q)         0.141     2.059 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.intr_err_reg[27]/Q
                         net (fo=2, routed)           0.064     2.122    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/intr_err[27]
    SLICE_X54Y74         LUT3 (Prop_lut3_I2_O)        0.045     2.167 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/GEN_TREE.GEN_BRANCH[32].GEN_MUX_REG.data_out_reg[32][27]_i_1/O
                         net (fo=1, routed)           0.000     2.167    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[32].GEN_MUX_REG.data_out_reg_reg[32][27]_0
    SLICE_X54Y74         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[32].GEN_MUX_REG.data_out_reg_reg[32][27]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.345     2.456    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/vid_aclk
    SLICE_X54Y74         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[32].GEN_MUX_REG.data_out_reg_reg[32][27]/C
                         clock pessimism             -0.525     1.931    
    SLICE_X54Y74         FDRE (Hold_fdre_C_D)         0.121     2.052    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[32].GEN_MUX_REG.data_out_reg_reg[32][27]
  -------------------------------------------------------------------
                         required time                         -2.052    
                         arrival time                           2.167    
  -------------------------------------------------------------------
                         slack                                  0.116    

Slack (MET) :             0.117ns  (arrival time - required time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[23][18]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[46].GEN_MUX_REG.data_out_reg_reg[46][18]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.251ns  (logic 0.186ns (74.070%)  route 0.065ns (25.930%))
  Logic Levels:           1  (LUT3=1)
  Clock Path Skew:        0.013ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.464ns
    Source Clock Delay      (SCD):    1.925ns
    Clock Pessimism Removal (CPR):    0.526ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.316     1.925    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/vid_aclk
    SLICE_X83Y60         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[23][18]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X83Y60         FDRE (Prop_fdre_C_Q)         0.141     2.066 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs_int_reg[23][18]/Q
                         net (fo=2, routed)           0.065     2.131    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/genr_regs[89]
    SLICE_X82Y60         LUT3 (Prop_lut3_I0_O)        0.045     2.176 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/GEN_TREE.GEN_BRANCH[46].GEN_MUX_REG.data_out_reg[46][18]_i_1/O
                         net (fo=1, routed)           0.000     2.176    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[46].GEN_MUX_REG.data_out_reg_reg[46][18]_0
    SLICE_X82Y60         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[46].GEN_MUX_REG.data_out_reg_reg[46][18]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.353     2.464    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/vid_aclk
    SLICE_X82Y60         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[46].GEN_MUX_REG.data_out_reg_reg[46][18]/C
                         clock pessimism             -0.526     1.938    
    SLICE_X82Y60         FDRE (Hold_fdre_C_D)         0.121     2.059    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_TREE.GEN_BRANCH[46].GEN_MUX_REG.data_out_reg_reg[46][18]
  -------------------------------------------------------------------
                         required time                         -2.059    
                         arrival time                           2.176    
  -------------------------------------------------------------------
                         slack                                  0.117    

Slack (MET) :             0.120ns  (arrival time - required time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[1][3]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.472ns
    Source Clock Delay      (SCD):    1.931ns
    Clock Pessimism Removal (CPR):    0.541ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.322     1.931    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_clk
    SLICE_X57Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y53         FDRE (Prop_fdre_C_Q)         0.141     2.072 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/Q
                         net (fo=1, routed)           0.055     2.127    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff[0][3]
    SLICE_X57Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[1][3]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.361     2.472    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_clk
    SLICE_X57Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[1][3]/C
                         clock pessimism             -0.541     1.931    
    SLICE_X57Y53         FDRE (Hold_fdre_C_D)         0.076     2.007    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[1][3]
  -------------------------------------------------------------------
                         required time                         -2.007    
                         arrival time                           2.127    
  -------------------------------------------------------------------
                         slack                                  0.120    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][12]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][12]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.482ns
    Source Clock Delay      (SCD):    1.942ns
    Clock Pessimism Removal (CPR):    0.540ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.333     1.942    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X51Y67         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][12]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y67         FDRE (Prop_fdre_C_Q)         0.141     2.083 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][12]/Q
                         net (fo=1, routed)           0.055     2.138    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync[0]_0[12]
    SLICE_X51Y67         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][12]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.371     2.482    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X51Y67         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][12]/C
                         clock pessimism             -0.540     1.942    
    SLICE_X51Y67         FDRE (Hold_fdre_C_D)         0.075     2.017    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][12]
  -------------------------------------------------------------------
                         required time                         -2.017    
                         arrival time                           2.138    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][19]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][19]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.489ns
    Source Clock Delay      (SCD):    1.947ns
    Clock Pessimism Removal (CPR):    0.542ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.338     1.947    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X51Y58         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][19]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X51Y58         FDRE (Prop_fdre_C_Q)         0.141     2.088 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][19]/Q
                         net (fo=1, routed)           0.055     2.143    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync[0]_0[19]
    SLICE_X51Y58         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][19]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.378     2.489    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X51Y58         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][19]/C
                         clock pessimism             -0.542     1.947    
    SLICE_X51Y58         FDRE (Hold_fdre_C_D)         0.075     2.022    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][19]
  -------------------------------------------------------------------
                         required time                         -2.022    
                         arrival time                           2.143    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][3]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][3]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.467ns
    Source Clock Delay      (SCD):    1.927ns
    Clock Pessimism Removal (CPR):    0.540ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.318     1.927    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X81Y57         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][3]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X81Y57         FDRE (Prop_fdre_C_Q)         0.141     2.068 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][3]/Q
                         net (fo=1, routed)           0.055     2.123    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync[0]_0[3]
    SLICE_X81Y57         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][3]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.356     2.467    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X81Y57         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][3]/C
                         clock pessimism             -0.540     1.927    
    SLICE_X81Y57         FDRE (Hold_fdre_C_D)         0.075     2.002    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][3]
  -------------------------------------------------------------------
                         required time                         -2.002    
                         arrival time                           2.123    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][44]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][44]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.461ns
    Source Clock Delay      (SCD):    1.922ns
    Clock Pessimism Removal (CPR):    0.539ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.313     1.922    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X59Y79         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][44]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y79         FDRE (Prop_fdre_C_Q)         0.141     2.063 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][44]/Q
                         net (fo=1, routed)           0.055     2.118    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync[0]_0[44]
    SLICE_X59Y79         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][44]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.350     2.461    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/vid_aclk
    SLICE_X59Y79         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][44]/C
                         clock pessimism             -0.539     1.922    
    SLICE_X59Y79         FDRE (Hold_fdre_C_D)         0.075     1.997    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[1][44]
  -------------------------------------------------------------------
                         required time                         -1.997    
                         arrival time                           2.118    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][0]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.473ns
    Source Clock Delay      (SCD):    1.932ns
    Clock Pessimism Removal (CPR):    0.541ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.323     1.932    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X59Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y50         FDRE (Prop_fdre_C_Q)         0.141     2.073 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]/Q
                         net (fo=1, routed)           0.055     2.128    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff[0][0]
    SLICE_X59Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][0]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.362     2.473    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X59Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][0]/C
                         clock pessimism             -0.541     1.932    
    SLICE_X59Y50         FDRE (Hold_fdre_C_D)         0.075     2.007    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][0]
  -------------------------------------------------------------------
                         required time                         -2.007    
                         arrival time                           2.128    
  -------------------------------------------------------------------
                         slack                                  0.121    

Slack (MET) :             0.121ns  (arrival time - required time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][10]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Hold (Min at Fast Process Corner)
  Requirement:            0.000ns  (LCD_CLK_OBUF rise@0.000ns - LCD_CLK_OBUF rise@0.000ns)
  Data Path Delay:        0.196ns  (logic 0.141ns (71.838%)  route 0.055ns (28.162%))
  Logic Levels:           0  
  Clock Path Skew:        0.000ns (DCD - SCD - CPR)
    Destination Clock Delay (DCD):    2.471ns
    Source Clock Delay      (SCD):    1.931ns
    Clock Pessimism Removal (CPR):    0.540ns

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.310     0.310    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.026     0.336 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.590     0.926    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.050     0.976 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.363     1.339    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.270     1.609 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.322     1.931    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X61Y51         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y51         FDRE (Prop_fdre_C_Q)         0.141     2.072 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]/Q
                         net (fo=1, routed)           0.055     2.127    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff[0][10]
    SLICE_X61Y51         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][10]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.337     0.337    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.029     0.366 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        0.859     1.225    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.053     1.278 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.402     1.680    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.431     2.111 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.360     2.471    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X61Y51         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][10]/C
                         clock pessimism             -0.540     1.931    
    SLICE_X61Y51         FDRE (Hold_fdre_C_D)         0.075     2.006    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[1][10]
  -------------------------------------------------------------------
                         required time                         -2.006    
                         arrival time                           2.127    
  -------------------------------------------------------------------
                         slack                                  0.121    





Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         LCD_CLK_OBUF
Waveform(ns):       { 0.000 4.000 }
Period(ns):         10.000
Sources:            { u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O }

Check Type        Corner  Lib Pin             Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location      Pin
Min Period        n/a     RAMB36E1/CLKBWRCLK  n/a            2.170         10.000      7.830      RAMB36_X3Y10  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg/CLKBWRCLK
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y52  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[10]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y50  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[2]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y50  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[3]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y51  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[4]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y51  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[5]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y51  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[6]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y51  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[7]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y52  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[8]/C
Min Period        n/a     FDRE/C              n/a            1.000         10.000      9.000      SLICE_X55Y52  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/grdc.rd_data_count_i_reg[9]/C
Low Pulse Width   Slow    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X66Y73  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_SEL_DELAY[4].sel_int_reg[4][1]_srl3/CLK
Low Pulse Width   Fast    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X66Y61  u1/utg_i/v_tc_0/U0/U_TC_TOP/detect_en_d_reg[1]_srl2___U_TC_TOP_detect_en_d_reg_r_0/CLK
Low Pulse Width   Fast    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X66Y61  u1/utg_i/v_tc_0/U0/U_TC_TOP/generate_en_d_reg[1]_srl2___U_TC_TOP_detect_en_d_reg_r_0/CLK
Low Pulse Width   Slow    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X62Y75  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_SEL_DELAY[3].sel_int_reg[3][1]_srl2/CLK
Low Pulse Width   Slow    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X62Y75  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_SEL_DELAY[5].sel_int_reg[5][1]_srl4/CLK
Low Pulse Width   Slow    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X82Y79  u1/utg_i/rst_ps7_0_100M1/U0/EXT_LPF/POR_SRL_I/CLK
Low Pulse Width   Fast    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X82Y79  u1/utg_i/rst_ps7_0_100M1/U0/EXT_LPF/POR_SRL_I/CLK
Low Pulse Width   Slow    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X66Y61  u1/utg_i/v_tc_0/U0/U_TC_TOP/detect_en_d_reg[1]_srl2___U_TC_TOP_detect_en_d_reg_r_0/CLK
Low Pulse Width   Slow    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X66Y61  u1/utg_i/v_tc_0/U0/U_TC_TOP/generate_en_d_reg[1]_srl2___U_TC_TOP_detect_en_d_reg_r_0/CLK
Low Pulse Width   Fast    SRL16E/CLK          n/a            0.854         6.000       5.146      SLICE_X62Y75  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_SEL_DELAY[3].sel_int_reg[3][1]_srl2/CLK
High Pulse Width  Slow    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X66Y61  u1/utg_i/v_tc_0/U0/U_TC_TOP/detect_en_d_reg[1]_srl2___U_TC_TOP_detect_en_d_reg_r_0/CLK
High Pulse Width  Slow    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X66Y61  u1/utg_i/v_tc_0/U0/U_TC_TOP/generate_en_d_reg[1]_srl2___U_TC_TOP_detect_en_d_reg_r_0/CLK
High Pulse Width  Fast    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X62Y75  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_SEL_DELAY[3].sel_int_reg[3][1]_srl2/CLK
High Pulse Width  Fast    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X62Y75  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_SEL_DELAY[5].sel_int_reg[5][1]_srl4/CLK
High Pulse Width  Slow    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X82Y79  u1/utg_i/rst_ps7_0_100M1/U0/EXT_LPF/POR_SRL_I/CLK
High Pulse Width  Fast    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X82Y79  u1/utg_i/rst_ps7_0_100M1/U0/EXT_LPF/POR_SRL_I/CLK
High Pulse Width  Slow    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X62Y75  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.CORE_MUX0/GEN_SEL_DELAY[3].sel_int_reg[3][1]_srl2/CLK
High Pulse Width  Fast    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X66Y73  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_SEL_DELAY[4].sel_int_reg[4][1]_srl3/CLK
High Pulse Width  Slow    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X62Y75  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/GEN_SEL_DELAY[5].sel_int_reg[5][1]_srl4/CLK
High Pulse Width  Fast    SRL16E/CLK          n/a            0.854         4.000       3.146      SLICE_X66Y61  u1/utg_i/v_tc_0/U0/U_TC_TOP/detect_en_d_reg[1]_srl2___U_TC_TOP_detect_en_d_reg_r_0/CLK



---------------------------------------------------------------------------------------------------
From Clock:  mmcm_fbclk_out
  To Clock:  mmcm_fbclk_out

Setup :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
PW    :            0  Failing Endpoints,  Worst Slack        8.751ns,  Total Violation        0.000ns
---------------------------------------------------------------------------------------------------


Pulse Width Checks
--------------------------------------------------------------------------------------
Clock Name:         mmcm_fbclk_out
Waveform(ns):       { 0.000 5.000 }
Period(ns):         10.000
Sources:            { u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKFBOUT }

Check Type  Corner  Lib Pin              Reference Pin  Required(ns)  Actual(ns)  Slack(ns)  Location         Pin
Min Period  n/a     MMCME2_ADV/CLKFBOUT  n/a            1.249         10.000      8.751      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKFBOUT
Min Period  n/a     MMCME2_ADV/CLKFBIN   n/a            1.249         10.000      8.751      MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKFBIN
Max Period  n/a     MMCME2_ADV/CLKFBIN   n/a            100.000       10.000      90.000     MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKFBIN
Max Period  n/a     MMCME2_ADV/CLKFBOUT  n/a            213.360       10.000      203.360    MMCME2_ADV_X1Y1  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKFBOUT



---------------------------------------------------------------------------------------------------
From Clock:  LCD_CLK_OBUF
  To Clock:  clk_fpga_0

Setup :            0  Failing Endpoints,  Worst Slack        8.498ns,  Total Violation        0.000ns
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             8.498ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[21]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][21]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.427ns  (logic 0.433ns (30.341%)  route 0.994ns (69.659%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X82Y72                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[21]/C
    SLICE_X82Y72         FDRE (Prop_fdre_C_Q)         0.433     0.433 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[21]/Q
                         net (fo=1, routed)           0.994     1.427    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[21]
    SLICE_X80Y51         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][21]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X80Y51         FDRE (Setup_fdre_C_D)       -0.075     9.925    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][21]
  -------------------------------------------------------------------
                         required time                          9.925    
                         arrival time                          -1.427    
  -------------------------------------------------------------------
                         slack                                  8.498    

Slack (MET) :             8.588ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[5]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][5]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.337ns  (logic 0.379ns (28.349%)  route 0.958ns (71.651%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X80Y66                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[5]/C
    SLICE_X80Y66         FDRE (Prop_fdre_C_Q)         0.379     0.379 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[5]/Q
                         net (fo=1, routed)           0.958     1.337    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[5]
    SLICE_X78Y49         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][5]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X78Y49         FDRE (Setup_fdre_C_D)       -0.075     9.925    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][5]
  -------------------------------------------------------------------
                         required time                          9.925    
                         arrival time                          -1.337    
  -------------------------------------------------------------------
                         slack                                  8.588    

Slack (MET) :             8.645ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[8]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][8]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.282ns  (logic 0.379ns (29.556%)  route 0.903ns (70.444%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X81Y70                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[8]/C
    SLICE_X81Y70         FDRE (Prop_fdre_C_Q)         0.379     0.379 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[8]/Q
                         net (fo=1, routed)           0.903     1.282    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[8]
    SLICE_X80Y51         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][8]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X80Y51         FDRE (Setup_fdre_C_D)       -0.073     9.927    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][8]
  -------------------------------------------------------------------
                         required time                          9.927    
                         arrival time                          -1.282    
  -------------------------------------------------------------------
                         slack                                  8.645    

Slack (MET) :             8.667ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[29]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][29]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.258ns  (logic 0.379ns (30.127%)  route 0.879ns (69.873%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X64Y80                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[29]/C
    SLICE_X64Y80         FDRE (Prop_fdre_C_Q)         0.379     0.379 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[29]/Q
                         net (fo=1, routed)           0.879     1.258    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[29]
    SLICE_X63Y65         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][29]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X63Y65         FDRE (Setup_fdre_C_D)       -0.075     9.925    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][29]
  -------------------------------------------------------------------
                         required time                          9.925    
                         arrival time                          -1.258    
  -------------------------------------------------------------------
                         slack                                  8.667    

Slack (MET) :             8.697ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[19]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][19]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.230ns  (logic 0.379ns (30.819%)  route 0.851ns (69.181%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X56Y61                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[19]/C
    SLICE_X56Y61         FDRE (Prop_fdre_C_Q)         0.379     0.379 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[19]/Q
                         net (fo=1, routed)           0.851     1.230    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[19]
    SLICE_X56Y49         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][19]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X56Y49         FDRE (Setup_fdre_C_D)       -0.073     9.927    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][19]
  -------------------------------------------------------------------
                         required time                          9.927    
                         arrival time                          -1.230    
  -------------------------------------------------------------------
                         slack                                  8.697    

Slack (MET) :             8.708ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[1]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][1]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.261ns  (logic 0.433ns (34.339%)  route 0.828ns (65.661%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X58Y63                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[1]/C
    SLICE_X58Y63         FDRE (Prop_fdre_C_Q)         0.433     0.433 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[1]/Q
                         net (fo=1, routed)           0.828     1.261    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[1]
    SLICE_X62Y49         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][1]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X62Y49         FDRE (Setup_fdre_C_D)       -0.031     9.969    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][1]
  -------------------------------------------------------------------
                         required time                          9.969    
                         arrival time                          -1.261    
  -------------------------------------------------------------------
                         slack                                  8.708    

Slack (MET) :             8.747ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[11]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][11]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.178ns  (logic 0.379ns (32.166%)  route 0.799ns (67.834%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X81Y70                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[11]/C
    SLICE_X81Y70         FDRE (Prop_fdre_C_Q)         0.379     0.379 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[11]/Q
                         net (fo=1, routed)           0.799     1.178    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[11]
    SLICE_X67Y58         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][11]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X67Y58         FDRE (Setup_fdre_C_D)       -0.075     9.925    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][11]
  -------------------------------------------------------------------
                         required time                          9.925    
                         arrival time                          -1.178    
  -------------------------------------------------------------------
                         slack                                  8.747    

Slack (MET) :             8.768ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[9]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][9]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.159ns  (logic 0.379ns (32.688%)  route 0.780ns (67.312%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X80Y73                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[9]/C
    SLICE_X80Y73         FDRE (Prop_fdre_C_Q)         0.379     0.379 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[9]/Q
                         net (fo=1, routed)           0.780     1.159    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[9]
    SLICE_X63Y65         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][9]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X63Y65         FDRE (Setup_fdre_C_D)       -0.073     9.927    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][9]
  -------------------------------------------------------------------
                         required time                          9.927    
                         arrival time                          -1.159    
  -------------------------------------------------------------------
                         slack                                  8.768    

Slack (MET) :             8.802ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[2]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][2]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.123ns  (logic 0.379ns (33.743%)  route 0.744ns (66.257%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X63Y59                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[2]/C
    SLICE_X63Y59         FDRE (Prop_fdre_C_Q)         0.379     0.379 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[2]/Q
                         net (fo=1, routed)           0.744     1.123    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[2]
    SLICE_X63Y48         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][2]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X63Y48         FDRE (Setup_fdre_C_D)       -0.075     9.925    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][2]
  -------------------------------------------------------------------
                         required time                          9.925    
                         arrival time                          -1.123    
  -------------------------------------------------------------------
                         slack                                  8.802    

Slack (MET) :             8.804ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[24]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][24]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Path Group:             clk_fpga_0
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.121ns  (logic 0.433ns (38.631%)  route 0.688ns (61.369%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X82Y72                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[24]/C
    SLICE_X82Y72         FDRE (Prop_fdre_C_Q)         0.433     0.433 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.ipif_RdData_reg[24]/Q
                         net (fo=1, routed)           0.688     1.121    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/in_data[24]
    SLICE_X80Y60         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][24]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X80Y60         FDRE (Setup_fdre_C_D)       -0.075     9.925    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2PROCCLK_I/data_sync_reg[0][24]
  -------------------------------------------------------------------
                         required time                          9.925    
                         arrival time                          -1.121    
  -------------------------------------------------------------------
                         slack                                  8.804    





---------------------------------------------------------------------------------------------------
From Clock:  clk_fpga_0
  To Clock:  LCD_CLK_OBUF

Setup :            0  Failing Endpoints,  Worst Slack        8.776ns,  Total Violation        0.000ns
Hold  :           NA  Failing Endpoints,  Worst Slack           NA  ,  Total Violation           NA
---------------------------------------------------------------------------------------------------


Max Delay Paths
--------------------------------------------------------------------------------------
Slack (MET) :             8.776ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.022ns  (logic 0.398ns (38.955%)  route 0.624ns (61.045%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X58Y49                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/C
    SLICE_X58Y49         FDRE (Prop_fdre_C_Q)         0.398     0.398 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/Q
                         net (fo=1, routed)           0.624     1.022    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[7]
    SLICE_X60Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X60Y50         FDRE (Setup_fdre_C_D)       -0.202     9.798    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]
  -------------------------------------------------------------------
                         required time                          9.798    
                         arrival time                          -1.022    
  -------------------------------------------------------------------
                         slack                                  8.776    

Slack (MET) :             8.842ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.998ns  (logic 0.398ns (39.863%)  route 0.600ns (60.137%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X54Y49                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]/C
    SLICE_X54Y49         FDRE (Prop_fdre_C_Q)         0.398     0.398 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]/Q
                         net (fo=1, routed)           0.600     0.998    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/async_path[5]
    SLICE_X54Y52         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X54Y52         FDRE (Setup_fdre_C_D)       -0.160     9.840    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]
  -------------------------------------------------------------------
                         required time                          9.840    
                         arrival time                          -0.998    
  -------------------------------------------------------------------
                         slack                                  8.842    

Slack (MET) :             8.944ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.proc_sync1_reg[14]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][14]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.983ns  (logic 0.433ns (44.064%)  route 0.550ns (55.936%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X54Y75                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.proc_sync1_reg[14]/C
    SLICE_X54Y75         FDRE (Prop_fdre_C_Q)         0.433     0.433 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.proc_sync1_reg[14]/Q
                         net (fo=1, routed)           0.550     0.983    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][44]_0[14]
    SLICE_X55Y75         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][14]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X55Y75         FDRE (Setup_fdre_C_D)       -0.073     9.927    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][14]
  -------------------------------------------------------------------
                         required time                          9.927    
                         arrival time                          -0.983    
  -------------------------------------------------------------------
                         slack                                  8.944    

Slack (MET) :             8.946ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        1.021ns  (logic 0.433ns (42.415%)  route 0.588ns (57.585%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X58Y49                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[0]/C
    SLICE_X58Y49         FDRE (Prop_fdre_C_Q)         0.433     0.433 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[0]/Q
                         net (fo=1, routed)           0.588     1.021    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[0]
    SLICE_X58Y54         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X58Y54         FDRE (Setup_fdre_C_D)       -0.033     9.967    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]
  -------------------------------------------------------------------
                         required time                          9.967    
                         arrival time                          -1.021    
  -------------------------------------------------------------------
                         slack                                  8.946    

Slack (MET) :             8.950ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[7]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.880ns  (logic 0.348ns (39.544%)  route 0.532ns (60.456%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X56Y49                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[7]/C
    SLICE_X56Y49         FDRE (Prop_fdre_C_Q)         0.348     0.348 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[7]/Q
                         net (fo=1, routed)           0.532     0.880    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/async_path[7]
    SLICE_X54Y51         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X54Y51         FDRE (Setup_fdre_C_D)       -0.170     9.830    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]
  -------------------------------------------------------------------
                         required time                          9.830    
                         arrival time                          -0.880    
  -------------------------------------------------------------------
                         slack                                  8.950    

Slack (MET) :             8.957ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[1]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.831ns  (logic 0.348ns (41.899%)  route 0.483ns (58.101%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X59Y49                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[1]/C
    SLICE_X59Y49         FDRE (Prop_fdre_C_Q)         0.348     0.348 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[1]/Q
                         net (fo=1, routed)           0.483     0.831    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/async_path[1]
    SLICE_X61Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X61Y50         FDRE (Setup_fdre_C_D)       -0.212     9.788    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]
  -------------------------------------------------------------------
                         required time                          9.788    
                         arrival time                          -0.831    
  -------------------------------------------------------------------
                         slack                                  8.957    

Slack (MET) :             8.973ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[5]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.866ns  (logic 0.398ns (45.953%)  route 0.468ns (54.047%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X58Y49                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[5]/C
    SLICE_X58Y49         FDRE (Prop_fdre_C_Q)         0.398     0.398 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[5]/Q
                         net (fo=1, routed)           0.468     0.866    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[5]
    SLICE_X58Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X58Y53         FDRE (Setup_fdre_C_D)       -0.161     9.839    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]
  -------------------------------------------------------------------
                         required time                          9.839    
                         arrival time                          -0.866    
  -------------------------------------------------------------------
                         slack                                  8.973    

Slack (MET) :             8.990ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[3]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.803ns  (logic 0.348ns (43.338%)  route 0.455ns (56.662%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X57Y49                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[3]/C
    SLICE_X57Y49         FDRE (Prop_fdre_C_Q)         0.348     0.348 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[3]/Q
                         net (fo=1, routed)           0.455     0.803    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[3]
    SLICE_X57Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X57Y53         FDRE (Setup_fdre_C_D)       -0.207     9.793    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]
  -------------------------------------------------------------------
                         required time                          9.793    
                         arrival time                          -0.803    
  -------------------------------------------------------------------
                         slack                                  8.990    

Slack (MET) :             9.008ns  (required time - arrival time)
  Source:                 u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[9]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.917ns  (logic 0.433ns (47.200%)  route 0.484ns (52.800%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X62Y53                                      0.000     0.000 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[9]/C
    SLICE_X62Y53         FDRE (Prop_fdre_C_Q)         0.433     0.433 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[9]/Q
                         net (fo=1, routed)           0.484     0.917    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[9]
    SLICE_X60Y54         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X60Y54         FDRE (Setup_fdre_C_D)       -0.075     9.925    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]
  -------------------------------------------------------------------
                         required time                          9.925    
                         arrival time                          -0.917    
  -------------------------------------------------------------------
                         slack                                  9.008    

Slack (MET) :             9.024ns  (required time - arrival time)
  Source:                 u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.proc_sync1_reg[42]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0  {rise@0.000ns fall@5.000ns period=10.000ns})
  Destination:            u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][42]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF  {rise@0.000ns fall@4.000ns period=10.000ns})
  Path Group:             LCD_CLK_OBUF
  Path Type:              Setup (Max at Slow Process Corner)
  Requirement:            10.000ns  (MaxDelay Path 10.000ns)
  Data Path Delay:        0.774ns  (logic 0.398ns (51.421%)  route 0.376ns (48.579%))
  Logic Levels:           0  
  Timing Exception:       MaxDelay Path 10.000ns -datapath_only

    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
    SLICE_X54Y82                                      0.000     0.000 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.proc_sync1_reg[42]/C
    SLICE_X54Y82         FDRE (Prop_fdre_C_Q)         0.398     0.398 r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.proc_sync1_reg[42]/Q
                         net (fo=1, routed)           0.376     0.774    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][44]_0[42]
    SLICE_X56Y82         FDRE                                         r  u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][42]/D
  -------------------------------------------------------------------    -------------------

                         max delay                   10.000    10.000    
    SLICE_X56Y82         FDRE (Setup_fdre_C_D)       -0.202     9.798    u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[0][42]
  -------------------------------------------------------------------
                         required time                          9.798    
                         arrival time                          -0.774    
  -------------------------------------------------------------------
                         slack                                  9.024    





