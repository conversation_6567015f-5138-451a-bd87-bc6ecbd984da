﻿<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<EDKSYSTEM EDWVERSION="1.2" TIMESTAMP="Fri Aug  1 16:38:37 2025" VIVADOVERSION="2018.3">

  <SYSTEMINFO ARCH="zynq" DEVICE="7z020" NAME="utg" PACKAGE="clg400" SPEEDGRADE="-2"/>

  <EXTERNALPORTS>
    <PORT CLKFREQUENCY="100000000" DIR="O" NAME="lcd_pclk" SIGIS="clk" SIGNAME="axi_dynclk_0_PXL_CLK_O">
      <CONNECTIONS>
        <CONNECTION INSTANCE="axi_dynclk_0" PORT="PXL_CLK_O"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="lcd_de" SIGIS="data" SIGNAME="v_axi4s_vid_out_0_vid_active_video">
      <CONNECTIONS>
        <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vid_active_video"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="lcd_hsync" SIGIS="data" SIGNAME="v_axi4s_vid_out_0_vid_hsync">
      <CONNECTIONS>
        <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vid_hsync"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="0" NAME="lcd_rst" RIGHT="0" SIGIS="rst" SIGNAME="xlconstant_0_dout">
      <CONNECTIONS>
        <CONNECTION INSTANCE="xlconstant_0" PORT="dout"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="0" NAME="lcd_bl" RIGHT="0" SIGIS="data" SIGNAME="xlconstant_0_dout">
      <CONNECTIONS>
        <CONNECTION INSTANCE="xlconstant_0" PORT="dout"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="lcd_vsync" SIGIS="data" SIGNAME="v_axi4s_vid_out_0_vid_vsync">
      <CONNECTIONS>
        <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vid_vsync"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="31" NAME="lcd_data" RIGHT="0" SIGIS="data" SIGNAME="v_axi4s_vid_out_0_vid_data">
      <CONNECTIONS>
        <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vid_data"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" NAME="ADC_OR" SIGIS="data" SIGNAME="External_Ports_ADC_OR">
      <CONNECTIONS>
        <CONNECTION INSTANCE="AXI_ADC_0" PORT="ADC_OR"/>
      </CONNECTIONS>
    </PORT>
    <PORT CLKFREQUENCY="125000000" DIR="I" NAME="ADC_DCLK" SIGIS="clk" SIGNAME="External_Ports_ADC_DCLK">
      <CONNECTIONS>
        <CONNECTION INSTANCE="AXI_ADC_0" PORT="ADC_DCLK"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" LEFT="11" NAME="ADC_DATA" RIGHT="0" SIGIS="data" SIGNAME="External_Ports_ADC_DATA">
      <CONNECTIONS>
        <CONNECTION INSTANCE="AXI_ADC_0" PORT="ADC_DATA"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="ADC_PDWN" SIGIS="data" SIGNAME="AXI_ADC_0_ADC_PDWN">
      <CONNECTIONS>
        <CONNECTION INSTANCE="AXI_ADC_0" PORT="ADC_PDWN"/>
      </CONNECTIONS>
    </PORT>
    <PORT CLKFREQUENCY="100000000" DIR="O" NAME="ADC_SP_CLK" SIGIS="clk" SIGNAME="AXI_ADC_0_ADC_SP_CLK">
      <CONNECTIONS>
        <CONNECTION INSTANCE="AXI_ADC_0" PORT="ADC_SP_CLK"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="ADC_START" SIGIS="undef" SIGNAME="AXI_ADC_0_ADC_START">
      <CONNECTIONS>
        <CONNECTION INSTANCE="AXI_ADC_0" PORT="ADC_START"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="7" NAME="S_AXI_HP0_FIFO_CTRL_0_rcount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_RCOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RCOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="7" NAME="S_AXI_HP0_FIFO_CTRL_0_wcount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_WCOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_WCOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="2" NAME="S_AXI_HP0_FIFO_CTRL_0_racount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_RACOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RACOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="5" NAME="S_AXI_HP0_FIFO_CTRL_0_wacount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_WACOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_WACOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" NAME="S_AXI_HP0_FIFO_CTRL_0_rdissuecapen" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_RDISSUECAP1_EN">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RDISSUECAP1_EN"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" NAME="S_AXI_HP0_FIFO_CTRL_0_wrissuecapen" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_WRISSUECAP1_EN">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_WRISSUECAP1_EN"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" LEFT="2" NAME="gpio0_tri_i" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_GPIO_I">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="GPIO_I"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="2" NAME="gpio0_tri_o" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_GPIO_O">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="GPIO_O"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="2" NAME="gpio0_tri_t" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_GPIO_T">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="GPIO_T"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" LEFT="53" NAME="FIXED_IO_mio" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_MIO">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="MIO"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="FIXED_IO_ddr_vrn" SIGIS="undef" SIGNAME="processing_system7_0_DDR_VRN">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_VRN"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="FIXED_IO_ddr_vrp" SIGIS="undef" SIGNAME="processing_system7_0_DDR_VRP">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_VRP"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="FIXED_IO_ps_srstb" SIGIS="undef" SIGNAME="processing_system7_0_PS_SRSTB">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="PS_SRSTB"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="FIXED_IO_ps_clk" SIGIS="undef" SIGNAME="processing_system7_0_PS_CLK">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="PS_CLK"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="FIXED_IO_ps_porb" SIGIS="undef" SIGNAME="processing_system7_0_PS_PORB">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="PS_PORB"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" NAME="i2c0_sda_i" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SDA_I">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="I2C0_SDA_I"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="i2c0_sda_o" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SDA_O">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="I2C0_SDA_O"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="i2c0_sda_t" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SDA_T">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="I2C0_SDA_T"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" NAME="i2c0_scl_i" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SCL_I">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="I2C0_SCL_I"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="i2c0_scl_o" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SCL_O">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="I2C0_SCL_O"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="i2c0_scl_t" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SCL_T">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="I2C0_SCL_T"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="DDR_cas_n" SIGIS="undef" SIGNAME="processing_system7_0_DDR_CAS_n">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_CAS_n"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="DDR_cke" SIGIS="undef" SIGNAME="processing_system7_0_DDR_CKE">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_CKE"/>
      </CONNECTIONS>
    </PORT>
    <PORT CLKFREQUENCY="100000000" DIR="IO" NAME="DDR_ck_n" SIGIS="clk" SIGNAME="processing_system7_0_DDR_Clk_n">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_Clk_n"/>
      </CONNECTIONS>
    </PORT>
    <PORT CLKFREQUENCY="100000000" DIR="IO" NAME="DDR_ck_p" SIGIS="clk" SIGNAME="processing_system7_0_DDR_Clk">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_Clk"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="DDR_cs_n" SIGIS="undef" SIGNAME="processing_system7_0_DDR_CS_n">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_CS_n"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="DDR_reset_n" SIGIS="rst" SIGNAME="processing_system7_0_DDR_DRSTB">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_DRSTB"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="DDR_odt" SIGIS="undef" SIGNAME="processing_system7_0_DDR_ODT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_ODT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="DDR_ras_n" SIGIS="undef" SIGNAME="processing_system7_0_DDR_RAS_n">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_RAS_n"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" NAME="DDR_we_n" SIGIS="undef" SIGNAME="processing_system7_0_DDR_WEB">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_WEB"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" LEFT="2" NAME="DDR_ba" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_BankAddr">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_BankAddr"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" LEFT="14" NAME="DDR_addr" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_Addr">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_Addr"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" LEFT="3" NAME="DDR_dm" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DM">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_DM"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" LEFT="31" NAME="DDR_dq" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DQ">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_DQ"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" LEFT="3" NAME="DDR_dqs_n" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DQS_n">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_DQS_n"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="IO" LEFT="3" NAME="DDR_dqs_p" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DQS">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="DDR_DQS"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="7" NAME="S_AXI_HP1_FIFO_CTRL_0_rcount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_RCOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RCOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="7" NAME="S_AXI_HP1_FIFO_CTRL_0_wcount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_WCOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WCOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="2" NAME="S_AXI_HP1_FIFO_CTRL_0_racount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_RACOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RACOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" LEFT="5" NAME="S_AXI_HP1_FIFO_CTRL_0_wacount" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_WACOUNT">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WACOUNT"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" NAME="S_AXI_HP1_FIFO_CTRL_0_rdissuecapen" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_RDISSUECAP1_EN">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RDISSUECAP1_EN"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="I" NAME="S_AXI_HP1_FIFO_CTRL_0_wrissuecapen" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_WRISSUECAP1_EN">
      <CONNECTIONS>
        <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WRISSUECAP1_EN"/>
      </CONNECTIONS>
    </PORT>
    <PORT DIR="O" NAME="ULTRASOUND_PLUS" SIGIS="undef" SIGNAME="AXI_ADC_0_ULTRASOUND_PLUS">
      <CONNECTIONS>
        <CONNECTION INSTANCE="AXI_ADC_0" PORT="ULTRASOUND_PLUS"/>
      </CONNECTIONS>
    </PORT>
  </EXTERNALPORTS>

  <EXTERNALINTERFACES>
    <BUSINTERFACE BUSNAME="processing_system7_0_DDR" DATAWIDTH="8" NAME="DDR" TYPE="INITIATOR">
      <PARAMETER NAME="CAN_DEBUG" VALUE="false"/>
      <PARAMETER NAME="TIMEPERIOD_PS" VALUE="1250"/>
      <PARAMETER NAME="MEMORY_TYPE" VALUE="COMPONENTS"/>
      <PARAMETER NAME="MEMORY_PART"/>
      <PARAMETER NAME="DATA_WIDTH" VALUE="8"/>
      <PARAMETER NAME="CS_ENABLED" VALUE="true"/>
      <PARAMETER NAME="DATA_MASK_ENABLED" VALUE="true"/>
      <PARAMETER NAME="SLOT" VALUE="Single"/>
      <PARAMETER NAME="CUSTOM_PARTS"/>
      <PARAMETER NAME="MEM_ADDR_MAP" VALUE="ROW_COLUMN_BANK"/>
      <PARAMETER NAME="BURST_LENGTH" VALUE="8"/>
      <PARAMETER NAME="AXI_ARBITRATION_SCHEME" VALUE="TDM"/>
      <PARAMETER NAME="CAS_LATENCY" VALUE="11"/>
      <PARAMETER NAME="CAS_WRITE_LATENCY" VALUE="11"/>
      <PORTMAPS>
        <PORTMAP LOGICAL="CAS_N" PHYSICAL="DDR_cas_n"/>
        <PORTMAP LOGICAL="CKE" PHYSICAL="DDR_cke"/>
        <PORTMAP LOGICAL="CK_N" PHYSICAL="DDR_ck_n"/>
        <PORTMAP LOGICAL="CK_P" PHYSICAL="DDR_ck_p"/>
        <PORTMAP LOGICAL="CS_N" PHYSICAL="DDR_cs_n"/>
        <PORTMAP LOGICAL="RESET_N" PHYSICAL="DDR_reset_n"/>
        <PORTMAP LOGICAL="ODT" PHYSICAL="DDR_odt"/>
        <PORTMAP LOGICAL="RAS_N" PHYSICAL="DDR_ras_n"/>
        <PORTMAP LOGICAL="WE_N" PHYSICAL="DDR_we_n"/>
        <PORTMAP LOGICAL="BA" PHYSICAL="DDR_ba"/>
        <PORTMAP LOGICAL="ADDR" PHYSICAL="DDR_addr"/>
        <PORTMAP LOGICAL="DM" PHYSICAL="DDR_dm"/>
        <PORTMAP LOGICAL="DQ" PHYSICAL="DDR_dq"/>
        <PORTMAP LOGICAL="DQS_N" PHYSICAL="DDR_dqs_n"/>
        <PORTMAP LOGICAL="DQS_P" PHYSICAL="DDR_dqs_p"/>
      </PORTMAPS>
    </BUSINTERFACE>
    <BUSINTERFACE BUSNAME="processing_system7_0_FIXED_IO" NAME="FIXED_IO" TYPE="INITIATOR">
      <PARAMETER NAME="CAN_DEBUG" VALUE="false"/>
      <PORTMAPS>
        <PORTMAP LOGICAL="MIO" PHYSICAL="FIXED_IO_mio"/>
        <PORTMAP LOGICAL="DDR_VRN" PHYSICAL="FIXED_IO_ddr_vrn"/>
        <PORTMAP LOGICAL="DDR_VRP" PHYSICAL="FIXED_IO_ddr_vrp"/>
        <PORTMAP LOGICAL="PS_SRSTB" PHYSICAL="FIXED_IO_ps_srstb"/>
        <PORTMAP LOGICAL="PS_CLK" PHYSICAL="FIXED_IO_ps_clk"/>
        <PORTMAP LOGICAL="PS_PORB" PHYSICAL="FIXED_IO_ps_porb"/>
      </PORTMAPS>
    </BUSINTERFACE>
    <BUSINTERFACE BUSNAME="processing_system7_0_IIC_0" NAME="i2c0" TYPE="INITIATOR">
      <PORTMAPS>
        <PORTMAP LOGICAL="SDA_I" PHYSICAL="i2c0_sda_i"/>
        <PORTMAP LOGICAL="SDA_O" PHYSICAL="i2c0_sda_o"/>
        <PORTMAP LOGICAL="SDA_T" PHYSICAL="i2c0_sda_t"/>
        <PORTMAP LOGICAL="SCL_I" PHYSICAL="i2c0_scl_i"/>
        <PORTMAP LOGICAL="SCL_O" PHYSICAL="i2c0_scl_o"/>
        <PORTMAP LOGICAL="SCL_T" PHYSICAL="i2c0_scl_t"/>
      </PORTMAPS>
    </BUSINTERFACE>
    <BUSINTERFACE BUSNAME="processing_system7_0_GPIO_0" NAME="gpio0" TYPE="INITIATOR">
      <PORTMAPS>
        <PORTMAP LOGICAL="TRI_I" PHYSICAL="gpio0_tri_i"/>
        <PORTMAP LOGICAL="TRI_O" PHYSICAL="gpio0_tri_o"/>
        <PORTMAP LOGICAL="TRI_T" PHYSICAL="gpio0_tri_t"/>
      </PORTMAPS>
    </BUSINTERFACE>
    <BUSINTERFACE BUSNAME="External_Interface_S_AXI_HP0_FIFO_CTRL_0" NAME="S_AXI_HP0_FIFO_CTRL_0" TYPE="TARGET">
      <PORTMAPS>
        <PORTMAP LOGICAL="RCOUNT" PHYSICAL="S_AXI_HP0_FIFO_CTRL_0_rcount"/>
        <PORTMAP LOGICAL="WCOUNT" PHYSICAL="S_AXI_HP0_FIFO_CTRL_0_wcount"/>
        <PORTMAP LOGICAL="RACOUNT" PHYSICAL="S_AXI_HP0_FIFO_CTRL_0_racount"/>
        <PORTMAP LOGICAL="WACOUNT" PHYSICAL="S_AXI_HP0_FIFO_CTRL_0_wacount"/>
        <PORTMAP LOGICAL="RDISSUECAPEN" PHYSICAL="S_AXI_HP0_FIFO_CTRL_0_rdissuecapen"/>
        <PORTMAP LOGICAL="WRISSUECAPEN" PHYSICAL="S_AXI_HP0_FIFO_CTRL_0_wrissuecapen"/>
      </PORTMAPS>
    </BUSINTERFACE>
    <BUSINTERFACE BUSNAME="External_Interface_S_AXI_HP1_FIFO_CTRL_0" NAME="S_AXI_HP1_FIFO_CTRL_0" TYPE="TARGET">
      <PORTMAPS>
        <PORTMAP LOGICAL="RCOUNT" PHYSICAL="S_AXI_HP1_FIFO_CTRL_0_rcount"/>
        <PORTMAP LOGICAL="WCOUNT" PHYSICAL="S_AXI_HP1_FIFO_CTRL_0_wcount"/>
        <PORTMAP LOGICAL="RACOUNT" PHYSICAL="S_AXI_HP1_FIFO_CTRL_0_racount"/>
        <PORTMAP LOGICAL="WACOUNT" PHYSICAL="S_AXI_HP1_FIFO_CTRL_0_wacount"/>
        <PORTMAP LOGICAL="RDISSUECAPEN" PHYSICAL="S_AXI_HP1_FIFO_CTRL_0_rdissuecapen"/>
        <PORTMAP LOGICAL="WRISSUECAPEN" PHYSICAL="S_AXI_HP1_FIFO_CTRL_0_wrissuecapen"/>
      </PORTMAPS>
    </BUSINTERFACE>
  </EXTERNALINTERFACES>

  <MODULES>
    <MODULE COREREVISION="1" FULLNAME="/AXI_ADC_0" HWVERSION="1.0" INSTANCE="AXI_ADC_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="AXI_ADC" VLNV="lzcx:user:AXI_ADC:1.0">
      <DOCUMENTS/>
      <ADDRESSBLOCKS>
        <ADDRESSBLOCK ACCESS="" INTERFACE="S00_AXI" NAME="S00_AXI_reg" RANGE="4096" USAGE="register"/>
      </ADDRESSBLOCKS>
      <PARAMETERS>
        <PARAMETER NAME="C_M00_AXI_TARGET_SLAVE_BASE_ADDR" VALUE="0x40000000"/>
        <PARAMETER NAME="C_M00_AXI_BURST_LEN" VALUE="16"/>
        <PARAMETER NAME="C_M00_AXI_ID_WIDTH" VALUE="1"/>
        <PARAMETER NAME="C_M00_AXI_ADDR_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_M00_AXI_DATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_M00_AXI_AWUSER_WIDTH" VALUE="0"/>
        <PARAMETER NAME="C_M00_AXI_ARUSER_WIDTH" VALUE="0"/>
        <PARAMETER NAME="C_M00_AXI_WUSER_WIDTH" VALUE="0"/>
        <PARAMETER NAME="C_M00_AXI_RUSER_WIDTH" VALUE="0"/>
        <PARAMETER NAME="C_M00_AXI_BUSER_WIDTH" VALUE="0"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_AXI_ADC_0_4"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
        <PARAMETER NAME="C_S00_AXI_BASEADDR" VALUE="0x43C30000"/>
        <PARAMETER NAME="C_S00_AXI_HIGHADDR" VALUE="0x43C3FFFF"/>
      </PARAMETERS>
      <PORTS>
        <PORT DIR="I" NAME="ADC_OR" SIGIS="undef" SIGNAME="External_Ports_ADC_OR">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="ADC_OR"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="ADC_DCLK" SIGIS="undef" SIGNAME="External_Ports_ADC_DCLK">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="ADC_DCLK"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="11" NAME="ADC_DATA" RIGHT="0" SIGIS="undef" SIGNAME="External_Ports_ADC_DATA">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="ADC_DATA"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="ADC_PDWN" SIGIS="undef" SIGNAME="AXI_ADC_0_ADC_PDWN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="ADC_PDWN"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="ADC_SP_CLK" SIGIS="undef" SIGNAME="AXI_ADC_0_ADC_SP_CLK">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="ADC_SP_CLK"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="ADC_START" SIGIS="undef" SIGNAME="AXI_ADC_0_ADC_START">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="ADC_START"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="ULTRASOUND_PLUS" SIGIS="undef" SIGNAME="AXI_ADC_0_ULTRASOUND_PLUS">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="ULTRASOUND_PLUS"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="5" NAME="s00_axi_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="s00_axi_awprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_awvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_awready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="s00_axi_wdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="s00_axi_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_wvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_wready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s00_axi_bresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_bvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_bready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="5" NAME="s00_axi_araddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="s00_axi_arprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_arvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_arready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="s00_axi_rdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s00_axi_rresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_rvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_rready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="s00_axi_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_aresetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="m00_axi_awid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="m00_axi_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="m00_axi_awlen" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="m00_axi_awsize" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="m00_axi_awburst" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_awlock" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="m00_axi_awcache" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="m00_axi_awprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="m00_axi_awqos" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_awvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m00_axi_awready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="m00_axi_wdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="m00_axi_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_wlast" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_wlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_wvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m00_axi_wready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="m00_axi_bid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_bid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="m00_axi_bresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m00_axi_bvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_bready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="m00_axi_arid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="m00_axi_araddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="m00_axi_arlen" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="m00_axi_arsize" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="m00_axi_arburst" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_arlock" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="m00_axi_arcache" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="m00_axi_arprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="m00_axi_arqos" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_arvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m00_axi_arready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="m00_axi_rid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_rid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="m00_axi_rdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="m00_axi_rresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m00_axi_rlast" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_rlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m00_axi_rvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m00_axi_rready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="S00_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="m00_axi_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m00_axi_aresetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M04_AXI" DATAWIDTH="32" NAME="S00_AXI" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="WIZ_DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="WIZ_NUM_REG" VALUE="8"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4LITE"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="6"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="1"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="1"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="s00_axi_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="s00_axi_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="s00_axi_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="s00_axi_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="s00_axi_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="s00_axi_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="s00_axi_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="s00_axi_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="s00_axi_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="s00_axi_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="s00_axi_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="s00_axi_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="s00_axi_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="s00_axi_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="s00_axi_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="s00_axi_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="s00_axi_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="s00_axi_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="s00_axi_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="AXI_ADC_0_M00_AXI" DATAWIDTH="32" NAME="M00_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="WIZ_DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="1"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="1"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="1"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="1"/>
          <PARAMETER NAME="HAS_QOS" VALUE="1"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="16"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="256"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="AWID" PHYSICAL="m00_axi_awid"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="m00_axi_awaddr"/>
            <PORTMAP LOGICAL="AWLEN" PHYSICAL="m00_axi_awlen"/>
            <PORTMAP LOGICAL="AWSIZE" PHYSICAL="m00_axi_awsize"/>
            <PORTMAP LOGICAL="AWBURST" PHYSICAL="m00_axi_awburst"/>
            <PORTMAP LOGICAL="AWLOCK" PHYSICAL="m00_axi_awlock"/>
            <PORTMAP LOGICAL="AWCACHE" PHYSICAL="m00_axi_awcache"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="m00_axi_awprot"/>
            <PORTMAP LOGICAL="AWQOS" PHYSICAL="m00_axi_awqos"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="m00_axi_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="m00_axi_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="m00_axi_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="m00_axi_wstrb"/>
            <PORTMAP LOGICAL="WLAST" PHYSICAL="m00_axi_wlast"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="m00_axi_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="m00_axi_wready"/>
            <PORTMAP LOGICAL="BID" PHYSICAL="m00_axi_bid"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="m00_axi_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="m00_axi_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="m00_axi_bready"/>
            <PORTMAP LOGICAL="ARID" PHYSICAL="m00_axi_arid"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="m00_axi_araddr"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="m00_axi_arlen"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="m00_axi_arsize"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="m00_axi_arburst"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="m00_axi_arlock"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="m00_axi_arcache"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="m00_axi_arprot"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="m00_axi_arqos"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="m00_axi_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="m00_axi_arready"/>
            <PORTMAP LOGICAL="RID" PHYSICAL="m00_axi_rid"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="m00_axi_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="m00_axi_rresp"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="m00_axi_rlast"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="m00_axi_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="m00_axi_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
      <MEMORYMAP>
        <MEMRANGE ADDRESSBLOCK="HP1_DDR_LOWOCM" BASENAME="C_BASEADDR" BASEVALUE="0x10000000" HIGHNAME="C_HIGHADDR" HIGHVALUE="0x1FFFFFFF" INSTANCE="processing_system7_0" IS_DATA="TRUE" IS_INSTRUCTION="TRUE" MASTERBUSINTERFACE="M00_AXI" MEMTYPE="MEMORY" SLAVEBUSINTERFACE="S_AXI_HP1"/>
      </MEMORYMAP>
      <PERIPHERALS>
        <PERIPHERAL INSTANCE="processing_system7_0"/>
      </PERIPHERALS>
    </MODULE>
    <MODULE COREREVISION="3" FULLNAME="/axi_dynclk_0" HWVERSION="1.0" INSTANCE="axi_dynclk_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="axi_dynclk" VLNV="digilentinc.com:ip:axi_dynclk:1.0">
      <DOCUMENTS/>
      <ADDRESSBLOCKS>
        <ADDRESSBLOCK ACCESS="" INTERFACE="s00_axi" NAME="reg0" RANGE="32" USAGE="register"/>
      </ADDRESSBLOCKS>
      <PARAMETERS>
        <PARAMETER NAME="C_S00_AXI_DATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_S00_AXI_ADDR_WIDTH" VALUE="5"/>
        <PARAMETER NAME="ADD_BUFMR" VALUE="false"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_axi_dynclk_0_1"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
        <PARAMETER NAME="C_BASEADDR" VALUE="0x43C10000"/>
        <PARAMETER NAME="C_HIGHADDR" VALUE="0x43C1FFFF"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="REF_CLK_I" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="O" NAME="PXL_CLK_O" SIGIS="clk" SIGNAME="axi_dynclk_0_PXL_CLK_O">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vid_io_out_clk"/>
            <CONNECTION INSTANCE="v_tc_0" PORT="clk"/>
            <CONNECTION INSTANCE="External_Ports" PORT="lcd_pclk"/>
            <CONNECTION INSTANCE="rst_ps7_0_100M1" PORT="slowest_sync_clk"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="O" NAME="PXL_CLK_5X_O" SIGIS="clk"/>
        <PORT DIR="O" NAME="LOCKED_O" SIGIS="undef"/>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="s00_axi_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_aresetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="4" NAME="s00_axi_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="s00_axi_awprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_awvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_awready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="s00_axi_wdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="s00_axi_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_wvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_wready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s00_axi_bresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_bvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_bready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="4" NAME="s00_axi_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="s00_axi_arprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_arvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_arready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="s00_axi_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s00_axi_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_rvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_rready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M02_AXI" DATAWIDTH="32" NAME="s00_axi" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4LITE"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="5"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="1"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="1"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="s00_axi_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="s00_axi_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="s00_axi_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="s00_axi_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="s00_axi_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="s00_axi_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="s00_axi_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="s00_axi_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="s00_axi_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="s00_axi_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="s00_axi_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="s00_axi_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="s00_axi_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="s00_axi_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="s00_axi_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="s00_axi_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="s00_axi_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="s00_axi_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="s00_axi_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
    </MODULE>
    <MODULE BD="utg_axi_smc_2" BDTYPE="SBD" COREREVISION="10" DRIVERMODE="CORE" FULLNAME="/axi_smc" HWVERSION="1.0" INSTANCE="axi_smc" IPTYPE="BUS" IS_ENABLE="1" MODCLASS="BUS" MODTYPE="smartconnect" SIM_BD="utg_axi_smc_2" VLNV="xilinx.com:ip:smartconnect:1.0">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=smartconnect;v=v1_0;d=pg247-smartconnect.pdf"/>
      </DOCUMENTS>
      <PARAMETERS>
        <PARAMETER NAME="NUM_MI" VALUE="1"/>
        <PARAMETER NAME="NUM_SI" VALUE="1"/>
        <PARAMETER NAME="NUM_CLKS" VALUE="1"/>
        <PARAMETER NAME="HAS_ARESETN" VALUE="1"/>
        <PARAMETER NAME="ADVANCED_PROPERTIES" VALUE="0"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_axi_smc_2"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="BUS"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="aresetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S00_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="7" NAME="S00_AXI_arlen" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_arlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_arsize" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_arsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S00_AXI_arburst" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_arburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="S00_AXI_arlock" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_arcache" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_arcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_arqos" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" NAME="S00_AXI_arvalid" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_arready" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="63" NAME="S00_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="S00_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_rlast" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_rlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_rvalid" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_rready" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M00_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARADDR"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_arlen" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARLEN"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M00_AXI_arsize" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARSIZE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M00_AXI_arburst" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARBURST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M00_AXI_arlock" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARLOCK"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_arcache" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARCACHE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M00_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARPROT"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_arqos" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARQOS"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_arvalid" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_arready" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ARREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="63" NAME="M00_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RDATA"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M00_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RRESP"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_rlast" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RLAST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_rvalid" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_rready" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_RREADY"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="axi_vdma_0_M_AXI_MM2S" DATAWIDTH="64" NAME="S00_AXI" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="DATA_WIDTH" VALUE="64"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_ONLY"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="0"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="0"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="0"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="64"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="S00_AXI_araddr"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="S00_AXI_arlen"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="S00_AXI_arsize"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="S00_AXI_arburst"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="S00_AXI_arlock"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="S00_AXI_arcache"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="S00_AXI_arprot"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="S00_AXI_arqos"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="S00_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="S00_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="S00_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="S00_AXI_rresp"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="S00_AXI_rlast"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="S00_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="S00_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="axi_smc_M00_AXI" DATAWIDTH="64" NAME="M00_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="DATA_WIDTH" VALUE="64"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI3"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_ONLY"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="0"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="0"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="0"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="16"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M00_AXI_araddr"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="M00_AXI_arlen"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="M00_AXI_arsize"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="M00_AXI_arburst"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="M00_AXI_arlock"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="M00_AXI_arcache"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M00_AXI_arprot"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="M00_AXI_arqos"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M00_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M00_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M00_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M00_AXI_rresp"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="M00_AXI_rlast"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M00_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M00_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
    </MODULE>
    <MODULE BD="utg_axi_smc_0" BDTYPE="SBD" COREREVISION="10" DRIVERMODE="CORE" FULLNAME="/axi_smc1" HWVERSION="1.0" INSTANCE="axi_smc1" IPTYPE="BUS" IS_ENABLE="1" MODCLASS="BUS" MODTYPE="smartconnect" SIM_BD="utg_axi_smc_0" VLNV="xilinx.com:ip:smartconnect:1.0">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=smartconnect;v=v1_0;d=pg247-smartconnect.pdf"/>
      </DOCUMENTS>
      <PARAMETERS>
        <PARAMETER NAME="NUM_MI" VALUE="1"/>
        <PARAMETER NAME="NUM_SI" VALUE="1"/>
        <PARAMETER NAME="NUM_CLKS" VALUE="1"/>
        <PARAMETER NAME="HAS_ARESETN" VALUE="1"/>
        <PARAMETER NAME="ADVANCED_PROPERTIES" VALUE="0"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_axi_smc_0"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="BUS"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="aresetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="S00_AXI_awid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S00_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="7" NAME="S00_AXI_awlen" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_awsize" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S00_AXI_awburst" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="S00_AXI_awlock" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_awcache" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_awprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_awqos" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_awvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_awready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S00_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_wlast" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_wlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_wvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_wready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="S00_AXI_bid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_bid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="S00_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_bvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_bready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="S00_AXI_arid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S00_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="7" NAME="S00_AXI_arlen" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_arsize" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S00_AXI_arburst" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="S00_AXI_arlock" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_arcache" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_arqos" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_arvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_arready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="S00_AXI_rid" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_rid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="S00_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="S00_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_rlast" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_rlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_rvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_rready" SIGIS="undef" SIGNAME="AXI_ADC_0_m00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M00_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWADDR"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_awlen" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWLEN"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M00_AXI_awsize" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWSIZE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M00_AXI_awburst" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWBURST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M00_AXI_awlock" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWLOCK"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_awcache" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWCACHE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M00_AXI_awprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWPROT"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_awqos" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWQOS"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_awvalid" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_awready" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_AWREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="63" NAME="M00_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WDATA"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="M00_AXI_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WSTRB"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_wlast" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WLAST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_wvalid" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_wready" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_WREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M00_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_BRESP"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_bvalid" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_BVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_bready" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_BREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M00_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARADDR"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_arlen" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARLEN"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M00_AXI_arsize" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARSIZE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M00_AXI_arburst" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARBURST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M00_AXI_arlock" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARLOCK"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_arcache" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARCACHE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M00_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARPROT"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M00_AXI_arqos" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARQOS"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_arvalid" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_arready" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ARREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="63" NAME="M00_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RDATA"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M00_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RRESP"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_rlast" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RLAST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_AXI_rvalid" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_rready" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_RREADY"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="AXI_ADC_0_M00_AXI" DATAWIDTH="32" NAME="S00_AXI" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="1"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="1"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="1"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="1"/>
          <PARAMETER NAME="HAS_QOS" VALUE="1"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="16"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="256"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="AWID" PHYSICAL="S00_AXI_awid"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="S00_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWLEN" PHYSICAL="S00_AXI_awlen"/>
            <PORTMAP LOGICAL="AWSIZE" PHYSICAL="S00_AXI_awsize"/>
            <PORTMAP LOGICAL="AWBURST" PHYSICAL="S00_AXI_awburst"/>
            <PORTMAP LOGICAL="AWLOCK" PHYSICAL="S00_AXI_awlock"/>
            <PORTMAP LOGICAL="AWCACHE" PHYSICAL="S00_AXI_awcache"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="S00_AXI_awprot"/>
            <PORTMAP LOGICAL="AWQOS" PHYSICAL="S00_AXI_awqos"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="S00_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="S00_AXI_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="S00_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="S00_AXI_wstrb"/>
            <PORTMAP LOGICAL="WLAST" PHYSICAL="S00_AXI_wlast"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="S00_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="S00_AXI_wready"/>
            <PORTMAP LOGICAL="BID" PHYSICAL="S00_AXI_bid"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="S00_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="S00_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="S00_AXI_bready"/>
            <PORTMAP LOGICAL="ARID" PHYSICAL="S00_AXI_arid"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="S00_AXI_araddr"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="S00_AXI_arlen"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="S00_AXI_arsize"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="S00_AXI_arburst"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="S00_AXI_arlock"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="S00_AXI_arcache"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="S00_AXI_arprot"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="S00_AXI_arqos"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="S00_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="S00_AXI_arready"/>
            <PORTMAP LOGICAL="RID" PHYSICAL="S00_AXI_rid"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="S00_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="S00_AXI_rresp"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="S00_AXI_rlast"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="S00_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="S00_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="axi_smc1_M00_AXI" DATAWIDTH="64" NAME="M00_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="DATA_WIDTH" VALUE="64"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI3"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="1"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="1"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="1"/>
          <PARAMETER NAME="HAS_QOS" VALUE="1"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="16"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="16"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="M00_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWLEN" PHYSICAL="M00_AXI_awlen"/>
            <PORTMAP LOGICAL="AWSIZE" PHYSICAL="M00_AXI_awsize"/>
            <PORTMAP LOGICAL="AWBURST" PHYSICAL="M00_AXI_awburst"/>
            <PORTMAP LOGICAL="AWLOCK" PHYSICAL="M00_AXI_awlock"/>
            <PORTMAP LOGICAL="AWCACHE" PHYSICAL="M00_AXI_awcache"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="M00_AXI_awprot"/>
            <PORTMAP LOGICAL="AWQOS" PHYSICAL="M00_AXI_awqos"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="M00_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="M00_AXI_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="M00_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="M00_AXI_wstrb"/>
            <PORTMAP LOGICAL="WLAST" PHYSICAL="M00_AXI_wlast"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="M00_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="M00_AXI_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="M00_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="M00_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="M00_AXI_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M00_AXI_araddr"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="M00_AXI_arlen"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="M00_AXI_arsize"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="M00_AXI_arburst"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="M00_AXI_arlock"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="M00_AXI_arcache"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M00_AXI_arprot"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="M00_AXI_arqos"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M00_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M00_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M00_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M00_AXI_rresp"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="M00_AXI_rlast"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M00_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M00_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
    </MODULE>
    <MODULE COREREVISION="6" FULLNAME="/axi_vdma_0" HWVERSION="6.3" INSTANCE="axi_vdma_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="axi_vdma" VLNV="xilinx.com:ip:axi_vdma:6.3">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=axi_vdma;v=v6_3;d=pg020_axi_vdma.pdf"/>
      </DOCUMENTS>
      <ADDRESSBLOCKS>
        <ADDRESSBLOCK ACCESS="read-write" INTERFACE="S_AXI_LITE" NAME="Reg" RANGE="256" USAGE="register">
          <REGISTERS>
            <REGISTER NAME="MM2S_VDMACR">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S VDMA Control Register"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x00"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x10042"/>
              <FIELDS>
                <FIELD NAME="RS">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Run / Stop controls the running and stopping of the VDMA channel. For any VDMA operations to commence, the AXI VDMA engine must be running (VDMACR.RS=1).   0 - Stop. VDMA stops when current (if any) VDMA operations are complete. The halted bit in the VDMA Status Register asserts to 1 when the VDMA engine is halted. This bit gets cleared by the AXI VDMA hardware when an AXI4 Slave response error occurs. The CPU can also choose to clear this bit to stop VDMA operations.   1 - Run. Start VDMA operations. The halted bit in the VDMA Status Register deasserts to 0 when the VDMA engine begins operations. Note: On Run/Stop clear, in-progress stream transfers might terminate early.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Circular_Park">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates frame buffer Circular mode or frame buffer Park mode.&#xA;  0 - Park Mode. Engine will park on frame buffer referenced by PARK_PTR_REG.RdFrmPntrRef.&#xA;  1 - Circular Mode. Engine continuously circles through frame buffers.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="1"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="1"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Reset">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Soft reset for AXI VDMA MM2S channel. Setting this bit to a 1 causes the AXI VDMA MM2S channel to be reset. Reset is accomplished gracefully. Pending commands/transfers are flushed or completed. AXI4-Stream reset output is asserted. Setting VDMACR.Reset = 1 only resets the MM2S channel. After completion of a soft reset all MM2S registers and bits are in the default state. This bit will be zero at the end of the reset cycle.   0 - Normal operation   1 - Reset in progress.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="2"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="2"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="GenlockEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Enables Genlock or Dynamic Genlock Synchronization.   0 - Genlock or Dynamic Genlock Synchronization disabled. Genlock input is ignored by MM2S.   1 - Genlock or Dynamic Genlock Synchronization enabled. MM2S synchronized to Genlock frame input. Note: This value is valid only when the channel is configured as Genlock Slave or Dynamic Genlock Master or Dynamic Genlock Slave. If configured for Genlock Master mode, this bit is reserved and always reads as zero.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="3"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="3"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="FrameCntEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Configures the MM2S channel to allow only a IRQFrameCount number of transfers to occur. After IRQFrameCount frames have been transferred, the MM2S channel halts, DMACR.RS bit is cleared to 0, and DMASR.Halted asserts to 1 when the channel has completely halted.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="GenlockSrc">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Selects internal or external genlock bus. This bit is set by default when both channels are enabled and are configured as a valid Genlock pair.   0 - External Genlock   1 - Internal Genlock&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="RdPntrNum">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the master in control when MM2S channel is configured for Genlock slave/Dynamic Genlock Master/Dynamic Genlock Slave or reserved otherwise.   0000b - Controlling entity is Entity 1   0001b - Controller entity is Entity 2   0010b - Controller entity is Entity 3   and so on.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="4"/>
                </FIELD>
                <FIELD NAME="FrmCnt_IrqEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Frame Count Complete Interrupt Enable. When set to 1, allows DMASR.FrmCnt_Irq to generate an interrupt out when IRQFrameCount value reaches zero.   0 - Frame Count Interrupt disabled   1 - Frame Count Interrupt enabled&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="DlyCnt_IrqEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Delay Count Interrupt Enable. When set to 1, allows DMASR.DlyCnt_Irq to generate an interrupt out.   0 - Delay Count Interrupt disabled   1 - Delay Count Interrupt enabled.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Err_IrqEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Error Interrupt Enable. When set to 1, allows VDMASR.Err_Irq to generate an interrupt out.   0 - Error Interrupt disabled   1 - Error Interrupt enabled.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Repeat_En">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Enables repeat or advance frame when AXI VDMA encounters a frame error. This is applicable when AXI VDMA is configured in Genlock Master or Dynamic Genlock Master.   0 - Advance to next frame on frame errors   1 - Repeat previous frame on frame errors&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="15"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="15"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="IRQFrameCount">
                  <PROPERTY NAME="DESCRIPTION" VALUE="This value is used for setting the interrupt threshold. When a frame transfer starts, an internal counter counts down from the Interrupt Frame Count setting.&#xA;When the count reaches zero, an interrupt out is generated by the MM2S channel. When a value different than the current IRQFrameCount is written to this field, the internal frame counter is reset to the new value.&#xA;The minimum setting for the count is 0x01. A write of 0x00 to this register sets the count to 0x01.&#xA;When DMACR.FrameCntEn = 1, this value determines the number of frame buffers to process.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
                <FIELD NAME="IRQDelayCount">
                  <PROPERTY NAME="DESCRIPTION" VALUE="This value is used for setting the interrupt delay count value. The delay count interrupt is a mechanism for causing the MM2S channel to generate an interrupt after the delay period has expired. The timer begins counting either upon receipt of frame sync (external fsync mode) or completion of vsize lines (free run mode). It resets with a subsequent start-of-packet ( m_axis_mm2s_tvalid ) assertion. When a value different than the current IRQDelayCount is written to this field, the internal delay counter is reset to the new value.&#xA;Setting this value to zero disables the delay counter interrupt.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_VDMASR">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S VDMA Status Register"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x04"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x10001"/>
              <FIELDS>
                <FIELD NAME="Halted">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Channel Halted.&#xA;Indicates the run/stop state of the VDMA channel.&#xA;  0 - VDMA channel running&#xA;  1 - VDMA channel halted. This bit gets set when VDMACR.RS = 0. There can be a lag of time between when VDMACR.RS = 0 and when VDMASR.Halted = 1.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="VDMAIntErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Internal Error.   0 - No VDMA Internal Errors.   1 - VDMA Internal Error detected. This error occurs during one of the following conditions.   (a) HSIZE or VSIZE register were written zeros or   (b) Internal error received from helper core axi_datamover or   (c) Transferred frame size is lesser than programmed vsize (SOFEarlyErr). In case (a) and/or (b) channel stops (that is, the VDMACR.RS bit is set to 0 and remains cleared).&#xA;To restart the channel, soft or hard reset is required.&#xA;In case (c), channel does not stop or halt.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="VDMASlvErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Slave Error.   0 - No VDMA Slave Errors.   1 - VDMA Slave Error detected. VDMA Engine halts. This error occurs if the slave read from the Memory Map interface issues a Slave Error.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="5"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="5"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="VDMADecErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Decode Error. This error occurs if the address request is to an invalid address.   0 = No VDMA Decode Errors.   1 = VDMA Decode Error detected. VDMA channel halts.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="6"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="6"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="SOFEarlyErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Start of Frame Early Error   0 - No start-of-frame Error   1 - Start of Frame Early Error detected This error occurs if mm2s_fsync is received before the completion of the frame on the streaming interface.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="FrmCnt_Irq">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Frame Count Interrupt.   0 - No Frame Count Interrupt.   1 - Frame Count Interrupt detected. If enabled (DMACR.FrmCnt_IrqEn = 1) and if the interrupt threshold has been met, an interrupt out is generated.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="DlyCnt_Irq">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Delay.   0 - No Delay Interrupt.   1 - Delay Interrupt detected. If enabled (DMACR.DlyCnt_IrqEn = 1), an interrupt out is generated when the delay count reaches its programmed value.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Err_Irq">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Error.   0 - No error Interrupt.   1 - Error interrupt detected. If enabled (VDMACR.Err_IrqEn = 1), an interrupt out is generated when an error is detected.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="IRQFrameCntSts">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt Frame Count Status. Indicates current interrupt frame count value.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
                <FIELD NAME="IRQDelayCntSts">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt Delay Count Status. Indicates current interrupt delay time value.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_REG_INDEX">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Register Index"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x14"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="MM2S_Reg_Index">
                  <PROPERTY NAME="DESCRIPTION" VALUE="When Frame Buffers is greater than 16&#xA;  0 - Any write or read access between 0x5C to 0x98 accesses the Start Address 1 to 16.&#xA;  1 - Any write or read access between 0x5C to 0x98 accesses the Start Address 17 to 32.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="PARK_PTR_REG">
              <PROPERTY NAME="DESCRIPTION" VALUE="Park Pointer Register"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x28"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="RdFrmPtrRef">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Read Frame Pointer Reference. When Parked (MM2S_VDMACR.Circular_Park = 0) the MM2S channel parks on the buffer referenced by this frame number.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="5"/>
                </FIELD>
                <FIELD NAME="WrFrmPtrRef">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Write Frame Pointer Reference. When Parked (S2MM_VDMACR.Circular_Park = 0) the S2MM channel parks on the buffer referenced by this frame number.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="5"/>
                </FIELD>
                <FIELD NAME="RdFrmStore">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Read Frame Store number. Indicates the frame number being operated on by the MM2S channel. During VDMA operations this value continually updates as each frame is processed. During error conditions, the value is updated with the frame number being operated on when the error occurred. It will again start tracking the current frame number when all errors are cleared. &#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="5"/>
                </FIELD>
                <FIELD NAME="WrFrmStore">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Write Frame Store number. Indicates current frame number being operated on by the S2MM channel. During VDMA operations this value continually updates as each frame is processed. During error conditions, the value is updated with the frame number being operated on when the error occurred. It will again start tracking the current frame number when all errors are cleared.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="5"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="VDMA_VERSION">
              <PROPERTY NAME="DESCRIPTION" VALUE="AXI VDMA Version Register"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x2C"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x62000000"/>
              <FIELDS>
                <FIELD NAME="Xilinx_Internal">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Reserved for Internal Use Only. Integer value from 0 to 9,999.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="16"/>
                </FIELD>
                <FIELD NAME="Minor_Version">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Two separate 4-bit hexadecimal values. 00 = 00h, 01 = 01h, and so on.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="20"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="20"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
                <FIELD NAME="Major_Version">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Single 4-bit hexadecimal value. v1 = 1h, v2=2h, v3=3h, and so on.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="28"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="28"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="4"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_VDMACR">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM VDMA Control Register"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x30"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x10042"/>
              <FIELDS>
                <FIELD NAME="RS">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Run / Stop controls the running and stopping of the VDMA channel. For any VDMA operations to commence, the AXI VDMA engine must be running (VDMACR.RS=1).   0 - Stop. VDMA stops when current (if any) VDMA operations are complete. The halted bit in the VDMA Status Register asserts to 1 when the VDMA engine is halted. This bit gets cleared by the AXI VDMA hardware when an AXI4 Slave response error occurs. The CPU can also choose to clear this bit to stop VDMA operations.   1 - Run. Start VDMA operations. The halted bit in the VDMA Status Register deasserts to 0 when the VDMA engine begins operations. Note: On Run/Stop clear, in-progress stream transfers might terminate early.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Circular_Park">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates frame buffer Circular mode or frame buffer Park mode.&#xA;  0 - Park Mode. Engine will park on frame buffer referenced by PARK_PTR_REG.RdFrmPntrRef.&#xA;  1 - Circular Mode. Engine continuously circles through frame buffers.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="1"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="1"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Reset">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Soft reset for AXI VDMA S2MM channel. Setting this bit to a 1 causes the AXI VDMA S2MM channel to be reset. Reset is accomplished gracefully. Pending commands/transfers are flushed or completed. AXI4-Stream reset output is asserted. Setting VDMACR.Reset = 1 only resets the S2MM channel. After completion of a soft reset all S2MM registers and bits are in the default state. This bit will be zero at the end of the reset cycle.   0 - Normal operation   1 - Reset in progress.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="2"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="2"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="GenlockEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Enables Genlock or Dynamic Genlock Synchronization.   0 - Genlock or Dynamic Genlock Synchronization disabled. Genlock input is ignored by S2MM.   1 - Genlock or Dynamic Genlock Synchronization enabled. S2MM synchronized to Genlock frame input. Note: This value is valid only when the channel is configured as Genlock Slave or Dynamic Genlock Master or Dynamic Genlock Slave. If configured for Genlock Master mode, this bit is reserved and always reads as zero.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="3"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="3"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="FrameCntEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Configures the S2MM channel to allow only a IRQFrameCount number of transfers to occur. After IRQFrameCount frames have been transferred, the S2MM channel halts, DMACR.RS bit is cleared to 0, and DMASR.Halted asserts to 1 when the channel has completely halted.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="GenlockSrc">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Selects internal or external genlock bus. This bit is set by default when both channels are enabled and are configured as a valid Genlock pair.   0 - External Genlock   1 - Internal Genlock&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="RdPntrNum">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the master in control when S2MM channel is configured for Genlock slave/Dynamic Genlock Master/Dynamic Genlock Slave or reserved otherwise.   0000b - Controlling entity is Entity 1   0001b - Controller entity is Entity 2   0010b - Controller entity is Entity 3   and so on.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="4"/>
                </FIELD>
                <FIELD NAME="FrmCnt_IrqEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Frame Count Complete Interrupt Enable. When set to 1, allows DMASR.FrmCnt_Irq to generate an interrupt out when IRQFrameCount value reaches zero.   0 - Frame Count Interrupt disabled   1 - Frame Count Interrupt enabled&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="DlyCnt_IrqEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Delay Count Interrupt Enable. When set to 1, allows DMASR.DlyCnt_Irq to generate an interrupt out.   0 - Delay Count Interrupt disabled   1 - Delay Count Interrupt enabled.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Err_IrqEn">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Error Interrupt Enable. When set to 1, allows VDMASR.Err_Irq to generate an interrupt out.   0 - Error Interrupt disabled   1 - Error Interrupt enabled.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Repeat_En">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Enables repeat or advance frame when AXI VDMA encounters a frame error. This is applicable when AXI VDMA is configured in Genlock Master or Dynamic Genlock Master.   0 - Advance to next frame on frame errors   1 - Repeat previous frame on frame errors&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="15"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="15"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="IRQFrameCount">
                  <PROPERTY NAME="DESCRIPTION" VALUE="This value is used for setting the interrupt threshold. When a frame transfer starts, an internal counter counts down from the Interrupt Frame Count setting.&#xA;When the count reaches zero, an interrupt out is generated by the S2MM channel. When a value different than the current IRQFrameCount is written to this field, the internal frame counter is reset to the new value.&#xA;The minimum setting for the count is 0x01. A write of 0x00 to this register sets the count to 0x01.&#xA;When DMACR.FrameCntEn = 1, this value determines the number of frame buffers to process.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
                <FIELD NAME="IRQDelayCount">
                  <PROPERTY NAME="DESCRIPTION" VALUE="This value is used for setting the interrupt delay count value. The delay count interrupt is a mechanism for causing the S2MM channel to generate an interrupt after the delay period has expired. The timer begins counting either upon receipt of frame sync (external fsync mode) or completion of vsize lines (free run mode). It resets with a subsequent start-of-packet ( s_axis_s2mm_tvalid ) assertion. When a value different than the current IRQDelayCount is written to this field, the internal delay counter is reset to the new value.&#xA;Setting this value to zero disables the delay counter interrupt.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_VDMASR">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM VDMA Status Register"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x34"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x10001"/>
              <FIELDS>
                <FIELD NAME="Halted">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Channel Halted.&#xA;Indicates the run/stop state of the VDMA channel.&#xA;  0 - VDMA channel running&#xA;  1 - VDMA channel halted. This bit gets set when VDMACR.RS = 0. There can be a lag of time between when VDMACR.RS = 0 and when VDMASR.Halted = 1.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="VDMAIntErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Internal Error.   0 - No VDMA Internal Errors.   1 - VDMA Internal Error detected. This error occurs during one of the following conditions.   (a) HSIZE or VSIZE register were written zeros or   (b) Internal error received from helper core axi_datamover or   (c) Transferred frame size is lesser than programmed vsize (SOFEarlyErr). In case (a) and/or (b) channel stops (that is, the VDMACR.RS bit is set to 0 and remains cleared).&#xA;To restart the channel, soft or hard reset is required.&#xA;In case (c), channel does not stop or halt.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="4"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="VDMASlvErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Slave Error.   0 - No VDMA Slave Errors.   1 - VDMA Slave Error detected. VDMA Engine halts. This error occurs if the slave read from the Memory Map interface issues a Slave Error.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="5"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="5"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="VDMADecErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="VDMA Decode Error. This error occurs if the address request is to an invalid address.   0 = No VDMA Decode Errors.   1 = VDMA Decode Error detected. VDMA channel halts.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="6"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="6"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="SOFEarlyErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Start of Frame Early Error   0 - No start-of-frame Error   1 - Start of Frame Early Error detected. VDMA does not halt. This error occurs if incoming frame size is lesser than programmed vsize value. Write 1 to Clear in flush on fsync mode and Read Only otherwise.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="7"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="EOLEarlyErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="End of Line Early Error.   0 - No End of Line Early Error   1 - End of Line Early Error detected. VDMA does not halt. This error occurs if the incoming line size is lesser than the programmed hsize value. Write 1 to clear.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="8"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="SOFLateErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Start of Frame Late Error.   0 - No start-of-frame Late Error   1 - Start of Frame Late Error detected. VDMA does not halt. This error occurs if the incoming frame size is greater than the programmed vsize value. Write 1 to Clear in flush on fsync mode.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="11"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="11"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="FrmCnt_Irq">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Frame Count Interrupt.   0 - No Frame Count Interrupt.   1 - Frame Count Interrupt detected. If enabled (DMACR.FrmCnt_IrqEn = 1) and if the interrupt threshold has been met, an interrupt out is generated.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="12"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="DlyCnt_Irq">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Delay.   0 - No Delay Interrupt.   1 - Delay Interrupt detected. If enabled (DMACR.DlyCnt_IrqEn = 1), an interrupt out is generated when the delay count reaches its programmed value.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="13"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="Err_Irq">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt on Error.   0 - No error Interrupt.   1 - Error interrupt detected. If enabled (VDMACR.Err_IrqEn = 1), an interrupt out is generated when an error is detected.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="14"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="EOLLateErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="End of Line Late Error.   0 - No End of Line Late Error   1 - End of Line Late Error detected. VDMA does not halt. This error occurs if the incoming line size is greater than the programmed hsize value. Write 1 to clear&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="15"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE="oneToClear"/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="15"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="IRQFrameCntSts">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt Frame Count Status. Indicates current interrupt frame count value.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="16"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
                <FIELD NAME="IRQDelayCntSts">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Interrupt Delay Count Status. Indicates current interrupt delay time value.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-only"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="8"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_VDMA_IRQ_MASK">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Error Interrupt Mask Register"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x3C"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="IRQMaskSOFEarlyErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="1 - Masks interrupt due to SOFEarlyErr.&#xA;0 - Does not mask interrupt due to SOFEarlyErr.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="IRQMaskEOLEarlyErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="1 - Masks interrupt due to EOLEarlyErr.&#xA;0 - Does not mask interrupt due to EOLEarlyErr.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="1"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="1"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="IRQMaskSOFLateErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="1 - Masks interrupt due to SOFLateErr.&#xA;0 - Does not mask interrupt due to SOFLateErr.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="2"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="2"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
                <FIELD NAME="IRQMaskEOLLateErr">
                  <PROPERTY NAME="DESCRIPTION" VALUE="1 = Masks interrupt due to EOLLateErr.&#xA;0 = Does not mask interrupt due to EOLLateErr.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="3"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="3"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_REG_INDEX">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Register Index"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x44"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="S2MM_Reg_Index">
                  <PROPERTY NAME="DESCRIPTION" VALUE="When Frame Buffers is greater than 16&#xA;  0 - Any write or read access between 0xAC to 0xE8 accesses the Start Address 1 to 16.&#xA;  1 - Any write or read access between 0xAC to 0xE8 accesses the Start Address 17 to 32.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="1"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_VSIZE">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Vertical Size"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x50"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Vertical_Size">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the vertical size in lines of the video data to transfer.&#xA;Note: Writing a value of zero in this field causes a VDMAIntErr to be flagged in the MM2S_VDMASR register on the next frame boundary.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="13"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_HSIZE">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Horizontal Size"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x54"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Horizontal_Size">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the horizontal size in bytes of the video data to transfer.&#xA;Note: A value of zero in this field when MM2S_VSIZE is written causes a VDMAIntErr to be flagged in the MM2S_VDMASR register on the next frame boundary.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="16"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_FRMDLY_STRIDE">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Frame Delay and Stride"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x58"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Stride">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the number of address bytes between the first pixels of each video line.&#xA;Note: A stride value less than MM2S_HSIZE causes data to be corrupted.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="16"/>
                </FIELD>
                <FIELD NAME="Frame_Delay">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the minimum number of frame buffers the Genlock slave is to be behind the locked master. This field is only used if the channel is enabled for Genlock Slave operations. This field has no meaning in other Genlock modes.&#xA;Note: Frame Delay must be less than or equal to Frame Buffers or an undefined results occur.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="5"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA1">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 1"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x5C"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address1">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 1&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA2">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 2"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x60"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address2">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 2&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA3">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 3"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x64"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address3">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 3&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA4">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 4"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x68"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address4">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 4&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA5">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 5"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x6C"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address5">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 5&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA6">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 6"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x70"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address6">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 6&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA7">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 7"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x74"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address7">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 7&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA8">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 8"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x78"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address8">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 8&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA9">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 9"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x7C"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address9">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 9&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA10">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 10"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x80"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address10">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 10&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA11">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 11"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x84"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address11">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 11&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA12">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 12"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x88"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address12">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 12&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA13">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 13"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x8C"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address13">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 13&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA14">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 14"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x90"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address14">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 14&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA15">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 15"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x94"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address15">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 15&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="MM2S_SA16">
              <PROPERTY NAME="DESCRIPTION" VALUE="MM2S Start Address Register 16"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0x98"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address16">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 16&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_VSIZE">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Vertical Size"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xA0"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Vertical_Size">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the vertical size in lines of the video data to transfer.&#xA;Note: Writing a value of zero in this field causes a VDMAIntErr to be flagged in the S2MM_VDMASR register on the next frame boundary.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="13"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_HSIZE">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Horizontal Size"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xA4"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Horizontal_Size">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the horizontal size in bytes of the video data to transfer.&#xA;Note: A value of zero in this field when S2MM_VSIZE is written causes a VDMAIntErr to be flagged in the S2MM_VDMASR register on the next frame boundary.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="16"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_FRMDLY_STRIDE">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Frame Delay and Stride"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xA8"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Stride">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the number of address bytes between the first pixels of each video line.&#xA;Note: A stride value less than S2MM_HSIZE causes data to be corrupted.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="16"/>
                </FIELD>
                <FIELD NAME="Frame_Delay">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the minimum number of frame buffers the Genlock slave is to be behind the locked master. This field is only used if the channel is enabled for Genlock Slave operations. This field has no meaning in other Genlock modes.&#xA;Note: Frame Delay must be less than or equal to Frame Buffers or an undefined results occur.&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="24"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="5"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA1">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 1"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xAC"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address1">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 1&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA2">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 2"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xB0"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address2">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 2&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA3">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 3"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xB4"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address3">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 3&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA4">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 4"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xB8"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address4">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 4&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA5">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 5"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xBC"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address5">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 5&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA6">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 6"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xC0"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address6">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 6&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA7">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 7"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xC4"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address7">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 7&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA8">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 8"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xC8"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address8">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 8&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA9">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 9"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xCC"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address9">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 9&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA10">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 10"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xD0"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address10">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 10&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA11">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 11"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xD4"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address11">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 11&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA12">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 12"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xD8"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address12">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 12&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA13">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 13"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xDC"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address13">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 13&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA14">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 14"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xE0"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address14">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 14&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA15">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 15"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xE4"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address15">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 15&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
            <REGISTER NAME="S2MM_SA16">
              <PROPERTY NAME="DESCRIPTION" VALUE="S2MM Start Address Register 16"/>
              <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0xE8"/>
              <PROPERTY NAME="SIZE" VALUE="32"/>
              <PROPERTY NAME="ACCESS" VALUE="read-write"/>
              <PROPERTY NAME="IS_ENABLED" VALUE="true"/>
              <PROPERTY NAME="RESET_VALUE" VALUE="0x0"/>
              <FIELDS>
                <FIELD NAME="Start_Address16">
                  <PROPERTY NAME="DESCRIPTION" VALUE="Indicates the Start Address for video buffer 16&#xA;"/>
                  <PROPERTY NAME="ADDRESS_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="ACCESS" VALUE="read-write"/>
                  <PROPERTY NAME="MODIFIED_READ_VALUES" VALUE=""/>
                  <PROPERTY NAME="WRITE_CONSTRAINT" VALUE="0"/>
                  <PROPERTY NAME="READ_ACTION" VALUE=""/>
                  <PROPERTY NAME="BIT_OFFSET" VALUE="0"/>
                  <PROPERTY NAME="BIT_WIDTH" VALUE="32"/>
                </FIELD>
              </FIELDS>
            </REGISTER>
          </REGISTERS>
        </ADDRESSBLOCK>
      </ADDRESSBLOCKS>
      <PARAMETERS>
        <PARAMETER NAME="C_S_AXI_LITE_ADDR_WIDTH" VALUE="9"/>
        <PARAMETER NAME="C_S_AXI_LITE_DATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_DLYTMR_RESOLUTION" VALUE="125"/>
        <PARAMETER NAME="C_PRMRY_IS_ACLK_ASYNC" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_VIDPRMTR_READS" VALUE="1"/>
        <PARAMETER NAME="C_DYNAMIC_RESOLUTION" VALUE="1"/>
        <PARAMETER NAME="C_NUM_FSTORES" VALUE="2"/>
        <PARAMETER NAME="C_USE_FSYNC" VALUE="1"/>
        <PARAMETER NAME="C_USE_MM2S_FSYNC" VALUE="0"/>
        <PARAMETER NAME="C_USE_S2MM_FSYNC" VALUE="2"/>
        <PARAMETER NAME="C_FLUSH_ON_FSYNC" VALUE="1"/>
        <PARAMETER NAME="C_INCLUDE_INTERNAL_GENLOCK" VALUE="1"/>
        <PARAMETER NAME="C_INCLUDE_SG" VALUE="0"/>
        <PARAMETER NAME="C_M_AXI_SG_ADDR_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_M_AXI_SG_DATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_INCLUDE_MM2S" VALUE="1"/>
        <PARAMETER NAME="C_MM2S_GENLOCK_MODE" VALUE="3"/>
        <PARAMETER NAME="C_MM2S_GENLOCK_NUM_MASTERS" VALUE="1"/>
        <PARAMETER NAME="C_MM2S_GENLOCK_REPEAT_EN" VALUE="0"/>
        <PARAMETER NAME="C_MM2S_SOF_ENABLE" VALUE="1"/>
        <PARAMETER NAME="C_INCLUDE_MM2S_DRE" VALUE="0"/>
        <PARAMETER NAME="C_INCLUDE_MM2S_SF" VALUE="0"/>
        <PARAMETER NAME="C_MM2S_LINEBUFFER_DEPTH" VALUE="2048"/>
        <PARAMETER NAME="C_MM2S_LINEBUFFER_THRESH" VALUE="4"/>
        <PARAMETER NAME="C_MM2S_MAX_BURST_LENGTH" VALUE="64"/>
        <PARAMETER NAME="C_M_AXI_MM2S_ADDR_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_M_AXI_MM2S_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="C_M_AXIS_MM2S_TDATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_M_AXIS_MM2S_TUSER_BITS" VALUE="1"/>
        <PARAMETER NAME="C_INCLUDE_S2MM" VALUE="0"/>
        <PARAMETER NAME="C_S2MM_GENLOCK_MODE" VALUE="0"/>
        <PARAMETER NAME="C_S2MM_GENLOCK_NUM_MASTERS" VALUE="1"/>
        <PARAMETER NAME="C_S2MM_GENLOCK_REPEAT_EN" VALUE="1"/>
        <PARAMETER NAME="C_S2MM_SOF_ENABLE" VALUE="1"/>
        <PARAMETER NAME="C_INCLUDE_S2MM_DRE" VALUE="0"/>
        <PARAMETER NAME="C_INCLUDE_S2MM_SF" VALUE="1"/>
        <PARAMETER NAME="C_S2MM_LINEBUFFER_DEPTH" VALUE="512"/>
        <PARAMETER NAME="C_S2MM_LINEBUFFER_THRESH" VALUE="4"/>
        <PARAMETER NAME="C_S2MM_MAX_BURST_LENGTH" VALUE="8"/>
        <PARAMETER NAME="C_M_AXI_S2MM_ADDR_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_M_AXI_S2MM_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="C_S_AXIS_S2MM_TDATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_S_AXIS_S2MM_TUSER_BITS" VALUE="1"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_ALL" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_0" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_1" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_2" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_3" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_4" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_5" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_6" VALUE="1"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_7" VALUE="1"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_8" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_9" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_10" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_11" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_12" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_13" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_14" VALUE="1"/>
        <PARAMETER NAME="C_ENABLE_DEBUG_INFO_15" VALUE="1"/>
        <PARAMETER NAME="C_INSTANCE" VALUE="axi_vdma"/>
        <PARAMETER NAME="C_SELECT_XPM" VALUE="0"/>
        <PARAMETER NAME="C_ENABLE_VERT_FLIP" VALUE="0"/>
        <PARAMETER NAME="C_FAMILY" VALUE="zynq"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_axi_vdma_0_0"/>
        <PARAMETER NAME="c_m_axi_mm2s_data_width" VALUE="64"/>
        <PARAMETER NAME="c_m_axis_mm2s_tdata_width" VALUE="32"/>
        <PARAMETER NAME="c_m_axi_s2mm_data_width" VALUE="64"/>
        <PARAMETER NAME="c_s_axis_s2mm_tdata_width" VALUE="32"/>
        <PARAMETER NAME="c_include_s2mm_dre" VALUE="0"/>
        <PARAMETER NAME="c_include_s2mm_sf" VALUE="1"/>
        <PARAMETER NAME="c_include_mm2s_sf" VALUE="0"/>
        <PARAMETER NAME="c_enable_vidprmtr_reads" VALUE="1"/>
        <PARAMETER NAME="c_num_fstores" VALUE="2"/>
        <PARAMETER NAME="c_use_fsync" VALUE="1"/>
        <PARAMETER NAME="c_use_mm2s_fsync" VALUE="0"/>
        <PARAMETER NAME="c_use_s2mm_fsync" VALUE="2"/>
        <PARAMETER NAME="c_mm2s_sof_enable" VALUE="1"/>
        <PARAMETER NAME="c_s2mm_sof_enable" VALUE="1"/>
        <PARAMETER NAME="c_include_internal_genlock" VALUE="1"/>
        <PARAMETER NAME="c_mm2s_genlock_mode" VALUE="3"/>
        <PARAMETER NAME="c_mm2s_genlock_num_masters" VALUE="1"/>
        <PARAMETER NAME="c_s2mm_genlock_mode" VALUE="0"/>
        <PARAMETER NAME="c_s2mm_genlock_num_masters" VALUE="1"/>
        <PARAMETER NAME="c_mm2s_linebuffer_depth" VALUE="2048"/>
        <PARAMETER NAME="c_mm2s_linebuffer_thresh" VALUE="4"/>
        <PARAMETER NAME="c_s2mm_linebuffer_depth" VALUE="512"/>
        <PARAMETER NAME="c_s2mm_linebuffer_thresh" VALUE="4"/>
        <PARAMETER NAME="c_include_mm2s" VALUE="1"/>
        <PARAMETER NAME="c_mm2s_max_burst_length" VALUE="64"/>
        <PARAMETER NAME="c_include_sg" VALUE="0"/>
        <PARAMETER NAME="c_dlytmr_resolution" VALUE="125"/>
        <PARAMETER NAME="c_prmry_is_aclk_async" VALUE="0"/>
        <PARAMETER NAME="c_dynamic_resolution" VALUE="1"/>
        <PARAMETER NAME="c_s2mm_max_burst_length" VALUE="8"/>
        <PARAMETER NAME="c_include_s2mm" VALUE="0"/>
        <PARAMETER NAME="c_enable_all" VALUE="0"/>
        <PARAMETER NAME="c_enable_mm2s_rst_out" VALUE="0"/>
        <PARAMETER NAME="c_enable_mm2s_buf_empty" VALUE="0"/>
        <PARAMETER NAME="c_enable_mm2s_param_updt" VALUE="0"/>
        <PARAMETER NAME="c_enable_mm2s_fsync_out" VALUE="0"/>
        <PARAMETER NAME="c_enable_tstvec" VALUE="0"/>
        <PARAMETER NAME="c_enable_mm2s_frmstr_reg" VALUE="0"/>
        <PARAMETER NAME="c_enable_mm2s_delay_counter" VALUE="1"/>
        <PARAMETER NAME="c_enable_mm2s_frm_counter" VALUE="1"/>
        <PARAMETER NAME="c_enable_s2mm_rst_out" VALUE="0"/>
        <PARAMETER NAME="c_enable_s2mm_buf_full" VALUE="0"/>
        <PARAMETER NAME="c_enable_s2mm_param_updt" VALUE="0"/>
        <PARAMETER NAME="c_enable_s2mm_fsync_out" VALUE="0"/>
        <PARAMETER NAME="c_enable_s2mm_sts_reg" VALUE="0"/>
        <PARAMETER NAME="c_enable_s2mm_frmstr_reg" VALUE="0"/>
        <PARAMETER NAME="c_enable_s2mm_delay_counter" VALUE="1"/>
        <PARAMETER NAME="c_enable_s2mm_frm_counter" VALUE="1"/>
        <PARAMETER NAME="c_include_mm2s_dre" VALUE="0"/>
        <PARAMETER NAME="c_flush_on_fsync" VALUE="1"/>
        <PARAMETER NAME="c_s2mm_genlock_repeat_en" VALUE="1"/>
        <PARAMETER NAME="c_mm2s_genlock_repeat_en" VALUE="0"/>
        <PARAMETER NAME="c_addr_width" VALUE="32"/>
        <PARAMETER NAME="c_single_interface" VALUE="0"/>
        <PARAMETER NAME="c_enable_vert_flip" VALUE="0"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
        <PARAMETER NAME="C_BASEADDR" VALUE="0x43000000"/>
        <PARAMETER NAME="C_HIGHADDR" VALUE="0x4300FFFF"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="s_axi_lite_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="m_axi_mm2s_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="m_axis_mm2s_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="axi_resetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_lite_awvalid" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_lite_awready" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="8" NAME="s_axi_lite_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_lite_wvalid" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_lite_wready" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="s_axi_lite_wdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s_axi_lite_bresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_lite_bvalid" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_lite_bready" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_lite_arvalid" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_lite_arready" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="8" NAME="s_axi_lite_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_lite_rvalid" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_lite_rready" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="s_axi_lite_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s_axi_lite_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="5" NAME="mm2s_frame_ptr_in" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_slv_vdma_frame_out">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="slv_vdma_frame_out"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="5" NAME="mm2s_frame_ptr_out" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_mm2s_frame_ptr_out">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="slv_vdma_frame_in"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="m_axi_mm2s_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="m_axi_mm2s_arlen" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_arlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="m_axi_mm2s_arsize" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_arsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="m_axi_mm2s_arburst" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_arburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="m_axi_mm2s_arprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="m_axi_mm2s_arcache" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_arcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m_axi_mm2s_arvalid" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m_axi_mm2s_arready" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="63" NAME="m_axi_mm2s_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="m_axi_mm2s_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m_axi_mm2s_rlast" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_rlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m_axi_mm2s_rvalid" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m_axi_mm2s_rready" SIGIS="undef" SIGNAME="axi_smc_S00_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="S00_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="m_axis_mm2s_tdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="s_axis_video_tdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="m_axis_mm2s_tkeep" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="0" NAME="m_axis_mm2s_tuser" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tuser">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="s_axis_video_tuser"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m_axis_mm2s_tvalid" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="s_axis_video_tvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="m_axis_mm2s_tready" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="s_axis_video_tready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="m_axis_mm2s_tlast" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="s_axis_video_tlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="mm2s_introut" SENSITIVITY="LEVEL_HIGH" SIGIS="INTERRUPT" SIGNAME="axi_vdma_0_mm2s_introut">
          <CONNECTIONS>
            <CONNECTION INSTANCE="xlconcat_0" PORT="In0"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M00_AXI" DATAWIDTH="32" NAME="S_AXI_LITE" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4LITE"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="9"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="0"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="0"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="1"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="s_axi_lite_araddr"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="s_axi_lite_arready"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="s_axi_lite_arvalid"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="s_axi_lite_awaddr"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="s_axi_lite_awready"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="s_axi_lite_awvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="s_axi_lite_bready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="s_axi_lite_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="s_axi_lite_bvalid"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="s_axi_lite_rdata"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="s_axi_lite_rready"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="s_axi_lite_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="s_axi_lite_rvalid"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="s_axi_lite_wdata"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="s_axi_lite_wready"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="s_axi_lite_wvalid"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="axi_vdma_0_M_AXI_MM2S" DATAWIDTH="64" NAME="M_AXI_MM2S" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="64"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_ONLY"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="0"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="0"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="0"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="64"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="m_axi_mm2s_araddr"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="m_axi_mm2s_arburst"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="m_axi_mm2s_arcache"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="m_axi_mm2s_arlen"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="m_axi_mm2s_arprot"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="m_axi_mm2s_arready"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="m_axi_mm2s_arsize"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="m_axi_mm2s_arvalid"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="m_axi_mm2s_rdata"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="m_axi_mm2s_rlast"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="m_axi_mm2s_rready"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="m_axi_mm2s_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="m_axi_mm2s_rvalid"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="axi_vdma_0_M_AXIS_MM2S" NAME="M_AXIS_MM2S" TYPE="INITIATOR" VLNV="xilinx.com:interface:axis:1.0">
          <PARAMETER NAME="TDATA_NUM_BYTES" VALUE="4"/>
          <PARAMETER NAME="TDEST_WIDTH" VALUE="0"/>
          <PARAMETER NAME="TID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="TUSER_WIDTH" VALUE="1"/>
          <PARAMETER NAME="HAS_TREADY" VALUE="1"/>
          <PARAMETER NAME="HAS_TSTRB" VALUE="0"/>
          <PARAMETER NAME="HAS_TKEEP" VALUE="1"/>
          <PARAMETER NAME="HAS_TLAST" VALUE="1"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="LAYERED_METADATA" VALUE="undef"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="TDATA" PHYSICAL="m_axis_mm2s_tdata"/>
            <PORTMAP LOGICAL="TKEEP" PHYSICAL="m_axis_mm2s_tkeep"/>
            <PORTMAP LOGICAL="TLAST" PHYSICAL="m_axis_mm2s_tlast"/>
            <PORTMAP LOGICAL="TREADY" PHYSICAL="m_axis_mm2s_tready"/>
            <PORTMAP LOGICAL="TUSER" PHYSICAL="m_axis_mm2s_tuser"/>
            <PORTMAP LOGICAL="TVALID" PHYSICAL="m_axis_mm2s_tvalid"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
      <MEMORYMAP>
        <MEMRANGE ADDRESSBLOCK="HP0_DDR_LOWOCM" BASENAME="C_BASEADDR" BASEVALUE="0x00000000" HIGHNAME="C_HIGHADDR" HIGHVALUE="0x0FFFFFFF" INSTANCE="processing_system7_0" IS_DATA="TRUE" IS_INSTRUCTION="TRUE" MASTERBUSINTERFACE="M_AXI_MM2S" MEMTYPE="MEMORY" SLAVEBUSINTERFACE="S_AXI_HP0"/>
      </MEMORYMAP>
      <PERIPHERALS>
        <PERIPHERAL INSTANCE="processing_system7_0"/>
        <PERIPHERAL INSTANCE="v_axi4s_vid_out_0"/>
      </PERIPHERALS>
    </MODULE>
    <MODULE COREREVISION="1" FULLNAME="/misc_0" HWVERSION="1.0" INSTANCE="misc_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="misc" VLNV="echo:user:misc:1.0">
      <DOCUMENTS/>
      <ADDRESSBLOCKS>
        <ADDRESSBLOCK ACCESS="" INTERFACE="S00_AXI" NAME="S00_AXI_reg" RANGE="4096" USAGE="register"/>
      </ADDRESSBLOCKS>
      <PARAMETERS>
        <PARAMETER NAME="C_S00_AXI_DATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_S00_AXI_ADDR_WIDTH" VALUE="5"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_misc_0_0"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
        <PARAMETER NAME="C_S00_AXI_BASEADDR" VALUE="0x43C20000"/>
        <PARAMETER NAME="C_S00_AXI_HIGHADDR" VALUE="0x43C2FFFF"/>
      </PARAMETERS>
      <PORTS>
        <PORT DIR="I" LEFT="5" NAME="slv_vdma_frame_in" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_mm2s_frame_ptr_out">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="mm2s_frame_ptr_out"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="5" NAME="slv_vdma_frame_out" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_slv_vdma_frame_out">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="mm2s_frame_ptr_in"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="4" NAME="s00_axi_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="s00_axi_awprot" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_awvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_awready" SIGIS="undef" SIGNAME="misc_0_s00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="s00_axi_wdata" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="s00_axi_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_wvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_wready" SIGIS="undef" SIGNAME="misc_0_s00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s00_axi_bresp" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_bvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_bready" SIGIS="undef" SIGNAME="misc_0_s00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="4" NAME="s00_axi_araddr" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="s00_axi_arprot" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_arvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_arready" SIGIS="undef" SIGNAME="misc_0_s00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="s00_axi_rdata" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s00_axi_rresp" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s00_axi_rvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_rready" SIGIS="undef" SIGNAME="misc_0_s00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="s00_axi_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s00_axi_aresetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M03_AXI" DATAWIDTH="32" NAME="S00_AXI" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="WIZ_DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="WIZ_NUM_REG" VALUE="8"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4LITE"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="5"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="1"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="1"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="s00_axi_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="s00_axi_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="s00_axi_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="s00_axi_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="s00_axi_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="s00_axi_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="s00_axi_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="s00_axi_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="s00_axi_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="s00_axi_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="s00_axi_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="s00_axi_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="s00_axi_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="s00_axi_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="s00_axi_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="s00_axi_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="s00_axi_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="s00_axi_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="s00_axi_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
    </MODULE>
    <MODULE CONFIGURABLE="TRUE" COREREVISION="6" FULLNAME="/processing_system7_0" HWVERSION="5.5" INSTANCE="processing_system7_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" IS_PL="FALSE" MODTYPE="processing_system7" VLNV="xilinx.com:ip:processing_system7:5.5">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=processing_system7;v=v5_3;d=pg082-processing-system7.pdf"/>
      </DOCUMENTS>
      <ADDRESSBLOCKS>
        <ADDRESSBLOCK ACCESS="" INTERFACE="S_AXI_HP1" NAME="HP1_DDR_LOWOCM" RANGE="1073741824" USAGE="memory"/>
      </ADDRESSBLOCKS>
      <PARAMETERS>
        <PARAMETER NAME="C_EN_EMIO_PJTAG" VALUE="0"/>
        <PARAMETER NAME="C_EN_EMIO_ENET0" VALUE="0"/>
        <PARAMETER NAME="C_EN_EMIO_ENET1" VALUE="0"/>
        <PARAMETER NAME="C_EN_EMIO_TRACE" VALUE="0"/>
        <PARAMETER NAME="C_INCLUDE_TRACE_BUFFER" VALUE="0"/>
        <PARAMETER NAME="C_TRACE_BUFFER_FIFO_SIZE" VALUE="128"/>
        <PARAMETER NAME="USE_TRACE_DATA_EDGE_DETECTOR" VALUE="0"/>
        <PARAMETER NAME="C_TRACE_PIPELINE_WIDTH" VALUE="8"/>
        <PARAMETER NAME="C_TRACE_BUFFER_CLOCK_DELAY" VALUE="12"/>
        <PARAMETER NAME="C_EMIO_GPIO_WIDTH" VALUE="3"/>
        <PARAMETER NAME="C_INCLUDE_ACP_TRANS_CHECK" VALUE="0"/>
        <PARAMETER NAME="C_USE_DEFAULT_ACP_USER_VAL" VALUE="0"/>
        <PARAMETER NAME="C_S_AXI_ACP_ARUSER_VAL" VALUE="31"/>
        <PARAMETER NAME="C_S_AXI_ACP_AWUSER_VAL" VALUE="31"/>
        <PARAMETER NAME="C_M_AXI_GP0_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="C_M_AXI_GP0_ENABLE_STATIC_REMAP" VALUE="0"/>
        <PARAMETER NAME="C_M_AXI_GP1_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="C_M_AXI_GP1_ENABLE_STATIC_REMAP" VALUE="0"/>
        <PARAMETER NAME="C_S_AXI_GP0_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="C_S_AXI_GP1_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="C_S_AXI_ACP_ID_WIDTH" VALUE="3"/>
        <PARAMETER NAME="C_S_AXI_HP0_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="C_S_AXI_HP0_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="C_S_AXI_HP1_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="C_S_AXI_HP1_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="C_S_AXI_HP2_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="C_S_AXI_HP2_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="C_S_AXI_HP3_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="C_S_AXI_HP3_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="C_M_AXI_GP0_THREAD_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="C_M_AXI_GP1_THREAD_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="C_NUM_F2P_INTR_INPUTS" VALUE="2"/>
        <PARAMETER NAME="C_IRQ_F2P_MODE" VALUE="DIRECT"/>
        <PARAMETER NAME="C_DQ_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_DQS_WIDTH" VALUE="4"/>
        <PARAMETER NAME="C_DM_WIDTH" VALUE="4"/>
        <PARAMETER NAME="C_MIO_PRIMITIVE" VALUE="54"/>
        <PARAMETER NAME="C_TRACE_INTERNAL_WIDTH" VALUE="2"/>
        <PARAMETER NAME="C_USE_AXI_NONSECURE" VALUE="0"/>
        <PARAMETER NAME="C_USE_M_AXI_GP0" VALUE="1"/>
        <PARAMETER NAME="C_USE_M_AXI_GP1" VALUE="0"/>
        <PARAMETER NAME="C_USE_S_AXI_GP0" VALUE="0"/>
        <PARAMETER NAME="C_USE_S_AXI_GP1" VALUE="0"/>
        <PARAMETER NAME="C_USE_S_AXI_HP0" VALUE="1"/>
        <PARAMETER NAME="C_USE_S_AXI_HP1" VALUE="1"/>
        <PARAMETER NAME="C_USE_S_AXI_HP2" VALUE="0"/>
        <PARAMETER NAME="C_USE_S_AXI_HP3" VALUE="0"/>
        <PARAMETER NAME="C_USE_S_AXI_ACP" VALUE="0"/>
        <PARAMETER NAME="C_PS7_SI_REV" VALUE="PRODUCTION"/>
        <PARAMETER NAME="C_FCLK_CLK0_BUF" VALUE="TRUE"/>
        <PARAMETER NAME="C_FCLK_CLK1_BUF" VALUE="FALSE"/>
        <PARAMETER NAME="C_FCLK_CLK2_BUF" VALUE="FALSE"/>
        <PARAMETER NAME="C_FCLK_CLK3_BUF" VALUE="FALSE"/>
        <PARAMETER NAME="C_PACKAGE_NAME" VALUE="clg400"/>
        <PARAMETER NAME="C_GP0_EN_MODIFIABLE_TXN" VALUE="1"/>
        <PARAMETER NAME="C_GP1_EN_MODIFIABLE_TXN" VALUE="1"/>
        <PARAMETER NAME="PCW_DDR_RAM_BASEADDR" VALUE="0x00100000"/>
        <PARAMETER NAME="PCW_DDR_RAM_HIGHADDR" VALUE="0x3FFFFFFF"/>
        <PARAMETER NAME="PCW_UART0_BASEADDR" VALUE="0xE0000000"/>
        <PARAMETER NAME="PCW_UART0_HIGHADDR" VALUE="0xE0000FFF"/>
        <PARAMETER NAME="PCW_UART1_BASEADDR" VALUE="0xE0001000"/>
        <PARAMETER NAME="PCW_UART1_HIGHADDR" VALUE="0xE0001FFF"/>
        <PARAMETER NAME="PCW_I2C0_BASEADDR" VALUE="0xE0004000"/>
        <PARAMETER NAME="PCW_I2C0_HIGHADDR" VALUE="0xE0004FFF"/>
        <PARAMETER NAME="PCW_I2C1_BASEADDR" VALUE="0xE0005000"/>
        <PARAMETER NAME="PCW_I2C1_HIGHADDR" VALUE="0xE0005FFF"/>
        <PARAMETER NAME="PCW_SPI0_BASEADDR" VALUE="0xE0006000"/>
        <PARAMETER NAME="PCW_SPI0_HIGHADDR" VALUE="0xE0006FFF"/>
        <PARAMETER NAME="PCW_SPI1_BASEADDR" VALUE="0xE0007000"/>
        <PARAMETER NAME="PCW_SPI1_HIGHADDR" VALUE="0xE0007FFF"/>
        <PARAMETER NAME="PCW_CAN0_BASEADDR" VALUE="0xE0008000"/>
        <PARAMETER NAME="PCW_CAN0_HIGHADDR" VALUE="0xE0008FFF"/>
        <PARAMETER NAME="PCW_CAN1_BASEADDR" VALUE="0xE0009000"/>
        <PARAMETER NAME="PCW_CAN1_HIGHADDR" VALUE="0xE0009FFF"/>
        <PARAMETER NAME="PCW_GPIO_BASEADDR" VALUE="0xE000A000"/>
        <PARAMETER NAME="PCW_GPIO_HIGHADDR" VALUE="0xE000AFFF"/>
        <PARAMETER NAME="PCW_ENET0_BASEADDR" VALUE="0xE000B000"/>
        <PARAMETER NAME="PCW_ENET0_HIGHADDR" VALUE="0xE000BFFF"/>
        <PARAMETER NAME="PCW_ENET1_BASEADDR" VALUE="0xE000C000"/>
        <PARAMETER NAME="PCW_ENET1_HIGHADDR" VALUE="0xE000CFFF"/>
        <PARAMETER NAME="PCW_SDIO0_BASEADDR" VALUE="0xE0100000"/>
        <PARAMETER NAME="PCW_SDIO0_HIGHADDR" VALUE="0xE0100FFF"/>
        <PARAMETER NAME="PCW_SDIO1_BASEADDR" VALUE="0xE0101000"/>
        <PARAMETER NAME="PCW_SDIO1_HIGHADDR" VALUE="0xE0101FFF"/>
        <PARAMETER NAME="PCW_USB0_BASEADDR" VALUE="0xE0102000"/>
        <PARAMETER NAME="PCW_USB0_HIGHADDR" VALUE="0xE0102fff"/>
        <PARAMETER NAME="PCW_USB1_BASEADDR" VALUE="0xE0103000"/>
        <PARAMETER NAME="PCW_USB1_HIGHADDR" VALUE="0xE0103fff"/>
        <PARAMETER NAME="PCW_TTC0_BASEADDR" VALUE="0xE0104000"/>
        <PARAMETER NAME="PCW_TTC0_HIGHADDR" VALUE="0xE0104fff"/>
        <PARAMETER NAME="PCW_TTC1_BASEADDR" VALUE="0xE0105000"/>
        <PARAMETER NAME="PCW_TTC1_HIGHADDR" VALUE="0xE0105fff"/>
        <PARAMETER NAME="PCW_FCLK_CLK0_BUF" VALUE="TRUE"/>
        <PARAMETER NAME="PCW_FCLK_CLK1_BUF" VALUE="FALSE"/>
        <PARAMETER NAME="PCW_FCLK_CLK2_BUF" VALUE="FALSE"/>
        <PARAMETER NAME="PCW_FCLK_CLK3_BUF" VALUE="FALSE"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_FREQ_MHZ" VALUE="533.333333"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_BANK_ADDR_COUNT" VALUE="3"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_ROW_ADDR_COUNT" VALUE="15"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_COL_ADDR_COUNT" VALUE="10"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CL" VALUE="7"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CWL" VALUE="6"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_T_RCD" VALUE="7"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_T_RP" VALUE="7"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_T_RC" VALUE="48.91"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_T_RAS_MIN" VALUE="35.0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_T_FAW" VALUE="40.0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_AL" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_0" VALUE="0.0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_1" VALUE="0.0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_2" VALUE="0.0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_TO_CLK_DELAY_3" VALUE="0.0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY0" VALUE="0.25"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY1" VALUE="0.25"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY2" VALUE="0.25"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_BOARD_DELAY3" VALUE="0.25"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_0_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_1_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_2_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_3_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_0_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_1_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_2_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_3_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_0_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_1_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_2_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_3_LENGTH_MM" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_0_PACKAGE_LENGTH" VALUE="101.239"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_1_PACKAGE_LENGTH" VALUE="79.5025"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_2_PACKAGE_LENGTH" VALUE="60.536"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_3_PACKAGE_LENGTH" VALUE="71.7715"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_0_PACKAGE_LENGTH" VALUE="104.5365"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_1_PACKAGE_LENGTH" VALUE="70.676"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_2_PACKAGE_LENGTH" VALUE="59.1615"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_3_PACKAGE_LENGTH" VALUE="81.319"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_0_PACKAGE_LENGTH" VALUE="54.563"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_1_PACKAGE_LENGTH" VALUE="54.563"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_2_PACKAGE_LENGTH" VALUE="54.563"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_3_PACKAGE_LENGTH" VALUE="54.563"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_0_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_1_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_2_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQS_3_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_0_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_1_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_2_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DQ_3_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_0_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_1_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_2_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_3_PROPOGATION_DELAY" VALUE="160"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_0" VALUE="-0.047"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_1" VALUE="-0.025"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_2" VALUE="-0.006"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_DQS_TO_CLK_DELAY_3" VALUE="-0.017"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_BOARD_DELAY0" VALUE="0.080"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_BOARD_DELAY1" VALUE="0.063"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_BOARD_DELAY2" VALUE="0.057"/>
        <PARAMETER NAME="PCW_PACKAGE_DDR_BOARD_DELAY3" VALUE="0.068"/>
        <PARAMETER NAME="PCW_CPU_CPU_6X4X_MAX_RANGE" VALUE="767"/>
        <PARAMETER NAME="PCW_CRYSTAL_PERIPHERAL_FREQMHZ" VALUE="33.333333"/>
        <PARAMETER NAME="PCW_APU_PERIPHERAL_FREQMHZ" VALUE="666.666666"/>
        <PARAMETER NAME="PCW_DCI_PERIPHERAL_FREQMHZ" VALUE="10.159"/>
        <PARAMETER NAME="PCW_QSPI_PERIPHERAL_FREQMHZ" VALUE="200"/>
        <PARAMETER NAME="PCW_SMC_PERIPHERAL_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_USB0_PERIPHERAL_FREQMHZ" VALUE="60"/>
        <PARAMETER NAME="PCW_USB1_PERIPHERAL_FREQMHZ" VALUE="60"/>
        <PARAMETER NAME="PCW_SDIO_PERIPHERAL_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_UART_PERIPHERAL_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_SPI_PERIPHERAL_FREQMHZ" VALUE="166.666666"/>
        <PARAMETER NAME="PCW_CAN_PERIPHERAL_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_CAN0_PERIPHERAL_FREQMHZ" VALUE="-1"/>
        <PARAMETER NAME="PCW_CAN1_PERIPHERAL_FREQMHZ" VALUE="-1"/>
        <PARAMETER NAME="PCW_I2C_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_WDT_PERIPHERAL_FREQMHZ" VALUE="133.333333"/>
        <PARAMETER NAME="PCW_TTC_PERIPHERAL_FREQMHZ" VALUE="50"/>
        <PARAMETER NAME="PCW_TTC0_CLK0_PERIPHERAL_FREQMHZ" VALUE="133.333333"/>
        <PARAMETER NAME="PCW_TTC0_CLK1_PERIPHERAL_FREQMHZ" VALUE="133.333333"/>
        <PARAMETER NAME="PCW_TTC0_CLK2_PERIPHERAL_FREQMHZ" VALUE="133.333333"/>
        <PARAMETER NAME="PCW_TTC1_CLK0_PERIPHERAL_FREQMHZ" VALUE="133.333333"/>
        <PARAMETER NAME="PCW_TTC1_CLK1_PERIPHERAL_FREQMHZ" VALUE="133.333333"/>
        <PARAMETER NAME="PCW_TTC1_CLK2_PERIPHERAL_FREQMHZ" VALUE="133.333333"/>
        <PARAMETER NAME="PCW_PCAP_PERIPHERAL_FREQMHZ" VALUE="200"/>
        <PARAMETER NAME="PCW_TPIU_PERIPHERAL_FREQMHZ" VALUE="200"/>
        <PARAMETER NAME="PCW_FPGA0_PERIPHERAL_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_FPGA1_PERIPHERAL_FREQMHZ" VALUE="150"/>
        <PARAMETER NAME="PCW_FPGA2_PERIPHERAL_FREQMHZ" VALUE="50"/>
        <PARAMETER NAME="PCW_FPGA3_PERIPHERAL_FREQMHZ" VALUE="50"/>
        <PARAMETER NAME="PCW_ACT_APU_PERIPHERAL_FREQMHZ" VALUE="666.666687"/>
        <PARAMETER NAME="PCW_UIPARAM_ACT_DDR_FREQ_MHZ" VALUE="533.333374"/>
        <PARAMETER NAME="PCW_ACT_DCI_PERIPHERAL_FREQMHZ" VALUE="10.158730"/>
        <PARAMETER NAME="PCW_ACT_QSPI_PERIPHERAL_FREQMHZ" VALUE="200.000000"/>
        <PARAMETER NAME="PCW_ACT_SMC_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_ENET0_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_ENET1_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_USB0_PERIPHERAL_FREQMHZ" VALUE="60"/>
        <PARAMETER NAME="PCW_ACT_USB1_PERIPHERAL_FREQMHZ" VALUE="60"/>
        <PARAMETER NAME="PCW_ACT_SDIO_PERIPHERAL_FREQMHZ" VALUE="100.000000"/>
        <PARAMETER NAME="PCW_ACT_UART_PERIPHERAL_FREQMHZ" VALUE="100.000000"/>
        <PARAMETER NAME="PCW_ACT_SPI_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_CAN_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_CAN0_PERIPHERAL_FREQMHZ" VALUE="23.8095"/>
        <PARAMETER NAME="PCW_ACT_CAN1_PERIPHERAL_FREQMHZ" VALUE="23.8095"/>
        <PARAMETER NAME="PCW_ACT_I2C_PERIPHERAL_FREQMHZ" VALUE="50"/>
        <PARAMETER NAME="PCW_ACT_WDT_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_ACT_TTC_PERIPHERAL_FREQMHZ" VALUE="50"/>
        <PARAMETER NAME="PCW_ACT_PCAP_PERIPHERAL_FREQMHZ" VALUE="200.000000"/>
        <PARAMETER NAME="PCW_ACT_TPIU_PERIPHERAL_FREQMHZ" VALUE="200.000000"/>
        <PARAMETER NAME="PCW_ACT_FPGA0_PERIPHERAL_FREQMHZ" VALUE="100.000000"/>
        <PARAMETER NAME="PCW_ACT_FPGA1_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_FPGA2_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_FPGA3_PERIPHERAL_FREQMHZ" VALUE="10.000000"/>
        <PARAMETER NAME="PCW_ACT_TTC0_CLK0_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_ACT_TTC0_CLK1_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_ACT_TTC0_CLK2_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_ACT_TTC1_CLK0_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_ACT_TTC1_CLK1_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_ACT_TTC1_CLK2_PERIPHERAL_FREQMHZ" VALUE="111.111115"/>
        <PARAMETER NAME="PCW_CLK0_FREQ" VALUE="100000000"/>
        <PARAMETER NAME="PCW_CLK1_FREQ" VALUE="10000000"/>
        <PARAMETER NAME="PCW_CLK2_FREQ" VALUE="10000000"/>
        <PARAMETER NAME="PCW_CLK3_FREQ" VALUE="10000000"/>
        <PARAMETER NAME="PCW_OVERRIDE_BASIC_CLOCK" VALUE="0"/>
        <PARAMETER NAME="PCW_CPU_PERIPHERAL_DIVISOR0" VALUE="2"/>
        <PARAMETER NAME="PCW_DDR_PERIPHERAL_DIVISOR0" VALUE="2"/>
        <PARAMETER NAME="PCW_SMC_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_QSPI_PERIPHERAL_DIVISOR0" VALUE="9"/>
        <PARAMETER NAME="PCW_SDIO_PERIPHERAL_DIVISOR0" VALUE="18"/>
        <PARAMETER NAME="PCW_UART_PERIPHERAL_DIVISOR0" VALUE="18"/>
        <PARAMETER NAME="PCW_SPI_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_CAN_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_CAN_PERIPHERAL_DIVISOR1" VALUE="1"/>
        <PARAMETER NAME="PCW_FCLK0_PERIPHERAL_DIVISOR0" VALUE="6"/>
        <PARAMETER NAME="PCW_FCLK1_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_FCLK2_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_FCLK3_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_FCLK0_PERIPHERAL_DIVISOR1" VALUE="3"/>
        <PARAMETER NAME="PCW_FCLK1_PERIPHERAL_DIVISOR1" VALUE="1"/>
        <PARAMETER NAME="PCW_FCLK2_PERIPHERAL_DIVISOR1" VALUE="1"/>
        <PARAMETER NAME="PCW_FCLK3_PERIPHERAL_DIVISOR1" VALUE="1"/>
        <PARAMETER NAME="PCW_ENET0_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_ENET1_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_ENET0_PERIPHERAL_DIVISOR1" VALUE="1"/>
        <PARAMETER NAME="PCW_ENET1_PERIPHERAL_DIVISOR1" VALUE="1"/>
        <PARAMETER NAME="PCW_TPIU_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_DCI_PERIPHERAL_DIVISOR0" VALUE="15"/>
        <PARAMETER NAME="PCW_DCI_PERIPHERAL_DIVISOR1" VALUE="7"/>
        <PARAMETER NAME="PCW_PCAP_PERIPHERAL_DIVISOR0" VALUE="9"/>
        <PARAMETER NAME="PCW_TTC0_CLK0_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_TTC0_CLK1_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_TTC0_CLK2_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_TTC1_CLK0_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_TTC1_CLK1_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_TTC1_CLK2_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_WDT_PERIPHERAL_DIVISOR0" VALUE="1"/>
        <PARAMETER NAME="PCW_ARMPLL_CTRL_FBDIV" VALUE="40"/>
        <PARAMETER NAME="PCW_IOPLL_CTRL_FBDIV" VALUE="54"/>
        <PARAMETER NAME="PCW_DDRPLL_CTRL_FBDIV" VALUE="32"/>
        <PARAMETER NAME="PCW_CPU_CPU_PLL_FREQMHZ" VALUE="1333.333"/>
        <PARAMETER NAME="PCW_IO_IO_PLL_FREQMHZ" VALUE="1800.000"/>
        <PARAMETER NAME="PCW_DDR_DDR_PLL_FREQMHZ" VALUE="1066.667"/>
        <PARAMETER NAME="PCW_SMC_PERIPHERAL_VALID" VALUE="0"/>
        <PARAMETER NAME="PCW_SDIO_PERIPHERAL_VALID" VALUE="1"/>
        <PARAMETER NAME="PCW_SPI_PERIPHERAL_VALID" VALUE="0"/>
        <PARAMETER NAME="PCW_CAN_PERIPHERAL_VALID" VALUE="0"/>
        <PARAMETER NAME="PCW_UART_PERIPHERAL_VALID" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_EMIO_CAN0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_CAN1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_ENET0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_ENET1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_PTP_ENET0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_PTP_ENET1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_GPIO" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_EMIO_I2C0" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_EMIO_I2C1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_PJTAG" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_SDIO0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_CD_SDIO0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_WP_SDIO0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_SDIO1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_CD_SDIO1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_WP_SDIO1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_SPI0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_SPI1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_UART0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_UART1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_MODEM_UART0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_MODEM_UART1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_TTC0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_TTC1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_WDT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_TRACE" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_AXI_NONSECURE" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_M_AXI_GP0" VALUE="1"/>
        <PARAMETER NAME="PCW_USE_M_AXI_GP1" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_S_AXI_GP0" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_S_AXI_GP1" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_S_AXI_ACP" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_S_AXI_HP0" VALUE="1"/>
        <PARAMETER NAME="PCW_USE_S_AXI_HP1" VALUE="1"/>
        <PARAMETER NAME="PCW_USE_S_AXI_HP2" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_S_AXI_HP3" VALUE="0"/>
        <PARAMETER NAME="PCW_M_AXI_GP0_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_M_AXI_GP1_FREQMHZ" VALUE="10"/>
        <PARAMETER NAME="PCW_S_AXI_GP0_FREQMHZ" VALUE="10"/>
        <PARAMETER NAME="PCW_S_AXI_GP1_FREQMHZ" VALUE="10"/>
        <PARAMETER NAME="PCW_S_AXI_ACP_FREQMHZ" VALUE="10"/>
        <PARAMETER NAME="PCW_S_AXI_HP0_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_S_AXI_HP1_FREQMHZ" VALUE="100"/>
        <PARAMETER NAME="PCW_S_AXI_HP2_FREQMHZ" VALUE="10"/>
        <PARAMETER NAME="PCW_S_AXI_HP3_FREQMHZ" VALUE="10"/>
        <PARAMETER NAME="PCW_USE_DMA0" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_DMA1" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_DMA2" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_DMA3" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_TRACE" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_PIPELINE_WIDTH" VALUE="8"/>
        <PARAMETER NAME="PCW_INCLUDE_TRACE_BUFFER" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_BUFFER_FIFO_SIZE" VALUE="128"/>
        <PARAMETER NAME="PCW_USE_TRACE_DATA_EDGE_DETECTOR" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_BUFFER_CLOCK_DELAY" VALUE="12"/>
        <PARAMETER NAME="PCW_USE_CROSS_TRIGGER" VALUE="0"/>
        <PARAMETER NAME="PCW_FTM_CTI_IN0" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_FTM_CTI_IN1" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_FTM_CTI_IN2" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_FTM_CTI_IN3" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_FTM_CTI_OUT0" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_FTM_CTI_OUT1" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_FTM_CTI_OUT2" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_FTM_CTI_OUT3" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_USE_DEBUG" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_CR_FABRIC" VALUE="1"/>
        <PARAMETER NAME="PCW_USE_AXI_FABRIC_IDLE" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_DDR_BYPASS" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_FABRIC_INTERRUPT" VALUE="1"/>
        <PARAMETER NAME="PCW_USE_PROC_EVENT_BUS" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_EXPANDED_IOP" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_HIGH_OCM" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_PS_SLCR_REGISTERS" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_EXPANDED_PS_SLCR_REGISTERS" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_CORESIGHT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_EMIO_SRAM_INT" VALUE="0"/>
        <PARAMETER NAME="PCW_GPIO_EMIO_GPIO_WIDTH" VALUE="3"/>
        <PARAMETER NAME="PCW_GP0_NUM_WRITE_THREADS" VALUE="4"/>
        <PARAMETER NAME="PCW_GP0_NUM_READ_THREADS" VALUE="4"/>
        <PARAMETER NAME="PCW_GP1_NUM_WRITE_THREADS" VALUE="4"/>
        <PARAMETER NAME="PCW_GP1_NUM_READ_THREADS" VALUE="4"/>
        <PARAMETER NAME="PCW_UART0_BAUD_RATE" VALUE="115200"/>
        <PARAMETER NAME="PCW_UART1_BAUD_RATE" VALUE="115200"/>
        <PARAMETER NAME="PCW_EN_4K_TIMER" VALUE="0"/>
        <PARAMETER NAME="PCW_M_AXI_GP0_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="PCW_M_AXI_GP0_ENABLE_STATIC_REMAP" VALUE="0"/>
        <PARAMETER NAME="PCW_M_AXI_GP0_SUPPORT_NARROW_BURST" VALUE="0"/>
        <PARAMETER NAME="PCW_M_AXI_GP0_THREAD_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="PCW_M_AXI_GP1_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="PCW_M_AXI_GP1_ENABLE_STATIC_REMAP" VALUE="0"/>
        <PARAMETER NAME="PCW_M_AXI_GP1_SUPPORT_NARROW_BURST" VALUE="0"/>
        <PARAMETER NAME="PCW_M_AXI_GP1_THREAD_ID_WIDTH" VALUE="12"/>
        <PARAMETER NAME="PCW_S_AXI_GP0_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="PCW_S_AXI_GP1_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="PCW_S_AXI_ACP_ID_WIDTH" VALUE="3"/>
        <PARAMETER NAME="PCW_INCLUDE_ACP_TRANS_CHECK" VALUE="0"/>
        <PARAMETER NAME="PCW_USE_DEFAULT_ACP_USER_VAL" VALUE="0"/>
        <PARAMETER NAME="PCW_S_AXI_ACP_ARUSER_VAL" VALUE="31"/>
        <PARAMETER NAME="PCW_S_AXI_ACP_AWUSER_VAL" VALUE="31"/>
        <PARAMETER NAME="PCW_S_AXI_HP0_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="PCW_S_AXI_HP0_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="PCW_S_AXI_HP1_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="PCW_S_AXI_HP1_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="PCW_S_AXI_HP2_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="PCW_S_AXI_HP2_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="PCW_S_AXI_HP3_ID_WIDTH" VALUE="6"/>
        <PARAMETER NAME="PCW_S_AXI_HP3_DATA_WIDTH" VALUE="64"/>
        <PARAMETER NAME="PCW_NUM_F2P_INTR_INPUTS" VALUE="2"/>
        <PARAMETER NAME="PCW_EN_DDR" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_SMC" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_QSPI" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_CAN0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_CAN1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_ENET0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_ENET1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_GPIO" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_I2C0" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_I2C1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_PJTAG" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_SDIO0" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_SDIO1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_SPI0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_SPI1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_UART0" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_UART1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_MODEM_UART0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_MODEM_UART1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_TTC0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_TTC1" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_WDT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_TRACE" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_USB0" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_USB1" VALUE="0"/>
        <PARAMETER NAME="PCW_DQ_WIDTH" VALUE="32"/>
        <PARAMETER NAME="PCW_DQS_WIDTH" VALUE="4"/>
        <PARAMETER NAME="PCW_DM_WIDTH" VALUE="4"/>
        <PARAMETER NAME="PCW_MIO_PRIMITIVE" VALUE="54"/>
        <PARAMETER NAME="PCW_EN_CLK0_PORT" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_CLK1_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_CLK2_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_CLK3_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_RST0_PORT" VALUE="1"/>
        <PARAMETER NAME="PCW_EN_RST1_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_RST2_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_RST3_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_CLKTRIG0_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_CLKTRIG1_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_CLKTRIG2_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_EN_CLKTRIG3_PORT" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC_ABORT_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC2_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC3_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC4_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC5_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC6_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_DMAC7_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_SMC_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_QSPI_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_CTI_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_GPIO_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_USB0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_ENET0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_SDIO0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_I2C0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_SPI0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_UART0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_CAN0_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_USB1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_ENET1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_SDIO1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_I2C1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_SPI1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_UART1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_P2F_CAN1_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_IRQ_F2P_INTR" VALUE="1"/>
        <PARAMETER NAME="PCW_IRQ_F2P_MODE" VALUE="DIRECT"/>
        <PARAMETER NAME="PCW_CORE0_FIQ_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_CORE0_IRQ_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_CORE1_FIQ_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_CORE1_IRQ_INTR" VALUE="0"/>
        <PARAMETER NAME="PCW_VALUE_SILVERSION" VALUE="3"/>
        <PARAMETER NAME="PCW_GP0_EN_MODIFIABLE_TXN" VALUE="1"/>
        <PARAMETER NAME="PCW_GP1_EN_MODIFIABLE_TXN" VALUE="1"/>
        <PARAMETER NAME="PCW_IMPORT_BOARD_PRESET" VALUE="None"/>
        <PARAMETER NAME="PCW_PERIPHERAL_BOARD_PRESET" VALUE="None"/>
        <PARAMETER NAME="PCW_PRESET_BANK0_VOLTAGE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_PRESET_BANK1_VOLTAGE" VALUE="LVCMOS 1.8V"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_ADV_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_MEMORY_TYPE" VALUE="DDR 3"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_ECC" VALUE="Disabled"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_BUS_WIDTH" VALUE="32 Bit"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_BL" VALUE="8"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_HIGH_TEMP" VALUE="Normal (0-85)"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_PARTNO" VALUE="MT41J256M16 RE-125"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DRAM_WIDTH" VALUE="16 Bits"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_DEVICE_CAPACITY" VALUE="4096 MBits"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_SPEED_BIN" VALUE="DDR3_1066F"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_TRAIN_WRITE_LEVEL" VALUE="1"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_TRAIN_READ_GATE" VALUE="1"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_TRAIN_DATA_EYE" VALUE="1"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_CLOCK_STOP_EN" VALUE="0"/>
        <PARAMETER NAME="PCW_UIPARAM_DDR_USE_INTERNAL_VREF" VALUE="0"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_0" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_1" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_2" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_WRITEPORT_3" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_0" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_1" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_2" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PRIORITY_READPORT_3" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DDR_PORT0_HPR_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_DDR_PORT1_HPR_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_DDR_PORT2_HPR_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_DDR_PORT3_HPR_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_DDR_HPRLPR_QUEUE_PARTITION" VALUE="HPR(0)/LPR(32)"/>
        <PARAMETER NAME="PCW_DDR_LPR_TO_CRITICAL_PRIORITY_LEVEL" VALUE="2"/>
        <PARAMETER NAME="PCW_DDR_HPR_TO_CRITICAL_PRIORITY_LEVEL" VALUE="15"/>
        <PARAMETER NAME="PCW_DDR_WRITE_TO_CRITICAL_PRIORITY_LEVEL" VALUE="2"/>
        <PARAMETER NAME="PCW_NAND_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NAND_NAND_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NAND_GRP_D8_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NAND_GRP_D8_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NOR_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_NOR_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NOR_GRP_A25_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_GRP_A25_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NOR_GRP_CS0_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_GRP_CS0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS0_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NOR_GRP_CS1_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_GRP_CS1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS1_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_GRP_SRAM_CS1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_NOR_GRP_SRAM_INT_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_GRP_SRAM_INT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_QSPI_PERIPHERAL_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_QSPI_QSPI_IO" VALUE="MIO 1 .. 6"/>
        <PARAMETER NAME="PCW_QSPI_GRP_SINGLE_SS_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_QSPI_GRP_SINGLE_SS_IO" VALUE="MIO 1 .. 6"/>
        <PARAMETER NAME="PCW_QSPI_GRP_SS1_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_QSPI_GRP_SS1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SINGLE_QSPI_DATA_MODE" VALUE="x4"/>
        <PARAMETER NAME="PCW_DUAL_STACK_QSPI_DATA_MODE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_DUAL_PARALLEL_QSPI_DATA_MODE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_QSPI_GRP_IO1_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_QSPI_GRP_IO1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_QSPI_GRP_FBCLK_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_QSPI_GRP_FBCLK_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_QSPI_INTERNAL_HIGHADDRESS" VALUE="0xFCFFFFFF"/>
        <PARAMETER NAME="PCW_ENET0_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_ENET0_ENET0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_ENET0_GRP_MDIO_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_ENET0_GRP_MDIO_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_ENET_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_ENET_RESET_SELECT" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_ENET0_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_ENET0_RESET_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_ENET1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_ENET1_ENET1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_ENET1_GRP_MDIO_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_ENET1_GRP_MDIO_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_ENET1_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_ENET1_RESET_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SD0_PERIPHERAL_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_SD0_SD0_IO" VALUE="MIO 40 .. 45"/>
        <PARAMETER NAME="PCW_SD0_GRP_CD_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_SD0_GRP_CD_IO" VALUE="MIO 10"/>
        <PARAMETER NAME="PCW_SD0_GRP_WP_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SD0_GRP_WP_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SD0_GRP_POW_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SD0_GRP_POW_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SD1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SD1_SD1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SD1_GRP_CD_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SD1_GRP_CD_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SD1_GRP_WP_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SD1_GRP_WP_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SD1_GRP_POW_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SD1_GRP_POW_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_UART0_PERIPHERAL_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_UART0_UART0_IO" VALUE="MIO 14 .. 15"/>
        <PARAMETER NAME="PCW_UART0_GRP_FULL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_UART0_GRP_FULL_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_UART1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_UART1_UART1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_UART1_GRP_FULL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_UART1_GRP_FULL_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI0_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI0_SPI0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI0_GRP_SS0_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI0_GRP_SS0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI0_GRP_SS1_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI0_GRP_SS1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI0_GRP_SS2_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI0_GRP_SS2_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI1_SPI1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI1_GRP_SS0_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI1_GRP_SS0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI1_GRP_SS1_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI1_GRP_SS1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_SPI1_GRP_SS2_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_SPI1_GRP_SS2_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_CAN0_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_CAN0_CAN0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_CAN0_GRP_CLK_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_CAN0_GRP_CLK_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_CAN1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_CAN1_CAN1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_CAN1_GRP_CLK_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_CAN1_GRP_CLK_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TRACE_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_TRACE_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TRACE_GRP_2BIT_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_GRP_2BIT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TRACE_GRP_4BIT_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_GRP_4BIT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TRACE_GRP_8BIT_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_GRP_8BIT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TRACE_GRP_16BIT_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_GRP_16BIT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TRACE_GRP_32BIT_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TRACE_GRP_32BIT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TRACE_INTERNAL_WIDTH" VALUE="2"/>
        <PARAMETER NAME="PCW_WDT_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_WDT_WDT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TTC0_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TTC0_TTC0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_TTC1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_TTC1_TTC1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_PJTAG_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_PJTAG_PJTAG_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_USB0_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_USB0_USB0_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_USB_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_USB_RESET_SELECT" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_USB0_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_USB0_RESET_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_USB1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_USB1_USB1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_USB1_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_USB1_RESET_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_I2C0_PERIPHERAL_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_I2C0_I2C0_IO" VALUE="EMIO"/>
        <PARAMETER NAME="PCW_I2C0_GRP_INT_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_I2C0_GRP_INT_IO" VALUE="EMIO"/>
        <PARAMETER NAME="PCW_I2C0_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_I2C0_RESET_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_I2C1_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_I2C1_I2C1_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_I2C1_GRP_INT_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_I2C1_GRP_INT_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_I2C_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_I2C_RESET_SELECT" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_I2C1_RESET_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_I2C1_RESET_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_GPIO_PERIPHERAL_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_GPIO_MIO_GPIO_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_GPIO_MIO_GPIO_IO" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_GPIO_EMIO_GPIO_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_GPIO_EMIO_GPIO_IO" VALUE="3"/>
        <PARAMETER NAME="PCW_APU_CLK_RATIO_ENABLE" VALUE="6:2:1"/>
        <PARAMETER NAME="PCW_ENET0_PERIPHERAL_FREQMHZ" VALUE="1000 Mbps"/>
        <PARAMETER NAME="PCW_ENET1_PERIPHERAL_FREQMHZ" VALUE="1000 Mbps"/>
        <PARAMETER NAME="PCW_CPU_PERIPHERAL_CLKSRC" VALUE="ARM PLL"/>
        <PARAMETER NAME="PCW_DDR_PERIPHERAL_CLKSRC" VALUE="DDR PLL"/>
        <PARAMETER NAME="PCW_SMC_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_QSPI_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_SDIO_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_UART_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_SPI_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_CAN_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_FCLK0_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_FCLK1_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_FCLK2_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_FCLK3_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_ENET0_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_ENET1_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_CAN0_PERIPHERAL_CLKSRC" VALUE="External"/>
        <PARAMETER NAME="PCW_CAN1_PERIPHERAL_CLKSRC" VALUE="External"/>
        <PARAMETER NAME="PCW_TPIU_PERIPHERAL_CLKSRC" VALUE="External"/>
        <PARAMETER NAME="PCW_TTC0_CLK0_PERIPHERAL_CLKSRC" VALUE="CPU_1X"/>
        <PARAMETER NAME="PCW_TTC0_CLK1_PERIPHERAL_CLKSRC" VALUE="CPU_1X"/>
        <PARAMETER NAME="PCW_TTC0_CLK2_PERIPHERAL_CLKSRC" VALUE="CPU_1X"/>
        <PARAMETER NAME="PCW_TTC1_CLK0_PERIPHERAL_CLKSRC" VALUE="CPU_1X"/>
        <PARAMETER NAME="PCW_TTC1_CLK1_PERIPHERAL_CLKSRC" VALUE="CPU_1X"/>
        <PARAMETER NAME="PCW_TTC1_CLK2_PERIPHERAL_CLKSRC" VALUE="CPU_1X"/>
        <PARAMETER NAME="PCW_WDT_PERIPHERAL_CLKSRC" VALUE="CPU_1X"/>
        <PARAMETER NAME="PCW_DCI_PERIPHERAL_CLKSRC" VALUE="DDR PLL"/>
        <PARAMETER NAME="PCW_PCAP_PERIPHERAL_CLKSRC" VALUE="IO PLL"/>
        <PARAMETER NAME="PCW_USB_RESET_POLARITY" VALUE="Active Low"/>
        <PARAMETER NAME="PCW_ENET_RESET_POLARITY" VALUE="Active Low"/>
        <PARAMETER NAME="PCW_I2C_RESET_POLARITY" VALUE="Active Low"/>
        <PARAMETER NAME="PCW_MIO_0_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_0_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_0_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_0_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_1_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_1_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_1_DIRECTION" VALUE="out"/>
        <PARAMETER NAME="PCW_MIO_1_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_2_PULLUP" VALUE="disabled"/>
        <PARAMETER NAME="PCW_MIO_2_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_2_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_2_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_3_PULLUP" VALUE="disabled"/>
        <PARAMETER NAME="PCW_MIO_3_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_3_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_3_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_4_PULLUP" VALUE="disabled"/>
        <PARAMETER NAME="PCW_MIO_4_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_4_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_4_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_5_PULLUP" VALUE="disabled"/>
        <PARAMETER NAME="PCW_MIO_5_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_5_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_5_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_6_PULLUP" VALUE="disabled"/>
        <PARAMETER NAME="PCW_MIO_6_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_6_DIRECTION" VALUE="out"/>
        <PARAMETER NAME="PCW_MIO_6_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_7_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_7_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_7_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_7_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_8_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_8_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_8_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_8_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_9_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_9_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_9_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_9_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_10_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_10_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_10_DIRECTION" VALUE="in"/>
        <PARAMETER NAME="PCW_MIO_10_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_11_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_11_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_11_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_11_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_12_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_12_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_12_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_12_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_13_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_13_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_13_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_13_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_14_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_14_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_14_DIRECTION" VALUE="in"/>
        <PARAMETER NAME="PCW_MIO_14_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_15_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_15_IOTYPE" VALUE="LVCMOS 3.3V"/>
        <PARAMETER NAME="PCW_MIO_15_DIRECTION" VALUE="out"/>
        <PARAMETER NAME="PCW_MIO_15_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_16_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_16_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_16_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_16_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_17_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_17_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_17_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_17_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_18_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_18_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_18_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_18_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_19_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_19_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_19_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_19_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_20_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_20_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_20_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_20_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_21_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_21_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_21_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_21_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_22_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_22_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_22_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_22_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_23_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_23_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_23_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_23_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_24_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_24_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_24_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_24_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_25_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_25_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_25_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_25_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_26_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_26_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_26_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_26_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_27_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_27_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_27_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_27_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_28_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_28_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_28_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_28_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_29_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_29_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_29_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_29_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_30_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_30_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_30_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_30_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_31_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_31_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_31_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_31_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_32_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_32_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_32_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_32_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_33_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_33_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_33_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_33_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_34_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_34_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_34_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_34_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_35_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_35_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_35_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_35_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_36_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_36_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_36_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_36_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_37_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_37_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_37_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_37_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_38_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_38_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_38_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_38_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_39_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_39_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_39_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_39_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_40_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_40_IOTYPE" VALUE="LVCMOS 1.8V"/>
        <PARAMETER NAME="PCW_MIO_40_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_40_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_41_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_41_IOTYPE" VALUE="LVCMOS 1.8V"/>
        <PARAMETER NAME="PCW_MIO_41_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_41_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_42_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_42_IOTYPE" VALUE="LVCMOS 1.8V"/>
        <PARAMETER NAME="PCW_MIO_42_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_42_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_43_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_43_IOTYPE" VALUE="LVCMOS 1.8V"/>
        <PARAMETER NAME="PCW_MIO_43_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_43_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_44_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_44_IOTYPE" VALUE="LVCMOS 1.8V"/>
        <PARAMETER NAME="PCW_MIO_44_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_44_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_45_PULLUP" VALUE="enabled"/>
        <PARAMETER NAME="PCW_MIO_45_IOTYPE" VALUE="LVCMOS 1.8V"/>
        <PARAMETER NAME="PCW_MIO_45_DIRECTION" VALUE="inout"/>
        <PARAMETER NAME="PCW_MIO_45_SLEW" VALUE="slow"/>
        <PARAMETER NAME="PCW_MIO_46_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_46_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_46_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_46_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_47_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_47_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_47_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_47_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_48_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_48_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_48_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_48_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_49_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_49_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_49_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_49_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_50_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_50_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_50_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_50_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_51_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_51_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_51_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_51_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_52_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_52_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_52_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_52_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_53_PULLUP" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_53_IOTYPE" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_53_DIRECTION" VALUE="&lt;Select>"/>
        <PARAMETER NAME="PCW_MIO_53_SLEW" VALUE="&lt;Select>"/>
        <PARAMETER NAME="preset" VALUE="None"/>
        <PARAMETER NAME="PCW_UIPARAM_GENERATE_SUMMARY" VALUE="NA"/>
        <PARAMETER NAME="PCW_MIO_TREE_PERIPHERALS" VALUE="unassigned#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#Quad SPI Flash#unassigned#unassigned#unassigned#SD 0#unassigned#unassigned#unassigned#UART 0#UART 0#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#SD 0#SD 0#SD 0#SD 0#SD 0#SD 0#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned"/>
        <PARAMETER NAME="PCW_MIO_TREE_SIGNALS" VALUE="unassigned#qspi0_ss_b#qspi0_io[0]#qspi0_io[1]#qspi0_io[2]#qspi0_io[3]/HOLD_B#qspi0_sclk#unassigned#unassigned#unassigned#cd#unassigned#unassigned#unassigned#rx#tx#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#clk#cmd#data[0]#data[1]#data[2]#data[3]#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned#unassigned"/>
        <PARAMETER NAME="PCW_PS7_SI_REV" VALUE="PRODUCTION"/>
        <PARAMETER NAME="PCW_FPGA_FCLK0_ENABLE" VALUE="1"/>
        <PARAMETER NAME="PCW_FPGA_FCLK1_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_FPGA_FCLK2_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_FPGA_FCLK3_ENABLE" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_TR" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_PC" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_WP" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_CEOE" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_WC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS0_T_RC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS0_WE_TIME" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_TR" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_PC" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_WP" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_CEOE" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_WC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS1_T_RC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_SRAM_CS1_WE_TIME" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_CS0_T_TR" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS0_T_PC" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS0_T_WP" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS0_T_CEOE" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS0_T_WC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_CS0_T_RC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_CS0_WE_TIME" VALUE="0"/>
        <PARAMETER NAME="PCW_NOR_CS1_T_TR" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS1_T_PC" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS1_T_WP" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS1_T_CEOE" VALUE="1"/>
        <PARAMETER NAME="PCW_NOR_CS1_T_WC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_CS1_T_RC" VALUE="11"/>
        <PARAMETER NAME="PCW_NOR_CS1_WE_TIME" VALUE="0"/>
        <PARAMETER NAME="PCW_NAND_CYCLES_T_RR" VALUE="1"/>
        <PARAMETER NAME="PCW_NAND_CYCLES_T_AR" VALUE="1"/>
        <PARAMETER NAME="PCW_NAND_CYCLES_T_CLR" VALUE="1"/>
        <PARAMETER NAME="PCW_NAND_CYCLES_T_WP" VALUE="1"/>
        <PARAMETER NAME="PCW_NAND_CYCLES_T_REA" VALUE="1"/>
        <PARAMETER NAME="PCW_NAND_CYCLES_T_WC" VALUE="11"/>
        <PARAMETER NAME="PCW_NAND_CYCLES_T_RC" VALUE="11"/>
        <PARAMETER NAME="PCW_SMC_CYCLE_T0" VALUE="NA"/>
        <PARAMETER NAME="PCW_SMC_CYCLE_T1" VALUE="NA"/>
        <PARAMETER NAME="PCW_SMC_CYCLE_T2" VALUE="NA"/>
        <PARAMETER NAME="PCW_SMC_CYCLE_T3" VALUE="NA"/>
        <PARAMETER NAME="PCW_SMC_CYCLE_T4" VALUE="NA"/>
        <PARAMETER NAME="PCW_SMC_CYCLE_T5" VALUE="NA"/>
        <PARAMETER NAME="PCW_SMC_CYCLE_T6" VALUE="NA"/>
        <PARAMETER NAME="PCW_PACKAGE_NAME" VALUE="clg400"/>
        <PARAMETER NAME="PCW_PLL_BYPASSMODE_ENABLE" VALUE="0"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_processing_system7_0_0"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
        <PARAMETER NAME="C_BASEADDR" VALUE="0x00000000"/>
        <PARAMETER NAME="C_HIGHADDR" VALUE="0x0FFFFFFF"/>
      </PARAMETERS>
      <PORTS>
        <PORT DIR="I" LEFT="2" NAME="GPIO_I" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_GPIO_I">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="gpio0_tri_i"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="GPIO_O" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_GPIO_O">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="gpio0_tri_o"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="GPIO_T" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_GPIO_T">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="gpio0_tri_t"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="I2C0_SDA_I" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SDA_I">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="i2c0_sda_i"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="I2C0_SDA_O" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SDA_O">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="i2c0_sda_o"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="I2C0_SDA_T" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SDA_T">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="i2c0_sda_t"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="I2C0_SCL_I" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SCL_I">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="i2c0_scl_i"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="I2C0_SCL_O" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SCL_O">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="i2c0_scl_o"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="I2C0_SCL_T" SIGIS="undef" SIGNAME="processing_system7_0_I2C0_SCL_T">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="i2c0_scl_t"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M_AXI_GP0_ARVALID" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M_AXI_GP0_AWVALID" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M_AXI_GP0_BREADY" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M_AXI_GP0_RREADY" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M_AXI_GP0_WLAST" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WLAST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_wlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M_AXI_GP0_WVALID" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="11" NAME="M_AXI_GP0_ARID" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="11" NAME="M_AXI_GP0_AWID" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="11" NAME="M_AXI_GP0_WID" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_wid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M_AXI_GP0_ARBURST" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARBURST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M_AXI_GP0_ARLOCK" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARLOCK">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M_AXI_GP0_ARSIZE" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARSIZE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M_AXI_GP0_AWBURST" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWBURST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="M_AXI_GP0_AWLOCK" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWLOCK">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M_AXI_GP0_AWSIZE" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWSIZE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M_AXI_GP0_ARPROT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARPROT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M_AXI_GP0_AWPROT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWPROT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M_AXI_GP0_ARADDR" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARADDR">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M_AXI_GP0_AWADDR" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWADDR">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M_AXI_GP0_WDATA" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WDATA">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M_AXI_GP0_ARCACHE" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARCACHE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M_AXI_GP0_ARLEN" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARLEN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M_AXI_GP0_ARQOS" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARQOS">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M_AXI_GP0_AWCACHE" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWCACHE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M_AXI_GP0_AWLEN" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWLEN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M_AXI_GP0_AWQOS" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWQOS">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M_AXI_GP0_WSTRB" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WSTRB">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="M_AXI_GP0_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M_AXI_GP0_ARREADY" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M_AXI_GP0_AWREADY" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M_AXI_GP0_BVALID" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M_AXI_GP0_RLAST" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RLAST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_rlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M_AXI_GP0_RVALID" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M_AXI_GP0_WREADY" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="11" NAME="M_AXI_GP0_BID" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_bid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="11" NAME="M_AXI_GP0_RID" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_rid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M_AXI_GP0_BRESP" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BRESP">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M_AXI_GP0_RRESP" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RRESP">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="M_AXI_GP0_RDATA" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RDATA">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP0_ARREADY" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP0_AWREADY" SIGIS="undef"/>
        <PORT DIR="O" NAME="S_AXI_HP0_BVALID" SIGIS="undef"/>
        <PORT DIR="O" NAME="S_AXI_HP0_RLAST" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_rlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP0_RVALID" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP0_WREADY" SIGIS="undef"/>
        <PORT DIR="O" LEFT="1" NAME="S_AXI_HP0_BRESP" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="1" NAME="S_AXI_HP0_RRESP" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="5" NAME="S_AXI_HP0_BID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="5" NAME="S_AXI_HP0_RID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="63" NAME="S_AXI_HP0_RDATA" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="S_AXI_HP0_RCOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_RCOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP0_FIFO_CTRL_0_rcount"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="S_AXI_HP0_WCOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_WCOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP0_FIFO_CTRL_0_wcount"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="S_AXI_HP0_RACOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_RACOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP0_FIFO_CTRL_0_racount"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="5" NAME="S_AXI_HP0_WACOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_WACOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP0_FIFO_CTRL_0_wacount"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="S_AXI_HP0_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP0_ARVALID" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP0_AWVALID" SIGIS="undef"/>
        <PORT DIR="I" NAME="S_AXI_HP0_BREADY" SIGIS="undef"/>
        <PORT DIR="I" NAME="S_AXI_HP0_RDISSUECAP1_EN" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_RDISSUECAP1_EN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP0_FIFO_CTRL_0_rdissuecapen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP0_RREADY" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP0_WLAST" SIGIS="undef"/>
        <PORT DIR="I" NAME="S_AXI_HP0_WRISSUECAP1_EN" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP0_WRISSUECAP1_EN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP0_FIFO_CTRL_0_wrissuecapen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP0_WVALID" SIGIS="undef"/>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP0_ARBURST" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP0_ARLOCK" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP0_ARSIZE" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP0_AWBURST" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP0_AWLOCK" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP0_AWSIZE" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP0_ARPROT" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP0_AWPROT" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="31" NAME="S_AXI_HP0_ARADDR" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S_AXI_HP0_AWADDR" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP0_ARCACHE" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP0_ARLEN" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP0_ARQOS" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc_M00_AXI_arqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc" PORT="M00_AXI_arqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP0_AWCACHE" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP0_AWLEN" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP0_AWQOS" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="5" NAME="S_AXI_HP0_ARID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="5" NAME="S_AXI_HP0_AWID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="5" NAME="S_AXI_HP0_WID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="63" NAME="S_AXI_HP0_WDATA" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="7" NAME="S_AXI_HP0_WSTRB" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" NAME="S_AXI_HP1_ARREADY" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP1_AWREADY" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP1_BVALID" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP1_RLAST" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_rlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP1_RVALID" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S_AXI_HP1_WREADY" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="S_AXI_HP1_BRESP" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="S_AXI_HP1_RRESP" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="5" NAME="S_AXI_HP1_BID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="5" NAME="S_AXI_HP1_RID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="63" NAME="S_AXI_HP1_RDATA" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="S_AXI_HP1_RCOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_RCOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP1_FIFO_CTRL_0_rcount"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="7" NAME="S_AXI_HP1_WCOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_WCOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP1_FIFO_CTRL_0_wcount"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="S_AXI_HP1_RACOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_RACOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP1_FIFO_CTRL_0_racount"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="5" NAME="S_AXI_HP1_WACOUNT" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_WACOUNT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP1_FIFO_CTRL_0_wacount"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="S_AXI_HP1_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_ARVALID" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_AWVALID" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_BREADY" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_RDISSUECAP1_EN" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_RDISSUECAP1_EN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP1_FIFO_CTRL_0_rdissuecapen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_RREADY" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_WLAST" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_wlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_WRISSUECAP1_EN" SIGIS="undef" SIGNAME="processing_system7_0_S_AXI_HP1_WRISSUECAP1_EN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="S_AXI_HP1_FIFO_CTRL_0_wrissuecapen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S_AXI_HP1_WVALID" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP1_ARBURST" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP1_ARLOCK" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP1_ARSIZE" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP1_AWBURST" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awburst">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awburst"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S_AXI_HP1_AWLOCK" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awlock">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awlock"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP1_AWSIZE" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awsize">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awsize"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP1_ARPROT" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S_AXI_HP1_AWPROT" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S_AXI_HP1_ARADDR" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S_AXI_HP1_AWADDR" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP1_ARCACHE" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP1_ARLEN" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP1_ARQOS" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_arqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_arqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP1_AWCACHE" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awcache">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awcache"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP1_AWLEN" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awlen">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awlen"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S_AXI_HP1_AWQOS" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_awqos">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_awqos"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="5" NAME="S_AXI_HP1_ARID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="5" NAME="S_AXI_HP1_AWID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="5" NAME="S_AXI_HP1_WID" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="I" LEFT="63" NAME="S_AXI_HP1_WDATA" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="7" NAME="S_AXI_HP1_WSTRB" RIGHT="0" SIGIS="undef" SIGNAME="axi_smc1_M00_AXI_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_smc1" PORT="M00_AXI_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="IRQ_F2P" RIGHT="0" SENSITIVITY="NULL:LEVEL_HIGH" SIGIS="INTERRUPT" SIGNAME="xlconcat_0_dout">
          <CONNECTIONS>
            <CONNECTION INSTANCE="xlconcat_0" PORT="dout"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="O" NAME="FCLK_CLK0" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ACLK"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_ACLK"/>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="slowest_sync_clk"/>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_aclk"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_ACLK"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="ACLK"/>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_aclk"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_ACLK"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_ACLK"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_ACLK"/>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axi_mm2s_aclk"/>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axis_mm2s_aclk"/>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="aclk"/>
            <CONNECTION INSTANCE="axi_smc" PORT="aclk"/>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP0_ACLK"/>
            <CONNECTION INSTANCE="processing_system7_0" PORT="S_AXI_HP1_ACLK"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_ACLK"/>
            <CONNECTION INSTANCE="axi_smc1" PORT="aclk"/>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="REF_CLK_I"/>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_aclk"/>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_aclk"/>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_aclk"/>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_aclk"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="FCLK_RESET0_N" SIGIS="rst" SIGNAME="processing_system7_0_FCLK_RESET0_N">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="ext_reset_in"/>
            <CONNECTION INSTANCE="rst_ps7_0_100M1" PORT="ext_reset_in"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" LEFT="53" NAME="MIO" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_MIO">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="FIXED_IO_mio"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_CAS_n" SIGIS="undef" SIGNAME="processing_system7_0_DDR_CAS_n">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_cas_n"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_CKE" SIGIS="undef" SIGNAME="processing_system7_0_DDR_CKE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_cke"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_Clk_n" SIGIS="clk" SIGNAME="processing_system7_0_DDR_Clk_n">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_ck_n"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_Clk" SIGIS="clk" SIGNAME="processing_system7_0_DDR_Clk">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_ck_p"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_CS_n" SIGIS="undef" SIGNAME="processing_system7_0_DDR_CS_n">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_cs_n"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_DRSTB" SIGIS="rst" SIGNAME="processing_system7_0_DDR_DRSTB">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_reset_n"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_ODT" SIGIS="undef" SIGNAME="processing_system7_0_DDR_ODT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_odt"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_RAS_n" SIGIS="undef" SIGNAME="processing_system7_0_DDR_RAS_n">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_ras_n"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_WEB" SIGIS="undef" SIGNAME="processing_system7_0_DDR_WEB">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_we_n"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" LEFT="2" NAME="DDR_BankAddr" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_BankAddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_ba"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" LEFT="14" NAME="DDR_Addr" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_Addr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_addr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_VRN" SIGIS="undef" SIGNAME="processing_system7_0_DDR_VRN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="FIXED_IO_ddr_vrn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="DDR_VRP" SIGIS="undef" SIGNAME="processing_system7_0_DDR_VRP">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="FIXED_IO_ddr_vrp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" LEFT="3" NAME="DDR_DM" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DM">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_dm"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" LEFT="31" NAME="DDR_DQ" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DQ">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_dq"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" LEFT="3" NAME="DDR_DQS_n" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DQS_n">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_dqs_n"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" LEFT="3" NAME="DDR_DQS" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_DDR_DQS">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="DDR_dqs_p"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="PS_SRSTB" SIGIS="undef" SIGNAME="processing_system7_0_PS_SRSTB">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="FIXED_IO_ps_srstb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="PS_CLK" SIGIS="undef" SIGNAME="processing_system7_0_PS_CLK">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="FIXED_IO_ps_clk"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="IO" NAME="PS_PORB" SIGIS="undef" SIGNAME="processing_system7_0_PS_PORB">
          <CONNECTIONS>
            <CONNECTION INSTANCE="utg_imp" PORT="FIXED_IO_ps_porb"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="processing_system7_0_GPIO_0" NAME="GPIO_0" TYPE="INITIATOR" VLNV="xilinx.com:interface:gpio:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="TRI_I" PHYSICAL="GPIO_I"/>
            <PORTMAP LOGICAL="TRI_O" PHYSICAL="GPIO_O"/>
            <PORTMAP LOGICAL="TRI_T" PHYSICAL="GPIO_T"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="processing_system7_0_DDR" DATAWIDTH="8" NAME="DDR" TYPE="INITIATOR" VLNV="xilinx.com:interface:ddrx:1.0">
          <PARAMETER NAME="CAN_DEBUG" VALUE="false"/>
          <PARAMETER NAME="TIMEPERIOD_PS" VALUE="1250"/>
          <PARAMETER NAME="MEMORY_TYPE" VALUE="COMPONENTS"/>
          <PARAMETER NAME="MEMORY_PART"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="8"/>
          <PARAMETER NAME="CS_ENABLED" VALUE="true"/>
          <PARAMETER NAME="DATA_MASK_ENABLED" VALUE="true"/>
          <PARAMETER NAME="SLOT" VALUE="Single"/>
          <PARAMETER NAME="CUSTOM_PARTS"/>
          <PARAMETER NAME="MEM_ADDR_MAP" VALUE="ROW_COLUMN_BANK"/>
          <PARAMETER NAME="BURST_LENGTH" VALUE="8"/>
          <PARAMETER NAME="AXI_ARBITRATION_SCHEME" VALUE="TDM"/>
          <PARAMETER NAME="CAS_LATENCY" VALUE="11"/>
          <PARAMETER NAME="CAS_WRITE_LATENCY" VALUE="11"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="CAS_N" PHYSICAL="DDR_CAS_n"/>
            <PORTMAP LOGICAL="CKE" PHYSICAL="DDR_CKE"/>
            <PORTMAP LOGICAL="CK_N" PHYSICAL="DDR_Clk_n"/>
            <PORTMAP LOGICAL="CK_P" PHYSICAL="DDR_Clk"/>
            <PORTMAP LOGICAL="CS_N" PHYSICAL="DDR_CS_n"/>
            <PORTMAP LOGICAL="RESET_N" PHYSICAL="DDR_DRSTB"/>
            <PORTMAP LOGICAL="ODT" PHYSICAL="DDR_ODT"/>
            <PORTMAP LOGICAL="RAS_N" PHYSICAL="DDR_RAS_n"/>
            <PORTMAP LOGICAL="WE_N" PHYSICAL="DDR_WEB"/>
            <PORTMAP LOGICAL="BA" PHYSICAL="DDR_BankAddr"/>
            <PORTMAP LOGICAL="ADDR" PHYSICAL="DDR_Addr"/>
            <PORTMAP LOGICAL="DM" PHYSICAL="DDR_DM"/>
            <PORTMAP LOGICAL="DQ" PHYSICAL="DDR_DQ"/>
            <PORTMAP LOGICAL="DQS_N" PHYSICAL="DDR_DQS_n"/>
            <PORTMAP LOGICAL="DQS_P" PHYSICAL="DDR_DQS"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="processing_system7_0_FIXED_IO" NAME="FIXED_IO" TYPE="INITIATOR" VLNV="xilinx.com:display_processing_system7:fixedio:1.0">
          <PARAMETER NAME="CAN_DEBUG" VALUE="false"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="MIO" PHYSICAL="MIO"/>
            <PORTMAP LOGICAL="DDR_VRN" PHYSICAL="DDR_VRN"/>
            <PORTMAP LOGICAL="DDR_VRP" PHYSICAL="DDR_VRP"/>
            <PORTMAP LOGICAL="PS_SRSTB" PHYSICAL="PS_SRSTB"/>
            <PORTMAP LOGICAL="PS_CLK" PHYSICAL="PS_CLK"/>
            <PORTMAP LOGICAL="PS_PORB" PHYSICAL="PS_PORB"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="processing_system7_0_IIC_0" NAME="IIC_0" TYPE="INITIATOR" VLNV="xilinx.com:interface:iic:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="SDA_I" PHYSICAL="I2C0_SDA_I"/>
            <PORTMAP LOGICAL="SDA_O" PHYSICAL="I2C0_SDA_O"/>
            <PORTMAP LOGICAL="SDA_T" PHYSICAL="I2C0_SDA_T"/>
            <PORTMAP LOGICAL="SCL_I" PHYSICAL="I2C0_SCL_I"/>
            <PORTMAP LOGICAL="SCL_O" PHYSICAL="I2C0_SCL_O"/>
            <PORTMAP LOGICAL="SCL_T" PHYSICAL="I2C0_SCL_T"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="External_Interface_S_AXI_HP0_FIFO_CTRL_0" NAME="S_AXI_HP0_FIFO_CTRL" TYPE="TARGET" VLNV="xilinx.com:display_processing_system7:hpstatusctrl:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="RCOUNT" PHYSICAL="S_AXI_HP0_RCOUNT"/>
            <PORTMAP LOGICAL="WCOUNT" PHYSICAL="S_AXI_HP0_WCOUNT"/>
            <PORTMAP LOGICAL="RACOUNT" PHYSICAL="S_AXI_HP0_RACOUNT"/>
            <PORTMAP LOGICAL="WACOUNT" PHYSICAL="S_AXI_HP0_WACOUNT"/>
            <PORTMAP LOGICAL="RDISSUECAPEN" PHYSICAL="S_AXI_HP0_RDISSUECAP1_EN"/>
            <PORTMAP LOGICAL="WRISSUECAPEN" PHYSICAL="S_AXI_HP0_WRISSUECAP1_EN"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="External_Interface_S_AXI_HP1_FIFO_CTRL_0" NAME="S_AXI_HP1_FIFO_CTRL" TYPE="TARGET" VLNV="xilinx.com:display_processing_system7:hpstatusctrl:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="RCOUNT" PHYSICAL="S_AXI_HP1_RCOUNT"/>
            <PORTMAP LOGICAL="WCOUNT" PHYSICAL="S_AXI_HP1_WCOUNT"/>
            <PORTMAP LOGICAL="RACOUNT" PHYSICAL="S_AXI_HP1_RACOUNT"/>
            <PORTMAP LOGICAL="WACOUNT" PHYSICAL="S_AXI_HP1_WACOUNT"/>
            <PORTMAP LOGICAL="RDISSUECAPEN" PHYSICAL="S_AXI_HP1_RDISSUECAP1_EN"/>
            <PORTMAP LOGICAL="WRISSUECAPEN" PHYSICAL="S_AXI_HP1_WRISSUECAP1_EN"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="processing_system7_0_M_AXI_GP0" DATAWIDTH="32" NAME="M_AXI_GP0" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="8"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="8"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI3"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="12"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="1"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="1"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="1"/>
          <PARAMETER NAME="HAS_QOS" VALUE="1"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="16"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="4"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="4"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M_AXI_GP0_ARVALID"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="M_AXI_GP0_AWVALID"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="M_AXI_GP0_BREADY"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M_AXI_GP0_RREADY"/>
            <PORTMAP LOGICAL="WLAST" PHYSICAL="M_AXI_GP0_WLAST"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="M_AXI_GP0_WVALID"/>
            <PORTMAP LOGICAL="ARID" PHYSICAL="M_AXI_GP0_ARID"/>
            <PORTMAP LOGICAL="AWID" PHYSICAL="M_AXI_GP0_AWID"/>
            <PORTMAP LOGICAL="WID" PHYSICAL="M_AXI_GP0_WID"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="M_AXI_GP0_ARBURST"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="M_AXI_GP0_ARLOCK"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="M_AXI_GP0_ARSIZE"/>
            <PORTMAP LOGICAL="AWBURST" PHYSICAL="M_AXI_GP0_AWBURST"/>
            <PORTMAP LOGICAL="AWLOCK" PHYSICAL="M_AXI_GP0_AWLOCK"/>
            <PORTMAP LOGICAL="AWSIZE" PHYSICAL="M_AXI_GP0_AWSIZE"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M_AXI_GP0_ARPROT"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="M_AXI_GP0_AWPROT"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M_AXI_GP0_ARADDR"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="M_AXI_GP0_AWADDR"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="M_AXI_GP0_WDATA"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="M_AXI_GP0_ARCACHE"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="M_AXI_GP0_ARLEN"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="M_AXI_GP0_ARQOS"/>
            <PORTMAP LOGICAL="AWCACHE" PHYSICAL="M_AXI_GP0_AWCACHE"/>
            <PORTMAP LOGICAL="AWLEN" PHYSICAL="M_AXI_GP0_AWLEN"/>
            <PORTMAP LOGICAL="AWQOS" PHYSICAL="M_AXI_GP0_AWQOS"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="M_AXI_GP0_WSTRB"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M_AXI_GP0_ARREADY"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="M_AXI_GP0_AWREADY"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="M_AXI_GP0_BVALID"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="M_AXI_GP0_RLAST"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M_AXI_GP0_RVALID"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="M_AXI_GP0_WREADY"/>
            <PORTMAP LOGICAL="BID" PHYSICAL="M_AXI_GP0_BID"/>
            <PORTMAP LOGICAL="RID" PHYSICAL="M_AXI_GP0_RID"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="M_AXI_GP0_BRESP"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M_AXI_GP0_RRESP"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M_AXI_GP0_RDATA"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="axi_smc_M00_AXI" DATAWIDTH="64" NAME="S_AXI_HP0" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="8"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="8"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="64"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI3"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="6"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="1"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="1"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="1"/>
          <PARAMETER NAME="HAS_QOS" VALUE="1"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="16"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="S_AXI_HP0_ARREADY"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="S_AXI_HP0_AWREADY"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="S_AXI_HP0_BVALID"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="S_AXI_HP0_RLAST"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="S_AXI_HP0_RVALID"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="S_AXI_HP0_WREADY"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="S_AXI_HP0_BRESP"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="S_AXI_HP0_RRESP"/>
            <PORTMAP LOGICAL="BID" PHYSICAL="S_AXI_HP0_BID"/>
            <PORTMAP LOGICAL="RID" PHYSICAL="S_AXI_HP0_RID"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="S_AXI_HP0_RDATA"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="S_AXI_HP0_ARVALID"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="S_AXI_HP0_AWVALID"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="S_AXI_HP0_BREADY"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="S_AXI_HP0_RREADY"/>
            <PORTMAP LOGICAL="WLAST" PHYSICAL="S_AXI_HP0_WLAST"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="S_AXI_HP0_WVALID"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="S_AXI_HP0_ARBURST"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="S_AXI_HP0_ARLOCK"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="S_AXI_HP0_ARSIZE"/>
            <PORTMAP LOGICAL="AWBURST" PHYSICAL="S_AXI_HP0_AWBURST"/>
            <PORTMAP LOGICAL="AWLOCK" PHYSICAL="S_AXI_HP0_AWLOCK"/>
            <PORTMAP LOGICAL="AWSIZE" PHYSICAL="S_AXI_HP0_AWSIZE"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="S_AXI_HP0_ARPROT"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="S_AXI_HP0_AWPROT"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="S_AXI_HP0_ARADDR"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="S_AXI_HP0_AWADDR"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="S_AXI_HP0_ARCACHE"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="S_AXI_HP0_ARLEN"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="S_AXI_HP0_ARQOS"/>
            <PORTMAP LOGICAL="AWCACHE" PHYSICAL="S_AXI_HP0_AWCACHE"/>
            <PORTMAP LOGICAL="AWLEN" PHYSICAL="S_AXI_HP0_AWLEN"/>
            <PORTMAP LOGICAL="AWQOS" PHYSICAL="S_AXI_HP0_AWQOS"/>
            <PORTMAP LOGICAL="ARID" PHYSICAL="S_AXI_HP0_ARID"/>
            <PORTMAP LOGICAL="AWID" PHYSICAL="S_AXI_HP0_AWID"/>
            <PORTMAP LOGICAL="WID" PHYSICAL="S_AXI_HP0_WID"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="S_AXI_HP0_WDATA"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="S_AXI_HP0_WSTRB"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="axi_smc1_M00_AXI" DATAWIDTH="64" NAME="S_AXI_HP1" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="8"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="8"/>
          <PARAMETER NAME="DATA_WIDTH" VALUE="64"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI3"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="6"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="32"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="1"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="1"/>
          <PARAMETER NAME="HAS_PROT" VALUE="1"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="1"/>
          <PARAMETER NAME="HAS_QOS" VALUE="1"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="16"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="S_AXI_HP1_ARREADY"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="S_AXI_HP1_AWREADY"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="S_AXI_HP1_BVALID"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="S_AXI_HP1_RLAST"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="S_AXI_HP1_RVALID"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="S_AXI_HP1_WREADY"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="S_AXI_HP1_BRESP"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="S_AXI_HP1_RRESP"/>
            <PORTMAP LOGICAL="BID" PHYSICAL="S_AXI_HP1_BID"/>
            <PORTMAP LOGICAL="RID" PHYSICAL="S_AXI_HP1_RID"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="S_AXI_HP1_RDATA"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="S_AXI_HP1_ARVALID"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="S_AXI_HP1_AWVALID"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="S_AXI_HP1_BREADY"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="S_AXI_HP1_RREADY"/>
            <PORTMAP LOGICAL="WLAST" PHYSICAL="S_AXI_HP1_WLAST"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="S_AXI_HP1_WVALID"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="S_AXI_HP1_ARBURST"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="S_AXI_HP1_ARLOCK"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="S_AXI_HP1_ARSIZE"/>
            <PORTMAP LOGICAL="AWBURST" PHYSICAL="S_AXI_HP1_AWBURST"/>
            <PORTMAP LOGICAL="AWLOCK" PHYSICAL="S_AXI_HP1_AWLOCK"/>
            <PORTMAP LOGICAL="AWSIZE" PHYSICAL="S_AXI_HP1_AWSIZE"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="S_AXI_HP1_ARPROT"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="S_AXI_HP1_AWPROT"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="S_AXI_HP1_ARADDR"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="S_AXI_HP1_AWADDR"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="S_AXI_HP1_ARCACHE"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="S_AXI_HP1_ARLEN"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="S_AXI_HP1_ARQOS"/>
            <PORTMAP LOGICAL="AWCACHE" PHYSICAL="S_AXI_HP1_AWCACHE"/>
            <PORTMAP LOGICAL="AWLEN" PHYSICAL="S_AXI_HP1_AWLEN"/>
            <PORTMAP LOGICAL="AWQOS" PHYSICAL="S_AXI_HP1_AWQOS"/>
            <PORTMAP LOGICAL="ARID" PHYSICAL="S_AXI_HP1_ARID"/>
            <PORTMAP LOGICAL="AWID" PHYSICAL="S_AXI_HP1_AWID"/>
            <PORTMAP LOGICAL="WID" PHYSICAL="S_AXI_HP1_WID"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="S_AXI_HP1_WDATA"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="S_AXI_HP1_WSTRB"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
      <MEMORYMAP>
        <MEMRANGE ADDRESSBLOCK="Reg" BASENAME="C_BASEADDR" BASEVALUE="0x43000000" HIGHNAME="C_HIGHADDR" HIGHVALUE="0x4300FFFF" INSTANCE="axi_vdma_0" IS_DATA="TRUE" IS_INSTRUCTION="TRUE" MASTERBUSINTERFACE="M_AXI_GP0" MEMTYPE="REGISTER" SLAVEBUSINTERFACE="S_AXI_LITE"/>
        <MEMRANGE ADDRESSBLOCK="Reg" BASENAME="C_BASEADDR" BASEVALUE="0x43C00000" HIGHNAME="C_HIGHADDR" HIGHVALUE="0x43C0FFFF" INSTANCE="v_tc_0" IS_DATA="TRUE" IS_INSTRUCTION="TRUE" MASTERBUSINTERFACE="M_AXI_GP0" MEMTYPE="REGISTER" SLAVEBUSINTERFACE="ctrl"/>
        <MEMRANGE ADDRESSBLOCK="reg0" BASENAME="C_BASEADDR" BASEVALUE="0x43C10000" HIGHNAME="C_HIGHADDR" HIGHVALUE="0x43C1FFFF" INSTANCE="axi_dynclk_0" IS_DATA="TRUE" IS_INSTRUCTION="TRUE" MASTERBUSINTERFACE="M_AXI_GP0" MEMTYPE="REGISTER" SLAVEBUSINTERFACE="s00_axi"/>
        <MEMRANGE ADDRESSBLOCK="S00_AXI_reg" BASENAME="C_S00_AXI_BASEADDR" BASEVALUE="0x43C20000" HIGHNAME="C_S00_AXI_HIGHADDR" HIGHVALUE="0x43C2FFFF" INSTANCE="misc_0" IS_DATA="TRUE" IS_INSTRUCTION="TRUE" MASTERBUSINTERFACE="M_AXI_GP0" MEMTYPE="REGISTER" SLAVEBUSINTERFACE="S00_AXI"/>
        <MEMRANGE ADDRESSBLOCK="S00_AXI_reg" BASENAME="C_S00_AXI_BASEADDR" BASEVALUE="0x43C30000" HIGHNAME="C_S00_AXI_HIGHADDR" HIGHVALUE="0x43C3FFFF" INSTANCE="AXI_ADC_0" IS_DATA="TRUE" IS_INSTRUCTION="TRUE" MASTERBUSINTERFACE="M_AXI_GP0" MEMTYPE="REGISTER" SLAVEBUSINTERFACE="S00_AXI"/>
      </MEMORYMAP>
      <PERIPHERALS>
        <PERIPHERAL INSTANCE="axi_vdma_0"/>
        <PERIPHERAL INSTANCE="v_tc_0"/>
        <PERIPHERAL INSTANCE="axi_dynclk_0"/>
        <PERIPHERAL INSTANCE="misc_0"/>
        <PERIPHERAL INSTANCE="AXI_ADC_0"/>
      </PERIPHERALS>
    </MODULE>
    <MODULE COREREVISION="19" FULLNAME="/ps7_0_axi_periph" HWVERSION="2.1" INSTANCE="ps7_0_axi_periph" IPTYPE="BUS" IS_ENABLE="1" MODCLASS="BUS" MODTYPE="axi_interconnect" VLNV="xilinx.com:ip:axi_interconnect:2.1">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=axi_interconnect;v=v2_1;d=pg059-axi-interconnect.pdf"/>
      </DOCUMENTS>
      <PARAMETERS>
        <PARAMETER NAME="NUM_SI" VALUE="1"/>
        <PARAMETER NAME="NUM_MI" VALUE="5"/>
        <PARAMETER NAME="STRATEGY" VALUE="0"/>
        <PARAMETER NAME="ENABLE_ADVANCED_OPTIONS" VALUE="0"/>
        <PARAMETER NAME="ENABLE_PROTOCOL_CHECKERS" VALUE="0"/>
        <PARAMETER NAME="XBAR_DATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="PCHK_WAITS" VALUE="0"/>
        <PARAMETER NAME="PCHK_MAX_RD_BURSTS" VALUE="2"/>
        <PARAMETER NAME="PCHK_MAX_WR_BURSTS" VALUE="2"/>
        <PARAMETER NAME="SYNCHRONIZATION_STAGES" VALUE="3"/>
        <PARAMETER NAME="M00_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M01_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M02_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M03_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M04_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M05_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M06_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M07_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M08_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M09_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M10_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M11_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M12_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M13_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M14_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M15_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M16_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M17_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M18_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M19_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M20_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M21_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M22_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M23_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M24_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M25_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M26_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M27_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M28_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M29_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M30_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M31_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M32_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M33_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M34_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M35_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M36_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M37_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M38_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M39_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M40_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M41_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M42_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M43_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M44_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M45_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M46_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M47_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M48_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M49_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M50_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M51_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M52_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M53_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M54_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M55_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M56_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M57_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M58_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M59_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M60_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M61_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M62_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M63_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="M00_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M01_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M02_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M03_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M04_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M05_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M06_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M07_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M08_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M09_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M10_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M11_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M12_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M13_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M14_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M15_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M16_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M17_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M18_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M19_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M20_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M21_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M22_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M23_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M24_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M25_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M26_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M27_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M28_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M29_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M30_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M31_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M32_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M33_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M34_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M35_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M36_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M37_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M38_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M39_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M40_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M41_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M42_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M43_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M44_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M45_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M46_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M47_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M48_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M49_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M50_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M51_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M52_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M53_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M54_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M55_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M56_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M57_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M58_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M59_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M60_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M61_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M62_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M63_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S00_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S01_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S02_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S03_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S04_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S05_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S06_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S07_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S08_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S09_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S10_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S11_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S12_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S13_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S14_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S15_HAS_REGSLICE" VALUE="0"/>
        <PARAMETER NAME="S00_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S01_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S02_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S03_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S04_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S05_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S06_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S07_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S08_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S09_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S10_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S11_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S12_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S13_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S14_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="S15_HAS_DATA_FIFO" VALUE="0"/>
        <PARAMETER NAME="M00_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M01_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M02_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M03_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M04_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M05_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M06_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M07_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M08_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M09_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M10_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M11_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M12_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M13_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M14_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M15_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M16_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M17_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M18_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M19_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M20_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M21_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M22_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M23_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M24_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M25_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M26_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M27_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M28_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M29_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M30_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M31_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M32_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M33_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M34_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M35_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M36_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M37_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M38_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M39_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M40_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M41_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M42_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M43_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M44_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M45_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M46_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M47_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M48_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M49_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M50_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M51_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M52_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M53_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M54_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M55_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M56_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M57_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M58_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M59_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M60_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M61_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M62_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M63_ISSUANCE" VALUE="0"/>
        <PARAMETER NAME="M00_SECURE" VALUE="0"/>
        <PARAMETER NAME="M01_SECURE" VALUE="0"/>
        <PARAMETER NAME="M02_SECURE" VALUE="0"/>
        <PARAMETER NAME="M03_SECURE" VALUE="0"/>
        <PARAMETER NAME="M04_SECURE" VALUE="0"/>
        <PARAMETER NAME="M05_SECURE" VALUE="0"/>
        <PARAMETER NAME="M06_SECURE" VALUE="0"/>
        <PARAMETER NAME="M07_SECURE" VALUE="0"/>
        <PARAMETER NAME="M08_SECURE" VALUE="0"/>
        <PARAMETER NAME="M09_SECURE" VALUE="0"/>
        <PARAMETER NAME="M10_SECURE" VALUE="0"/>
        <PARAMETER NAME="M11_SECURE" VALUE="0"/>
        <PARAMETER NAME="M12_SECURE" VALUE="0"/>
        <PARAMETER NAME="M13_SECURE" VALUE="0"/>
        <PARAMETER NAME="M14_SECURE" VALUE="0"/>
        <PARAMETER NAME="M15_SECURE" VALUE="0"/>
        <PARAMETER NAME="M16_SECURE" VALUE="0"/>
        <PARAMETER NAME="M17_SECURE" VALUE="0"/>
        <PARAMETER NAME="M18_SECURE" VALUE="0"/>
        <PARAMETER NAME="M19_SECURE" VALUE="0"/>
        <PARAMETER NAME="M20_SECURE" VALUE="0"/>
        <PARAMETER NAME="M21_SECURE" VALUE="0"/>
        <PARAMETER NAME="M22_SECURE" VALUE="0"/>
        <PARAMETER NAME="M23_SECURE" VALUE="0"/>
        <PARAMETER NAME="M24_SECURE" VALUE="0"/>
        <PARAMETER NAME="M25_SECURE" VALUE="0"/>
        <PARAMETER NAME="M26_SECURE" VALUE="0"/>
        <PARAMETER NAME="M27_SECURE" VALUE="0"/>
        <PARAMETER NAME="M28_SECURE" VALUE="0"/>
        <PARAMETER NAME="M29_SECURE" VALUE="0"/>
        <PARAMETER NAME="M30_SECURE" VALUE="0"/>
        <PARAMETER NAME="M31_SECURE" VALUE="0"/>
        <PARAMETER NAME="M32_SECURE" VALUE="0"/>
        <PARAMETER NAME="M33_SECURE" VALUE="0"/>
        <PARAMETER NAME="M34_SECURE" VALUE="0"/>
        <PARAMETER NAME="M35_SECURE" VALUE="0"/>
        <PARAMETER NAME="M36_SECURE" VALUE="0"/>
        <PARAMETER NAME="M37_SECURE" VALUE="0"/>
        <PARAMETER NAME="M38_SECURE" VALUE="0"/>
        <PARAMETER NAME="M39_SECURE" VALUE="0"/>
        <PARAMETER NAME="M40_SECURE" VALUE="0"/>
        <PARAMETER NAME="M41_SECURE" VALUE="0"/>
        <PARAMETER NAME="M42_SECURE" VALUE="0"/>
        <PARAMETER NAME="M43_SECURE" VALUE="0"/>
        <PARAMETER NAME="M44_SECURE" VALUE="0"/>
        <PARAMETER NAME="M45_SECURE" VALUE="0"/>
        <PARAMETER NAME="M46_SECURE" VALUE="0"/>
        <PARAMETER NAME="M47_SECURE" VALUE="0"/>
        <PARAMETER NAME="M48_SECURE" VALUE="0"/>
        <PARAMETER NAME="M49_SECURE" VALUE="0"/>
        <PARAMETER NAME="M50_SECURE" VALUE="0"/>
        <PARAMETER NAME="M51_SECURE" VALUE="0"/>
        <PARAMETER NAME="M52_SECURE" VALUE="0"/>
        <PARAMETER NAME="M53_SECURE" VALUE="0"/>
        <PARAMETER NAME="M54_SECURE" VALUE="0"/>
        <PARAMETER NAME="M55_SECURE" VALUE="0"/>
        <PARAMETER NAME="M56_SECURE" VALUE="0"/>
        <PARAMETER NAME="M57_SECURE" VALUE="0"/>
        <PARAMETER NAME="M58_SECURE" VALUE="0"/>
        <PARAMETER NAME="M59_SECURE" VALUE="0"/>
        <PARAMETER NAME="M60_SECURE" VALUE="0"/>
        <PARAMETER NAME="M61_SECURE" VALUE="0"/>
        <PARAMETER NAME="M62_SECURE" VALUE="0"/>
        <PARAMETER NAME="M63_SECURE" VALUE="0"/>
        <PARAMETER NAME="S00_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S01_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S02_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S03_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S04_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S05_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S06_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S07_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S08_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S09_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S10_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S11_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S12_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S13_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S14_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="S15_ARB_PRIORITY" VALUE="0"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_ps7_0_axi_periph_1"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="BUS"/>
      </PARAMETERS>
      <PORTS>
        <PORT DIR="I" NAME="ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="ARESETN" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_ARESETN" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M00_ARESETN" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M01_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M01_ARESETN" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M02_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M02_ARESETN" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M03_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M03_ARESETN" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M04_ACLK" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M04_ARESETN" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M01_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M01_AXI_awprot" SIGIS="undef"/>
        <PORT DIR="O" LEFT="0" NAME="M01_AXI_awvalid" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M01_AXI_awready" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M01_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M01_AXI_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="M01_AXI_wvalid" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M01_AXI_wready" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M01_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M01_AXI_bvalid" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="M01_AXI_bready" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M01_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M01_AXI_arprot" SIGIS="undef"/>
        <PORT DIR="O" LEFT="0" NAME="M01_AXI_arvalid" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M01_AXI_arready" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="M01_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M01_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M01_AXI_rvalid" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="M01_AXI_rready" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M04_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M04_AXI_awprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M04_AXI_awvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M04_AXI_awready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M04_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M04_AXI_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M04_AXI_wvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M04_AXI_wready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M04_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M04_AXI_bvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M04_AXI_bready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M04_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M04_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M04_AXI_arvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M04_AXI_arready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="M04_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M04_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M04_AXI_rvalid" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M04_AXI_rready" SIGIS="undef" SIGNAME="AXI_ADC_0_s00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M02_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M02_AXI_awprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M02_AXI_awvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M02_AXI_awready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M02_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M02_AXI_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M02_AXI_wvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M02_AXI_wready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M02_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M02_AXI_bvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M02_AXI_bready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M02_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M02_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M02_AXI_arvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M02_AXI_arready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="M02_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M02_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M02_AXI_rvalid" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M02_AXI_rready" SIGIS="undef" SIGNAME="axi_dynclk_0_s00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M03_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M03_AXI_awprot" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_awprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_awprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M03_AXI_awvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M03_AXI_awready" SIGIS="undef" SIGNAME="misc_0_s00_axi_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M03_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="3" NAME="M03_AXI_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M03_AXI_wvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M03_AXI_wready" SIGIS="undef" SIGNAME="misc_0_s00_axi_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M03_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M03_AXI_bvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M03_AXI_bready" SIGIS="undef" SIGNAME="misc_0_s00_axi_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M03_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="2" NAME="M03_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_arprot">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_arprot"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M03_AXI_arvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M03_AXI_arready" SIGIS="undef" SIGNAME="misc_0_s00_axi_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="M03_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M03_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="misc_0_s00_axi_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="M03_AXI_rvalid" SIGIS="undef" SIGNAME="misc_0_s00_axi_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M03_AXI_rready" SIGIS="undef" SIGNAME="misc_0_s00_axi_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="11" NAME="S00_AXI_awid" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S00_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWADDR">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWADDR"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_awlen" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWLEN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWLEN"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_awsize" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWSIZE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWSIZE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S00_AXI_awburst" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWBURST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWBURST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S00_AXI_awlock" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWLOCK">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWLOCK"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_awcache" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWCACHE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWCACHE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_awprot" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWPROT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWPROT"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_awqos" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWQOS">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWQOS"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_awvalid" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_awready" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_AWREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_AWREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="11" NAME="S00_AXI_wid" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_WID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S00_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WDATA">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_WDATA"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WSTRB">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_WSTRB"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_wlast" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WLAST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_WLAST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_wvalid" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_WVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_wready" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_WREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_WREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="11" NAME="S00_AXI_bid" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_BID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="S00_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BRESP">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_BRESP"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_bvalid" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_BVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_bready" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_BREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_BREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="11" NAME="S00_AXI_arid" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="S00_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARADDR">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARADDR"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_arlen" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARLEN">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARLEN"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_arsize" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARSIZE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARSIZE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S00_AXI_arburst" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARBURST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARBURST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="S00_AXI_arlock" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARLOCK">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARLOCK"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_arcache" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARCACHE">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARCACHE"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="2" NAME="S00_AXI_arprot" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARPROT">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARPROT"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="S00_AXI_arqos" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARQOS">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARQOS"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_arvalid" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_arready" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_ARREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_ARREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="11" NAME="S00_AXI_rid" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_RID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="S00_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RDATA">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_RDATA"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="S00_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RRESP">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_RRESP"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_rlast" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RLAST">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_RLAST"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="S00_AXI_rvalid" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RVALID">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_RVALID"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="S00_AXI_rready" SIGIS="undef" SIGNAME="processing_system7_0_M_AXI_GP0_RREADY">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="M_AXI_GP0_RREADY"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M00_AXI_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_awprot" SIGIS="undef"/>
        <PORT DIR="O" LEFT="0" NAME="M00_AXI_awvalid" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M00_AXI_awready" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M00_AXI_wdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_wstrb" SIGIS="undef"/>
        <PORT DIR="O" LEFT="0" NAME="M00_AXI_wvalid" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M00_AXI_wready" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M00_AXI_bresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M00_AXI_bvalid" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="M00_AXI_bready" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="M00_AXI_araddr" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="M00_AXI_arprot" SIGIS="undef"/>
        <PORT DIR="O" LEFT="0" NAME="M00_AXI_arvalid" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M00_AXI_arready" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="M00_AXI_rdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="1" NAME="M00_AXI_rresp" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="M00_AXI_rvalid" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="M00_AXI_rready" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_s_axi_lite_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="s_axi_lite_rready"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="processing_system7_0_M_AXI_GP0" DATAWIDTH="32" NAME="S00_AXI" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="AWID" PHYSICAL="S00_AXI_awid"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="S00_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWLEN" PHYSICAL="S00_AXI_awlen"/>
            <PORTMAP LOGICAL="AWSIZE" PHYSICAL="S00_AXI_awsize"/>
            <PORTMAP LOGICAL="AWBURST" PHYSICAL="S00_AXI_awburst"/>
            <PORTMAP LOGICAL="AWLOCK" PHYSICAL="S00_AXI_awlock"/>
            <PORTMAP LOGICAL="AWCACHE" PHYSICAL="S00_AXI_awcache"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="S00_AXI_awprot"/>
            <PORTMAP LOGICAL="AWQOS" PHYSICAL="S00_AXI_awqos"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="S00_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="S00_AXI_awready"/>
            <PORTMAP LOGICAL="WID" PHYSICAL="S00_AXI_wid"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="S00_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="S00_AXI_wstrb"/>
            <PORTMAP LOGICAL="WLAST" PHYSICAL="S00_AXI_wlast"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="S00_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="S00_AXI_wready"/>
            <PORTMAP LOGICAL="BID" PHYSICAL="S00_AXI_bid"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="S00_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="S00_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="S00_AXI_bready"/>
            <PORTMAP LOGICAL="ARID" PHYSICAL="S00_AXI_arid"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="S00_AXI_araddr"/>
            <PORTMAP LOGICAL="ARLEN" PHYSICAL="S00_AXI_arlen"/>
            <PORTMAP LOGICAL="ARSIZE" PHYSICAL="S00_AXI_arsize"/>
            <PORTMAP LOGICAL="ARBURST" PHYSICAL="S00_AXI_arburst"/>
            <PORTMAP LOGICAL="ARLOCK" PHYSICAL="S00_AXI_arlock"/>
            <PORTMAP LOGICAL="ARCACHE" PHYSICAL="S00_AXI_arcache"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="S00_AXI_arprot"/>
            <PORTMAP LOGICAL="ARQOS" PHYSICAL="S00_AXI_arqos"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="S00_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="S00_AXI_arready"/>
            <PORTMAP LOGICAL="RID" PHYSICAL="S00_AXI_rid"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="S00_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="S00_AXI_rresp"/>
            <PORTMAP LOGICAL="RLAST" PHYSICAL="S00_AXI_rlast"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="S00_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="S00_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M00_AXI" DATAWIDTH="32" NAME="M00_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="M00_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="M00_AXI_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="M00_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="M00_AXI_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="M00_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="M00_AXI_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="M00_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="M00_AXI_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="M00_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="M00_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="M00_AXI_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M00_AXI_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M00_AXI_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M00_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M00_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M00_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M00_AXI_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M00_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M00_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M01_AXI" DATAWIDTH="32" NAME="M01_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="M01_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="M01_AXI_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="M01_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="M01_AXI_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="M01_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="M01_AXI_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="M01_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="M01_AXI_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="M01_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="M01_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="M01_AXI_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M01_AXI_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M01_AXI_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M01_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M01_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M01_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M01_AXI_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M01_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M01_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M02_AXI" DATAWIDTH="32" NAME="M02_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="M02_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="M02_AXI_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="M02_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="M02_AXI_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="M02_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="M02_AXI_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="M02_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="M02_AXI_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="M02_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="M02_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="M02_AXI_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M02_AXI_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M02_AXI_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M02_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M02_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M02_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M02_AXI_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M02_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M02_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M03_AXI" DATAWIDTH="32" NAME="M03_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="M03_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="M03_AXI_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="M03_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="M03_AXI_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="M03_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="M03_AXI_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="M03_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="M03_AXI_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="M03_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="M03_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="M03_AXI_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M03_AXI_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M03_AXI_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M03_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M03_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M03_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M03_AXI_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M03_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M03_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M04_AXI" DATAWIDTH="32" NAME="M04_AXI" TYPE="MASTER" VLNV="xilinx.com:interface:aximm:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="M04_AXI_awaddr"/>
            <PORTMAP LOGICAL="AWPROT" PHYSICAL="M04_AXI_awprot"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="M04_AXI_awvalid"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="M04_AXI_awready"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="M04_AXI_wdata"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="M04_AXI_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="M04_AXI_wvalid"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="M04_AXI_wready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="M04_AXI_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="M04_AXI_bvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="M04_AXI_bready"/>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="M04_AXI_araddr"/>
            <PORTMAP LOGICAL="ARPROT" PHYSICAL="M04_AXI_arprot"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="M04_AXI_arvalid"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="M04_AXI_arready"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="M04_AXI_rdata"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="M04_AXI_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="M04_AXI_rvalid"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="M04_AXI_rready"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
    </MODULE>
    <MODULE COREREVISION="13" FULLNAME="/rst_ps7_0_100M" HWVERSION="5.0" INSTANCE="rst_ps7_0_100M" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="proc_sys_reset" VLNV="xilinx.com:ip:proc_sys_reset:5.0">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=proc_sys_reset;v=v5_0;d=pg164-proc-sys-reset.pdf"/>
      </DOCUMENTS>
      <PARAMETERS>
        <PARAMETER NAME="C_FAMILY" VALUE="zynq"/>
        <PARAMETER NAME="C_EXT_RST_WIDTH" VALUE="4"/>
        <PARAMETER NAME="C_AUX_RST_WIDTH" VALUE="4"/>
        <PARAMETER NAME="C_EXT_RESET_HIGH" VALUE="0"/>
        <PARAMETER NAME="C_AUX_RESET_HIGH" VALUE="0"/>
        <PARAMETER NAME="C_NUM_BUS_RST" VALUE="1"/>
        <PARAMETER NAME="C_NUM_PERP_RST" VALUE="1"/>
        <PARAMETER NAME="C_NUM_INTERCONNECT_ARESETN" VALUE="1"/>
        <PARAMETER NAME="C_NUM_PERP_ARESETN" VALUE="1"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_rst_ps7_0_100M_1"/>
        <PARAMETER NAME="USE_BOARD_FLOW" VALUE="false"/>
        <PARAMETER NAME="RESET_BOARD_INTERFACE" VALUE="Custom"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="slowest_sync_clk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="ext_reset_in" SIGIS="rst" SIGNAME="processing_system7_0_FCLK_RESET0_N">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_RESET0_N"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="aux_reset_in" SIGIS="rst"/>
        <PORT DIR="I" NAME="mb_debug_sys_rst" SIGIS="rst"/>
        <PORT DIR="I" NAME="dcm_locked" SIGIS="undef"/>
        <PORT DIR="O" NAME="mb_reset" SIGIS="rst"/>
        <PORT DIR="O" LEFT="0" NAME="bus_struct_reset" RIGHT="0" SIGIS="rst"/>
        <PORT DIR="O" LEFT="0" NAME="peripheral_reset" RIGHT="0" SIGIS="rst"/>
        <PORT DIR="O" LEFT="0" NAME="interconnect_aresetn" RIGHT="0" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="S00_ARESETN"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="ARESETN"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M00_ARESETN"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_ARESETN"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M02_ARESETN"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M03_ARESETN"/>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M04_ARESETN"/>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="axi_resetn"/>
            <CONNECTION INSTANCE="axi_smc" PORT="aresetn"/>
            <CONNECTION INSTANCE="axi_smc1" PORT="aresetn"/>
            <CONNECTION INSTANCE="v_tc_0" PORT="s_axi_aresetn"/>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="s00_axi_aresetn"/>
            <CONNECTION INSTANCE="misc_0" PORT="s00_axi_aresetn"/>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="s00_axi_aresetn"/>
            <CONNECTION INSTANCE="AXI_ADC_0" PORT="m00_axi_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="0" NAME="peripheral_aresetn" RIGHT="0" SIGIS="rst"/>
      </PORTS>
      <BUSINTERFACES/>
    </MODULE>
    <MODULE COREREVISION="13" FULLNAME="/rst_ps7_0_100M1" HWVERSION="5.0" INSTANCE="rst_ps7_0_100M1" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="proc_sys_reset" VLNV="xilinx.com:ip:proc_sys_reset:5.0">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=proc_sys_reset;v=v5_0;d=pg164-proc-sys-reset.pdf"/>
      </DOCUMENTS>
      <PARAMETERS>
        <PARAMETER NAME="C_FAMILY" VALUE="zynq"/>
        <PARAMETER NAME="C_EXT_RST_WIDTH" VALUE="4"/>
        <PARAMETER NAME="C_AUX_RST_WIDTH" VALUE="4"/>
        <PARAMETER NAME="C_EXT_RESET_HIGH" VALUE="0"/>
        <PARAMETER NAME="C_AUX_RESET_HIGH" VALUE="0"/>
        <PARAMETER NAME="C_NUM_BUS_RST" VALUE="1"/>
        <PARAMETER NAME="C_NUM_PERP_RST" VALUE="1"/>
        <PARAMETER NAME="C_NUM_INTERCONNECT_ARESETN" VALUE="1"/>
        <PARAMETER NAME="C_NUM_PERP_ARESETN" VALUE="1"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_rst_ps7_0_100M_0"/>
        <PARAMETER NAME="USE_BOARD_FLOW" VALUE="false"/>
        <PARAMETER NAME="RESET_BOARD_INTERFACE" VALUE="Custom"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="slowest_sync_clk" SIGIS="clk" SIGNAME="axi_dynclk_0_PXL_CLK_O">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="PXL_CLK_O"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="ext_reset_in" SIGIS="rst" SIGNAME="processing_system7_0_FCLK_RESET0_N">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_RESET0_N"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="aux_reset_in" SIGIS="rst"/>
        <PORT DIR="I" NAME="mb_debug_sys_rst" SIGIS="rst"/>
        <PORT DIR="I" NAME="dcm_locked" SIGIS="undef"/>
        <PORT DIR="O" NAME="mb_reset" SIGIS="rst"/>
        <PORT DIR="O" LEFT="0" NAME="bus_struct_reset" RIGHT="0" SIGIS="rst"/>
        <PORT DIR="O" LEFT="0" NAME="peripheral_reset" RIGHT="0" SIGIS="rst"/>
        <PORT DIR="O" LEFT="0" NAME="interconnect_aresetn" RIGHT="0" SIGIS="rst"/>
        <PORT DIR="O" LEFT="0" NAME="peripheral_aresetn" RIGHT="0" SIGIS="rst" SIGNAME="rst_ps7_0_100M1_peripheral_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="resetn"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES/>
    </MODULE>
    <MODULE COREREVISION="10" FULLNAME="/v_axi4s_vid_out_0" HWVERSION="4.0" INSTANCE="v_axi4s_vid_out_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="v_axi4s_vid_out" VLNV="xilinx.com:ip:v_axi4s_vid_out:4.0">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=v_axi4s_vid_out;v=v4_0;d=pg044_v_axis_vid_out.pdf"/>
      </DOCUMENTS>
      <PARAMETERS>
        <PARAMETER NAME="C_FAMILY" VALUE="zynq"/>
        <PARAMETER NAME="C_PIXELS_PER_CLOCK" VALUE="1"/>
        <PARAMETER NAME="C_COMPONENTS_PER_PIXEL" VALUE="4"/>
        <PARAMETER NAME="C_S_AXIS_COMPONENT_WIDTH" VALUE="8"/>
        <PARAMETER NAME="C_NATIVE_COMPONENT_WIDTH" VALUE="8"/>
        <PARAMETER NAME="C_NATIVE_DATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_S_AXIS_TDATA_WIDTH" VALUE="32"/>
        <PARAMETER NAME="C_HAS_ASYNC_CLK" VALUE="1"/>
        <PARAMETER NAME="C_ADDR_WIDTH" VALUE="10"/>
        <PARAMETER NAME="C_VTG_MASTER_SLAVE" VALUE="0"/>
        <PARAMETER NAME="C_HYSTERESIS_LEVEL" VALUE="12"/>
        <PARAMETER NAME="C_SYNC_LOCK_THRESHOLD" VALUE="4"/>
        <PARAMETER NAME="C_INCLUDE_PIXEL_REPEAT" VALUE="0"/>
        <PARAMETER NAME="C_INCLUDE_PIXEL_REMAP_420" VALUE="0"/>
        <PARAMETER NAME="C_ADDR_WIDTH_PIXEL_REMAP_420" VALUE="10"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_v_axi4s_vid_out_0_2"/>
        <PARAMETER NAME="C_S_AXIS_VIDEO_FORMAT" VALUE="6"/>
        <PARAMETER NAME="C_S_AXIS_VIDEO_DATA_WIDTH" VALUE="8"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="aclken" SIGIS="ce" SIGNAME="xlconstant_1_dout">
          <CONNECTIONS>
            <CONNECTION INSTANCE="xlconstant_1" PORT="dout"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="aresetn" SIGIS="rst"/>
        <PORT DIR="I" LEFT="31" NAME="s_axis_video_tdata" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axis_mm2s_tdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axis_video_tvalid" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axis_mm2s_tvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axis_video_tready" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axis_mm2s_tready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axis_video_tuser" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tuser">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axis_mm2s_tuser"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axis_video_tlast" SIGIS="undef" SIGNAME="axi_vdma_0_m_axis_mm2s_tlast">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="m_axis_mm2s_tlast"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="fid" SIGIS="undef"/>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="vid_io_out_clk" SIGIS="clk" SIGNAME="axi_dynclk_0_PXL_CLK_O">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="PXL_CLK_O"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vid_io_out_ce" SIGIS="ce" SIGNAME="xlconstant_1_dout">
          <CONNECTIONS>
            <CONNECTION INSTANCE="xlconstant_1" PORT="dout"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vid_io_out_reset" SIGIS="rst"/>
        <PORT DIR="O" NAME="vid_active_video" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vid_active_video">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="lcd_de"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="vid_vsync" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vid_vsync">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="lcd_vsync"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="vid_hsync" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vid_hsync">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="lcd_hsync"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="vid_vblank" SIGIS="undef"/>
        <PORT DIR="O" NAME="vid_hblank" SIGIS="undef"/>
        <PORT DIR="O" NAME="vid_field_id" SIGIS="undef"/>
        <PORT DIR="O" LEFT="31" NAME="vid_data" RIGHT="0" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vid_data">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="lcd_data"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vtg_vsync" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_vsync">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="vsync_out"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vtg_hsync" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_hsync">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="hsync_out"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vtg_vblank" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_vblank">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="vblank_out"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vtg_hblank" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_hblank">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="hblank_out"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vtg_active_video" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_active_video">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="active_video_out"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="vtg_field_id" SIGIS="undef"/>
        <PORT DIR="O" NAME="vtg_ce" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_ce">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_tc_0" PORT="gen_clken"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="locked" SIGIS="undef"/>
        <PORT DIR="O" NAME="overflow" SIGIS="undef"/>
        <PORT DIR="O" NAME="underflow" SIGIS="undef"/>
        <PORT DIR="O" LEFT="10" NAME="fifo_read_level" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="31" NAME="status" RIGHT="0" SIGIS="undef"/>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="axi_vdma_0_M_AXIS_MM2S" NAME="video_in" TYPE="TARGET" VLNV="xilinx.com:interface:axis:1.0">
          <PARAMETER NAME="TDATA_NUM_BYTES" VALUE="4"/>
          <PARAMETER NAME="TDEST_WIDTH" VALUE="0"/>
          <PARAMETER NAME="TID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="TUSER_WIDTH" VALUE="1"/>
          <PARAMETER NAME="HAS_TREADY" VALUE="1"/>
          <PARAMETER NAME="HAS_TSTRB" VALUE="0"/>
          <PARAMETER NAME="HAS_TKEEP" VALUE="0"/>
          <PARAMETER NAME="HAS_TLAST" VALUE="1"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="LAYERED_METADATA" VALUE="undef"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="TDATA" PHYSICAL="s_axis_video_tdata"/>
            <PORTMAP LOGICAL="TLAST" PHYSICAL="s_axis_video_tlast"/>
            <PORTMAP LOGICAL="TREADY" PHYSICAL="s_axis_video_tready"/>
            <PORTMAP LOGICAL="TUSER" PHYSICAL="s_axis_video_tuser"/>
            <PORTMAP LOGICAL="TVALID" PHYSICAL="s_axis_video_tvalid"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="v_tc_0_vtiming_out" NAME="vtiming_in" TYPE="TARGET" VLNV="xilinx.com:interface:video_timing:2.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="ACTIVE_VIDEO" PHYSICAL="vtg_active_video"/>
            <PORTMAP LOGICAL="FIELD" PHYSICAL="vtg_field_id"/>
            <PORTMAP LOGICAL="HBLANK" PHYSICAL="vtg_hblank"/>
            <PORTMAP LOGICAL="HSYNC" PHYSICAL="vtg_hsync"/>
            <PORTMAP LOGICAL="VBLANK" PHYSICAL="vtg_vblank"/>
            <PORTMAP LOGICAL="VSYNC" PHYSICAL="vtg_vsync"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="__NOC__" NAME="vid_io_out" TYPE="INITIATOR" VLNV="xilinx.com:interface:vid_io:1.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="ACTIVE_VIDEO" PHYSICAL="vid_active_video"/>
            <PORTMAP LOGICAL="DATA" PHYSICAL="vid_data"/>
            <PORTMAP LOGICAL="FIELD" PHYSICAL="vid_field_id"/>
            <PORTMAP LOGICAL="HBLANK" PHYSICAL="vid_hblank"/>
            <PORTMAP LOGICAL="HSYNC" PHYSICAL="vid_hsync"/>
            <PORTMAP LOGICAL="VBLANK" PHYSICAL="vid_vblank"/>
            <PORTMAP LOGICAL="VSYNC" PHYSICAL="vid_vsync"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
    </MODULE>
    <MODULE COREREVISION="13" FULLNAME="/v_tc_0" HWVERSION="6.1" INSTANCE="v_tc_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="v_tc" VLNV="xilinx.com:ip:v_tc:6.1">
      <DOCUMENTS>
        <DOCUMENT SOURCE="http://www.xilinx.com/cgi-bin/docs/ipdoc?c=v_tc;v=v6_1;d=pg016_v_tc.pdf"/>
      </DOCUMENTS>
      <PARAMETERS>
        <PARAMETER NAME="C_HAS_AXI4_LITE" VALUE="1"/>
        <PARAMETER NAME="C_HAS_INTC_IF" VALUE="0"/>
        <PARAMETER NAME="C_GEN_INTERLACED" VALUE="0"/>
        <PARAMETER NAME="C_GEN_HACTIVE_SIZE" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_VACTIVE_SIZE" VALUE="720"/>
        <PARAMETER NAME="C_GEN_CPARITY" VALUE="0"/>
        <PARAMETER NAME="C_GEN_FIELDID_POLARITY" VALUE="1"/>
        <PARAMETER NAME="C_GEN_VBLANK_POLARITY" VALUE="1"/>
        <PARAMETER NAME="C_GEN_HBLANK_POLARITY" VALUE="1"/>
        <PARAMETER NAME="C_GEN_VSYNC_POLARITY" VALUE="1"/>
        <PARAMETER NAME="C_GEN_HSYNC_POLARITY" VALUE="1"/>
        <PARAMETER NAME="C_GEN_AVIDEO_POLARITY" VALUE="1"/>
        <PARAMETER NAME="C_GEN_ACHROMA_POLARITY" VALUE="1"/>
        <PARAMETER NAME="C_GEN_VIDEO_FORMAT" VALUE="2"/>
        <PARAMETER NAME="C_GEN_HFRAME_SIZE" VALUE="1650"/>
        <PARAMETER NAME="C_GEN_F0_VFRAME_SIZE" VALUE="750"/>
        <PARAMETER NAME="C_GEN_F1_VFRAME_SIZE" VALUE="750"/>
        <PARAMETER NAME="C_GEN_HSYNC_START" VALUE="1390"/>
        <PARAMETER NAME="C_GEN_HSYNC_END" VALUE="1430"/>
        <PARAMETER NAME="C_GEN_F0_VBLANK_HSTART" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_F0_VBLANK_HEND" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_F0_VSYNC_VSTART" VALUE="724"/>
        <PARAMETER NAME="C_GEN_F0_VSYNC_VEND" VALUE="729"/>
        <PARAMETER NAME="C_GEN_F0_VSYNC_HSTART" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_F0_VSYNC_HEND" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_F1_VBLANK_HSTART" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_F1_VBLANK_HEND" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_F1_VSYNC_VSTART" VALUE="724"/>
        <PARAMETER NAME="C_GEN_F1_VSYNC_VEND" VALUE="729"/>
        <PARAMETER NAME="C_GEN_F1_VSYNC_HSTART" VALUE="1280"/>
        <PARAMETER NAME="C_GEN_F1_VSYNC_HEND" VALUE="1280"/>
        <PARAMETER NAME="C_FSYNC_HSTART0" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART0" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART1" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART1" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART2" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART2" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART3" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART3" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART4" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART4" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART5" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART5" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART6" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART6" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART7" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART7" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART8" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART8" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART9" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART9" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART10" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART10" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART11" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART11" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART12" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART12" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART13" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART13" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART14" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART14" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_HSTART15" VALUE="0"/>
        <PARAMETER NAME="C_FSYNC_VSTART15" VALUE="0"/>
        <PARAMETER NAME="C_MAX_PIXELS" VALUE="4096"/>
        <PARAMETER NAME="C_MAX_LINES" VALUE="4096"/>
        <PARAMETER NAME="C_NUM_FSYNCS" VALUE="1"/>
        <PARAMETER NAME="C_INTERLACE_EN" VALUE="0"/>
        <PARAMETER NAME="C_GEN_AUTO_SWITCH" VALUE="0"/>
        <PARAMETER NAME="C_DETECT_EN" VALUE="0"/>
        <PARAMETER NAME="C_SYNC_EN" VALUE="0"/>
        <PARAMETER NAME="C_GENERATE_EN" VALUE="1"/>
        <PARAMETER NAME="C_DET_HSYNC_EN" VALUE="1"/>
        <PARAMETER NAME="C_DET_VSYNC_EN" VALUE="1"/>
        <PARAMETER NAME="C_DET_HBLANK_EN" VALUE="1"/>
        <PARAMETER NAME="C_DET_VBLANK_EN" VALUE="1"/>
        <PARAMETER NAME="C_DET_AVIDEO_EN" VALUE="1"/>
        <PARAMETER NAME="C_DET_ACHROMA_EN" VALUE="0"/>
        <PARAMETER NAME="C_GEN_HSYNC_EN" VALUE="1"/>
        <PARAMETER NAME="C_GEN_VSYNC_EN" VALUE="1"/>
        <PARAMETER NAME="C_GEN_HBLANK_EN" VALUE="1"/>
        <PARAMETER NAME="C_GEN_VBLANK_EN" VALUE="1"/>
        <PARAMETER NAME="C_GEN_AVIDEO_EN" VALUE="1"/>
        <PARAMETER NAME="C_GEN_ACHROMA_EN" VALUE="0"/>
        <PARAMETER NAME="C_GEN_FIELDID_EN" VALUE="0"/>
        <PARAMETER NAME="C_DET_FIELDID_EN" VALUE="0"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_v_tc_0_1"/>
        <PARAMETER NAME="C_FAMILY" VALUE="virtex7"/>
        <PARAMETER NAME="HAS_AXI4_LITE" VALUE="true"/>
        <PARAMETER NAME="HAS_INTC_IF" VALUE="false"/>
        <PARAMETER NAME="INTERLACE_EN" VALUE="false"/>
        <PARAMETER NAME="SYNC_EN" VALUE="false"/>
        <PARAMETER NAME="max_clocks_per_line" VALUE="4096"/>
        <PARAMETER NAME="max_lines_per_frame" VALUE="4096"/>
        <PARAMETER NAME="VIDEO_MODE" VALUE="720p"/>
        <PARAMETER NAME="FSYNC_HSTART0" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART0" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART1" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART1" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART2" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART2" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART3" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART3" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART4" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART4" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART5" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART5" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART6" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART6" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART7" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART7" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART8" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART8" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART9" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART9" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART10" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART10" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART11" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART11" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART12" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART12" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART13" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART13" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART14" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART14" VALUE="0"/>
        <PARAMETER NAME="FSYNC_HSTART15" VALUE="0"/>
        <PARAMETER NAME="FSYNC_VSTART15" VALUE="0"/>
        <PARAMETER NAME="GEN_F0_VSYNC_VSTART" VALUE="724"/>
        <PARAMETER NAME="GEN_F1_VSYNC_VSTART" VALUE="724"/>
        <PARAMETER NAME="GEN_HACTIVE_SIZE" VALUE="1280"/>
        <PARAMETER NAME="GEN_HSYNC_END" VALUE="1430"/>
        <PARAMETER NAME="GEN_HFRAME_SIZE" VALUE="1650"/>
        <PARAMETER NAME="GEN_F0_VSYNC_HSTART" VALUE="1280"/>
        <PARAMETER NAME="GEN_F1_VSYNC_HSTART" VALUE="1280"/>
        <PARAMETER NAME="GEN_F0_VSYNC_HEND" VALUE="1280"/>
        <PARAMETER NAME="GEN_F1_VSYNC_HEND" VALUE="1280"/>
        <PARAMETER NAME="GEN_F0_VFRAME_SIZE" VALUE="750"/>
        <PARAMETER NAME="GEN_F1_VFRAME_SIZE" VALUE="750"/>
        <PARAMETER NAME="GEN_F0_VSYNC_VEND" VALUE="729"/>
        <PARAMETER NAME="GEN_F1_VSYNC_VEND" VALUE="729"/>
        <PARAMETER NAME="GEN_F0_VBLANK_HEND" VALUE="1280"/>
        <PARAMETER NAME="GEN_F1_VBLANK_HEND" VALUE="1280"/>
        <PARAMETER NAME="GEN_HSYNC_START" VALUE="1390"/>
        <PARAMETER NAME="GEN_VACTIVE_SIZE" VALUE="720"/>
        <PARAMETER NAME="GEN_F0_VBLANK_HSTART" VALUE="1280"/>
        <PARAMETER NAME="GEN_F1_VBLANK_HSTART" VALUE="1280"/>
        <PARAMETER NAME="GEN_ACHROMA_POLARITY" VALUE="High"/>
        <PARAMETER NAME="GEN_HSYNC_POLARITY" VALUE="High"/>
        <PARAMETER NAME="GEN_VSYNC_POLARITY" VALUE="High"/>
        <PARAMETER NAME="GEN_HBLANK_POLARITY" VALUE="High"/>
        <PARAMETER NAME="GEN_AVIDEO_POLARITY" VALUE="High"/>
        <PARAMETER NAME="GEN_VBLANK_POLARITY" VALUE="High"/>
        <PARAMETER NAME="GEN_FIELDID_POLARITY" VALUE="High"/>
        <PARAMETER NAME="GEN_INTERLACED" VALUE="false"/>
        <PARAMETER NAME="GEN_CPARITY" VALUE="0"/>
        <PARAMETER NAME="GEN_VIDEO_FORMAT" VALUE="RGB"/>
        <PARAMETER NAME="horizontal_sync_generation" VALUE="true"/>
        <PARAMETER NAME="enable_detection" VALUE="false"/>
        <PARAMETER NAME="vertical_blank_generation" VALUE="true"/>
        <PARAMETER NAME="GEN_FIELDID_EN" VALUE="false"/>
        <PARAMETER NAME="horizontal_blank_detection" VALUE="true"/>
        <PARAMETER NAME="active_chroma_detection" VALUE="false"/>
        <PARAMETER NAME="horizontal_sync_detection" VALUE="true"/>
        <PARAMETER NAME="enable_generation" VALUE="true"/>
        <PARAMETER NAME="auto_generation_mode" VALUE="false"/>
        <PARAMETER NAME="vertical_sync_generation" VALUE="true"/>
        <PARAMETER NAME="active_chroma_generation" VALUE="false"/>
        <PARAMETER NAME="DET_FIELDID_EN" VALUE="false"/>
        <PARAMETER NAME="vertical_blank_detection" VALUE="true"/>
        <PARAMETER NAME="active_video_generation" VALUE="true"/>
        <PARAMETER NAME="vertical_sync_detection" VALUE="true"/>
        <PARAMETER NAME="horizontal_blank_generation" VALUE="true"/>
        <PARAMETER NAME="active_video_detection" VALUE="true"/>
        <PARAMETER NAME="frame_syncs" VALUE="1"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
        <PARAMETER NAME="C_BASEADDR" VALUE="0x43C00000"/>
        <PARAMETER NAME="C_HIGHADDR" VALUE="0x43C0FFFF"/>
      </PARAMETERS>
      <PORTS>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="clk" SIGIS="clk" SIGNAME="axi_dynclk_0_PXL_CLK_O">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_dynclk_0" PORT="PXL_CLK_O"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="clken" SIGIS="ce" SIGNAME="xlconstant_1_dout">
          <CONNECTIONS>
            <CONNECTION INSTANCE="xlconstant_1" PORT="dout"/>
          </CONNECTIONS>
        </PORT>
        <PORT CLKFREQUENCY="100000000" DIR="I" NAME="s_axi_aclk" SIGIS="clk" SIGNAME="processing_system7_0_FCLK_CLK0">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="FCLK_CLK0"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_aclken" SIGIS="ce"/>
        <PORT DIR="I" NAME="gen_clken" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_ce">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vtg_ce"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="hsync_out" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_hsync">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vtg_hsync"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="hblank_out" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_hblank">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vtg_hblank"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="vsync_out" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_vsync">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vtg_vsync"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="vblank_out" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_vblank">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vtg_vblank"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="active_video_out" SIGIS="undef" SIGNAME="v_axi4s_vid_out_0_vtg_active_video">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vtg_active_video"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="resetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M1_peripheral_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M1" PORT="peripheral_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_aresetn" SIGIS="rst" SIGNAME="rst_ps7_0_100M_interconnect_aresetn">
          <CONNECTIONS>
            <CONNECTION INSTANCE="rst_ps7_0_100M" PORT="interconnect_aresetn"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="8" NAME="s_axi_awaddr" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_awaddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_awaddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_awvalid" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_awvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_awvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_awready" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_awready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_awready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="31" NAME="s_axi_wdata" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_wdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="3" NAME="s_axi_wstrb" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wstrb">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_wstrb"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_wvalid" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_wvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_wready" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_wready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_wready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s_axi_bresp" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_bresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_bresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_bvalid" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_bvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_bvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_bready" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_bready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_bready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="8" NAME="s_axi_araddr" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_araddr">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_araddr"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_arvalid" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_arvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_arvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_arready" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_arready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_arready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="31" NAME="s_axi_rdata" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rdata">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_rdata"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" LEFT="1" NAME="s_axi_rresp" RIGHT="0" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rresp">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_rresp"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="s_axi_rvalid" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rvalid">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_rvalid"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" NAME="s_axi_rready" SIGIS="undef" SIGNAME="ps7_0_axi_periph_M01_AXI_rready">
          <CONNECTIONS>
            <CONNECTION INSTANCE="ps7_0_axi_periph" PORT="M01_AXI_rready"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="O" NAME="irq" SENSITIVITY="LEVEL_HIGH" SIGIS="INTERRUPT"/>
        <PORT DIR="I" NAME="fsync_in" SIGIS="undef"/>
        <PORT DIR="O" LEFT="0" NAME="fsync_out" RIGHT="0" SIGIS="undef"/>
      </PORTS>
      <BUSINTERFACES>
        <BUSINTERFACE BUSNAME="ps7_0_axi_periph_M01_AXI" DATAWIDTH="32" NAME="ctrl" TYPE="SLAVE" VLNV="xilinx.com:interface:aximm:1.0">
          <PARAMETER NAME="DATA_WIDTH" VALUE="32"/>
          <PARAMETER NAME="PROTOCOL" VALUE="AXI4LITE"/>
          <PARAMETER NAME="FREQ_HZ" VALUE="100000000"/>
          <PARAMETER NAME="ID_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ADDR_WIDTH" VALUE="9"/>
          <PARAMETER NAME="AWUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="ARUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="WUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="RUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="BUSER_WIDTH" VALUE="0"/>
          <PARAMETER NAME="READ_WRITE_MODE" VALUE="READ_WRITE"/>
          <PARAMETER NAME="HAS_BURST" VALUE="0"/>
          <PARAMETER NAME="HAS_LOCK" VALUE="0"/>
          <PARAMETER NAME="HAS_PROT" VALUE="0"/>
          <PARAMETER NAME="HAS_CACHE" VALUE="0"/>
          <PARAMETER NAME="HAS_QOS" VALUE="0"/>
          <PARAMETER NAME="HAS_REGION" VALUE="0"/>
          <PARAMETER NAME="HAS_WSTRB" VALUE="1"/>
          <PARAMETER NAME="HAS_BRESP" VALUE="1"/>
          <PARAMETER NAME="HAS_RRESP" VALUE="1"/>
          <PARAMETER NAME="SUPPORTS_NARROW_BURST" VALUE="0"/>
          <PARAMETER NAME="NUM_READ_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="NUM_WRITE_OUTSTANDING" VALUE="2"/>
          <PARAMETER NAME="MAX_BURST_LENGTH" VALUE="1"/>
          <PARAMETER NAME="PHASE" VALUE="0.000"/>
          <PARAMETER NAME="CLK_DOMAIN" VALUE="utg_processing_system7_0_0_FCLK_CLK0"/>
          <PARAMETER NAME="NUM_READ_THREADS" VALUE="1"/>
          <PARAMETER NAME="NUM_WRITE_THREADS" VALUE="1"/>
          <PARAMETER NAME="RUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="WUSER_BITS_PER_BYTE" VALUE="0"/>
          <PARAMETER NAME="INSERT_VIP" VALUE="0"/>
          <PORTMAPS>
            <PORTMAP LOGICAL="ARADDR" PHYSICAL="s_axi_araddr"/>
            <PORTMAP LOGICAL="ARREADY" PHYSICAL="s_axi_arready"/>
            <PORTMAP LOGICAL="ARVALID" PHYSICAL="s_axi_arvalid"/>
            <PORTMAP LOGICAL="AWADDR" PHYSICAL="s_axi_awaddr"/>
            <PORTMAP LOGICAL="AWREADY" PHYSICAL="s_axi_awready"/>
            <PORTMAP LOGICAL="AWVALID" PHYSICAL="s_axi_awvalid"/>
            <PORTMAP LOGICAL="BREADY" PHYSICAL="s_axi_bready"/>
            <PORTMAP LOGICAL="BRESP" PHYSICAL="s_axi_bresp"/>
            <PORTMAP LOGICAL="BVALID" PHYSICAL="s_axi_bvalid"/>
            <PORTMAP LOGICAL="RDATA" PHYSICAL="s_axi_rdata"/>
            <PORTMAP LOGICAL="RREADY" PHYSICAL="s_axi_rready"/>
            <PORTMAP LOGICAL="RRESP" PHYSICAL="s_axi_rresp"/>
            <PORTMAP LOGICAL="RVALID" PHYSICAL="s_axi_rvalid"/>
            <PORTMAP LOGICAL="WDATA" PHYSICAL="s_axi_wdata"/>
            <PORTMAP LOGICAL="WREADY" PHYSICAL="s_axi_wready"/>
            <PORTMAP LOGICAL="WSTRB" PHYSICAL="s_axi_wstrb"/>
            <PORTMAP LOGICAL="WVALID" PHYSICAL="s_axi_wvalid"/>
          </PORTMAPS>
        </BUSINTERFACE>
        <BUSINTERFACE BUSNAME="v_tc_0_vtiming_out" NAME="vtiming_out" TYPE="INITIATOR" VLNV="xilinx.com:interface:video_timing:2.0">
          <PORTMAPS>
            <PORTMAP LOGICAL="ACTIVE_VIDEO" PHYSICAL="active_video_out"/>
            <PORTMAP LOGICAL="HBLANK" PHYSICAL="hblank_out"/>
            <PORTMAP LOGICAL="HSYNC" PHYSICAL="hsync_out"/>
            <PORTMAP LOGICAL="VBLANK" PHYSICAL="vblank_out"/>
            <PORTMAP LOGICAL="VSYNC" PHYSICAL="vsync_out"/>
          </PORTMAPS>
        </BUSINTERFACE>
      </BUSINTERFACES>
    </MODULE>
    <MODULE COREREVISION="1" FULLNAME="/xlconcat_0" HWVERSION="2.1" INSTANCE="xlconcat_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="xlconcat" VLNV="xilinx.com:ip:xlconcat:2.1">
      <DOCUMENTS/>
      <PARAMETERS>
        <PARAMETER NAME="IN0_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN1_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN2_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN3_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN4_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN5_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN6_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN7_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN8_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN9_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN10_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN11_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN12_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN13_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN14_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN15_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN16_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN17_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN18_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN19_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN20_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN21_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN22_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN23_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN24_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN25_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN26_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN27_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN28_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN29_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN30_WIDTH" VALUE="1"/>
        <PARAMETER NAME="IN31_WIDTH" VALUE="1"/>
        <PARAMETER NAME="dout_width" VALUE="2"/>
        <PARAMETER NAME="NUM_PORTS" VALUE="2"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_xlconcat_0_0"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
      </PARAMETERS>
      <PORTS>
        <PORT DIR="I" LEFT="0" NAME="In0" RIGHT="0" SIGIS="undef" SIGNAME="axi_vdma_0_mm2s_introut">
          <CONNECTIONS>
            <CONNECTION INSTANCE="axi_vdma_0" PORT="mm2s_introut"/>
          </CONNECTIONS>
        </PORT>
        <PORT DIR="I" LEFT="0" NAME="In1" RIGHT="0" SIGIS="undef"/>
        <PORT DIR="O" LEFT="1" NAME="dout" RIGHT="0" SIGIS="undef" SIGNAME="xlconcat_0_dout">
          <CONNECTIONS>
            <CONNECTION INSTANCE="processing_system7_0" PORT="IRQ_F2P"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES/>
    </MODULE>
    <MODULE COREREVISION="5" FULLNAME="/xlconstant_0" HWVERSION="1.1" INSTANCE="xlconstant_0" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="xlconstant" VLNV="xilinx.com:ip:xlconstant:1.1">
      <DOCUMENTS/>
      <PARAMETERS>
        <PARAMETER NAME="CONST_WIDTH" VALUE="1"/>
        <PARAMETER NAME="CONST_VAL" VALUE="0x1"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_xlconstant_0_0"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
      </PARAMETERS>
      <PORTS>
        <PORT DIR="O" LEFT="0" NAME="dout" RIGHT="0" SIGIS="undef" SIGNAME="xlconstant_0_dout">
          <CONNECTIONS>
            <CONNECTION INSTANCE="External_Ports" PORT="lcd_rst"/>
            <CONNECTION INSTANCE="External_Ports" PORT="lcd_bl"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES/>
    </MODULE>
    <MODULE COREREVISION="5" FULLNAME="/xlconstant_1" HWVERSION="1.1" INSTANCE="xlconstant_1" IPTYPE="PERIPHERAL" IS_ENABLE="1" MODCLASS="PERIPHERAL" MODTYPE="xlconstant" VLNV="xilinx.com:ip:xlconstant:1.1">
      <DOCUMENTS/>
      <PARAMETERS>
        <PARAMETER NAME="CONST_WIDTH" VALUE="1"/>
        <PARAMETER NAME="CONST_VAL" VALUE="0x1"/>
        <PARAMETER NAME="Component_Name" VALUE="utg_xlconstant_0_1"/>
        <PARAMETER NAME="EDK_IPTYPE" VALUE="PERIPHERAL"/>
      </PARAMETERS>
      <PORTS>
        <PORT DIR="O" LEFT="0" NAME="dout" RIGHT="0" SIGIS="undef" SIGNAME="xlconstant_1_dout">
          <CONNECTIONS>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="aclken"/>
            <CONNECTION INSTANCE="v_tc_0" PORT="clken"/>
            <CONNECTION INSTANCE="v_axi4s_vid_out_0" PORT="vid_io_out_ce"/>
          </CONNECTIONS>
        </PORT>
      </PORTS>
      <BUSINTERFACES/>
    </MODULE>
  </MODULES>

</EDKSYSTEM>
