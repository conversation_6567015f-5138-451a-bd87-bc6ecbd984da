Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Aug  4 14:38:11 2025
| Host         : wantong running 64-bit major release  (build 9200)
| Command      : report_control_sets -verbose -file UGT_control_sets_placed.rpt
| Design       : UGT
| Device       : xc7z020
------------------------------------------------------------------------------------

Control Set Information

Table of Contents
-----------------
1. Summary
2. Histogram
3. Flip-Flop Distribution
4. Detailed Control Set Information

1. Summary
----------

+----------------------------------------------------------+-------+
|                          Status                          | Count |
+----------------------------------------------------------+-------+
| Number of unique control sets                            |   428 |
| Unused register locations in slices containing registers |  1034 |
+----------------------------------------------------------+-------+


2. Histogram
------------

+--------+--------------+
| Fanout | Control Sets |
+--------+--------------+
|      1 |           17 |
|      2 |            4 |
|      3 |            5 |
|      4 |           44 |
|      5 |           27 |
|      6 |            9 |
|      7 |            6 |
|      8 |          113 |
|      9 |           12 |
|     10 |            5 |
|     11 |            1 |
|     12 |           16 |
|     13 |            5 |
|     14 |            5 |
|     15 |            2 |
|    16+ |          157 |
+--------+--------------+


3. Flip-Flop Distribution
-------------------------

+--------------+-----------------------+------------------------+-----------------+--------------+
| Clock Enable | Synchronous Set/Reset | Asynchronous Set/Reset | Total Registers | Total Slices |
+--------------+-----------------------+------------------------+-----------------+--------------+
| No           | No                    | No                     |            2213 |          669 |
| No           | No                    | Yes                    |              42 |           15 |
| No           | Yes                   | No                     |             761 |          320 |
| Yes          | No                    | No                     |            2639 |          660 |
| Yes          | No                    | Yes                    |              34 |           11 |
| Yes          | Yes                   | No                     |            3853 |         1135 |
+--------------+-----------------------+------------------------+-----------------+--------------+


4. Detailed Control Set Information
-----------------------------------

+-----------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+------------------+----------------+
|                  Clock Signal                 |                                                                                                         Enable Signal                                                                                                         |                                                                                                        Set/Reset Signal                                                                                                        | Slice Load Count | Bel Load Count |
+-----------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+------------------+----------------+
|  ADC_DCLK_IBUF_BUFG                           |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/adc_inst/p_0_in                                                                                                                                                                                        |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_rsplitter.gen_rthread_loop[0].r_split_fifo/gen_srls[0].srl_nx1/shift_qual                                                     |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/r_reg/skid_buffer[1122]_i_1_n_0                                                                                                                                        |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_w_cmd_fifo.w_cmd_fifo/gen_srls[1].srl_nx1/shift_qual                                                                                                     |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/first_xfer                                                                                                          |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/s_sc_areset_pipe                                                                                                                                                              |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/s_sc_areset_pipe                                                                                                                                                               |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/r_reg/m_vector_i                                                                                                                                                       |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_rsplitter.gen_rthread_loop[0].r_split_fifo/gen_srls[0].srl_nx1/shift_qual                                                      |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_wsplitter.gen_wthread_loop[0].b_split_fifo/gen_srls[0].srl_nx1/shift_qual                                                     |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/OMIT_DRE_CNTL.I_DRE_CNTL_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/CNTR_INCR_DECR_ADDN_F_I/FIFO_Full_reg          |                                                                                                                                                                                                                                |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/s_sc_areset_pipe                                                                                                                                                             |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/s_sc_areset_pipe                                                                                                                                                             |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/s_sc_areset_pipe                                                                                                                                                              |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/s_sc_areset_pipe                                                                                                                                                              |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_cmd_fifo/gen_pipelined.mesg_reg                                                                                                  | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_cmd_fifo/gen_pipelined.mesg_reg[10]_i_1__2_n_0                                                                                    |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/s_sc_areset_pipe                                                                                                                                                              |                1 |              1 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_push_input_reg11_out                                                                                                                | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_input_cache_type_reg0                                                                                                                |                1 |              2 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_r_cmd_fifo.r_cmd_fifo/gen_pipelined.mesg_reg                                                                                                             |                                                                                                                                                                                                                                |                1 |              2 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_r_cmd_fifo.r_cmd_fifo/gen_srls[15].srl_nx1/shift_qual                                                                                                    |                                                                                                                                                                                                                                |                1 |              2 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.b_channel_0/bid_fifo_0/bresp_push                                                                                                 |                                                                                                                                                                                                                                |                1 |              2 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/SR[0]                                                                                                                                                                                |                1 |              3 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                   |                1 |              3 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_CMD_STATUS/GEN_INCLUDE_STATUS_FIFO.I_STS_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/DYNSHREG_F_I/sig_wr_fifo                                 |                                                                                                                                                                                                                                |                1 |              3 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/rst_i                                     |                1 |              3 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/rst_i |                2 |              3 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/rst_ps7_0_100M1/U0/EXT_LPF/lpf_int                                                                                                                                                                                    |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/cmd_fifo/fifoaddr[3]_i_1__0_n_0                                                                                       | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/sig_token_cntr[3]_i_1_n_0                                                                                              | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RESET/SS[0]                                                                                                                                       |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RD_DATA_CNTL/sig_push_coelsc_reg                                                                                                                 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RD_DATA_CNTL/sig_coelsc_reg_full_i_1_n_0                                                                                                          |                1 |              4 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_TC_TOP/GEN_GENERATOR.U_TC_GEN/p_3_in                                                                                                                                                                      |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_MIN_FOR_ASYNC.axis_min_count[3]_i_2_n_0                                                                                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_MIN_FOR_ASYNC.AXIS_CLR_CDC_I/SR[0]                                                                                                                          |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_MIN_FOR_ASYNC.prmry_min_count[3]_i_2_n_0                                                                                                                   | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_MIN_FOR_ASYNC.LITE_MIN_CDC_I/SR[0]                                                                                                                          |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_MIN_FOR_ASYNC.lite_min_count[3]_i_2_n_0                                                                                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_MIN_FOR_ASYNC.LITE_CLR_CDC_I/SR[0]                                                                                                                          |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/cmd_fifo/gen_pipelined.mesg_reg                                                                                       |                                                                                                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/ar.ar_pipe/aresetn_d_reg[1]_inv_0                                                                                              |                3 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/clk_map/psr_aclk/U0/EXT_LPF/lpf_int                                                                                                                                                                     |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/ar_reg_slice/E[0]                                                                                                                  | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/cmd_fifo/gen_srls[11].srl_nx1/shift_qual                                                                              |                                                                                                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/E[0]                                                                                                                  | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/areset_r                                                                                                                                                      |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc/inst/clk_map/psr_aclk/U0/EXT_LPF/lpf_int                                                                                                                                                                      |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/aw.aw_pipe/aresetn_d_reg[0]_0                                                                                                  |                4 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_arready0                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/w_payld_push_d                                                                                                        | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.ip2axi_rddata_captured_d1[30]_i_1_n_0                                                          |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_transaction_regulator/inst/gen_endpoint.gen_w_singleorder.w_singleorder/gen_id_fifo.singleorder_fifo/fifoaddr[3]_i_1_n_0                                                        | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_transaction_regulator/inst/areset                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_transaction_regulator/inst/gen_endpoint.gen_r_singleorder.r_singleorder/gen_id_fifo.singleorder_fifo/fifoaddr[3]_i_1__0_n_0                                                     | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_transaction_regulator/inst/areset                                                                                                                                                |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/axi_awready0                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/rst_ps7_0_100M/U0/EXT_LPF/lpf_int                                                                                                                                                                                     |                3 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_cmd_fifo/w_payld_push68_out                                                                                                      | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_cmd_fifo/gen_pipelined.mesg_reg_reg[10]_0                                                                                         |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_r_cmd_fifo.r_cmd_fifo/fifoaddr[3]_i_1__2_n_0                                                                                                             | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_cmd_fifo/fifoaddr[3]_i_1__3_n_0                                                                               | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_cmd_fifo/gen_pipelined.mesg_reg                                                                                                  |                                                                                                                                                                                                                                |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/cmd_fifo/gen_pipelined.state_reg[1]_0[0]                                                                              |                                                                                                                                                                                                                                |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/cmd_fifo/gen_pipelined.mesg_reg                                                                    |                                                                                                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_wsplitter.gen_wthread_loop[0].b_split_fifo/fifoaddr[3]_i_1__0_n_0                                                             | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/next_rom_addr                                                                                                                                                                        | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/rom_addr[3]_i_1_n_0                                                                                                                                                                   |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/cmd_fifo/E[0]                                                                                      |                                                                                                                                                                                                                                |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/state_count[3]_i_1_n_0                                                                                                                                                               |                                                                                                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_w_cmd_fifo.w_cmd_fifo/fifoaddr[3]_i_1__3_n_0                                                                                                             | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/cmd_fifo/gen_pipelined.state_reg[0]_0[0]                                                                              | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_rsplitter.gen_rthread_loop[0].r_split_fifo/fifoaddr[3]_i_1__1_n_0                                                             | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_wsplitter.w_split_fifo/fifoaddr[3]_i_1_n_0                                                                                    | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/cmd_fifo/fifoaddr[3]_i_1__2_n_0                                                                    | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/cmd_fifo/gen_srls[11].srl_nx1/shift_qual                                                           |                                                                                                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_r_cmd_fifo.r_cmd_fifo/gen_srls[15].srl_nx1/shift_qual                                                                                                     |                                                                                                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_r_cmd_fifo.r_cmd_fifo/fifoaddr[3]_i_1__0_n_0                                                                                                              | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                   |                2 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/exit_inst/gen_r_cmd_fifo.r_cmd_fifo/gen_pipelined.mesg_reg                                                                                                              |                                                                                                                                                                                                                                |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_rsplitter.gen_rthread_loop[0].r_split_fifo/fifoaddr[3]_i_1_n_0                                                                 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                   |                1 |              4 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_wsplitter.w_split_fifo/gen_srls[4].srl_nx1/shift_qual                                                                         |                                                                                                                                                                                                                                |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/fifoaddr                                                                                                              | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/FSM_sequential_fsm_state_i_1_n_0                                                                                                                                                      |                1 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.ADDR32.I_MM2S_DMA_MNGR/E[0]                                                                                                                                                          | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_REGISTER_MODULE_I/I_DMA_REGISTER/prmry_resetn_i_reg[0]                                                                                                                           |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[2]                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/aw_sreg/E[0]                                                                                                                                                           | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                  |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_wsplitter.w_split_fifo/gen_pipelined.mesg_reg                                                                                 |                                                                                                                                                                                                                                |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/Q[0]                                      |                3 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_REGISTER_MODULE_I/I_DMA_REGISTER/MM2S_ERR_FOR_IRQ.frm_store_i[4]_i_1_n_0                                                                                                        | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                1 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/cmd_fifo/last_beat_reg                                                                             | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/aw_cmd_reg/E[0]                                                                                                                   | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/ar_sreg/E[0]                                                                                                                                                           | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                  |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/fifoaddr                                                                                           | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                4 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/E[0]                                                                                               | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/areset_d1                                                                                                                             |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                   |                3 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/splitter_inst/gen_no_wsplitter.gen_endpoint_woffset.gen_wbypass_offset_fifo.wbypass_offset_fifo/gen_pipelined.mesg_reg_0                                      |                                                                                                                                                                                                                                |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_cmd_fifo/gen_srls[10].srl_nx1/shift_qual                                                                                         |                                                                                                                                                                                                                                |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/E[0]                                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/E[0]                                                                                                                   | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/transaction_fifo_0/cnt_read[4]_i_1__0_n_0                                                                             | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/areset_d1                                                                                                                             |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/E[0]                                                                                                                    | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                   |                3 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RD_STATUS_CNTLR/sig_push_rd_sts_reg                                                                                                              | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_CMD_STATUS/GEN_INCLUDE_STATUS_FIFO.I_STS_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/sig_inhibit_rdy_n_reg                                     |                1 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/ar_sreg/E[0]                                                                                                                                                            | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                   |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/write_index0                                                                                                                                                                         | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/write_index[4]_i_1_n_0                                                                                                                                                                |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/splitter_inst/gen_no_wsplitter.gen_endpoint_woffset.gen_wbypass_offset_fifo.wbypass_offset_fifo/gen_srls[13].srl_nx1/shift_qual                               |                                                                                                                                                                                                                                |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/cmd_fifo/last_beat_reg[0]                                                                                             | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                2 |              5 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/w_sreg/E[0]                                                                                                                                                            | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                  |                2 |              5 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/rst_ps7_0_100M1/U0/SEQ/seq_cnt_en                                                                                                                                                                                    | u1/utg_i/rst_ps7_0_100M1/U0/SEQ/SEQ_COUNTER/clear                                                                                                                                                                              |                1 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/axi_awvalid_i_1_n_0                                                                                                                                                                   |                3 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/clk_map/psr_aclk/U0/SEQ/seq_cnt_en                                                                                                                                                                     | u1/utg_i/axi_smc1/inst/clk_map/psr_aclk/U0/SEQ/SEQ_COUNTER/clear                                                                                                                                                               |                1 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/sig_awvalid_detected__0                                                                                                                         | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/hrd_resetn_i_reg_0[0]                                                                                                                                                                      |                2 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/sig_arvalid_detected__0                                                                                                                         | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/hrd_resetn_i_reg_0[0]                                                                                                                                                                      |                1 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/clk_map/psr_aclk/U0/SEQ/seq_cnt_en                                                                                                                                                                      | u1/utg_i/axi_smc/inst/clk_map/psr_aclk/U0/SEQ/SEQ_COUNTER/clear                                                                                                                                                                |                1 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_transaction_regulator/inst/areset                                                                                                                                                |                4 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/rst_ps7_0_100M/U0/SEQ/seq_cnt_en                                                                                                                                                                                     | u1/utg_i/rst_ps7_0_100M/U0/SEQ/SEQ_COUNTER/clear                                                                                                                                                                               |                1 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RD_DATA_CNTL/GEN_DATA_CNTL_FIFO.I_DATA_CNTL_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/CNTR_INCR_DECR_ADDN_F_I/sig_push_dqual_reg            | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RD_DATA_CNTL/GEN_DATA_CNTL_FIFO.I_DATA_CNTL_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/CNTR_INCR_DECR_ADDN_F_I/sig_dqual_reg_empty_reg_0      |                2 |              6 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                4 |              7 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_4[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                4 |              7 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.AXI_LITE_IPIF_I/I_SLAVE_ATTACHMENT/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                    |                1 |              7 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/DADDR[6]_i_2_n_0                                                                                                                                                                     | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/DADDR[6]_i_1_n_0                                                                                                                                                                      |                1 |              7 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_rd_addrb/rd_addrb_incr                                                | u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/m_sc_areset_r                                                                                                                                                 |                3 |              7 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_rd_addrb/rd_addrb_incr                                                | u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/m_sc_areset_r                                                                                                                                                 |                4 |              7 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg13[15]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg11[15]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg11[7]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                4 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg12[15]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg12[23]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg3[23]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg5[15]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg13[7]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg14[23]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg15[7]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg2[15]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg3[15]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg3[31]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg14[15]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg1[7]_i_1_n_0                                                                                                                                                                  | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg2[31]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg2[23]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg3[23]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg5[7]_i_1_n_0                                                                                                                                                          | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg4[31]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg5[23]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg3[15]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg5[7]_i_1_n_0                                                                                                                                                                  | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg4[15]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg2[7]_i_1_n_0                                                                                                                                                          | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg4[7]_i_1_n_0                                                                                                                                                          | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg6[15]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_TC_TOP/GEN_GENERATOR.U_TC_GEN/reset                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg7[31]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg7[7]_i_1_n_0                                                                                                                                                          | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg6[23]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg6[31]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg7[23]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg7[15]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg3[23]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg6[7]_i_1_n_0                                                                                                                                                          | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/p_1_in[7]                                                                                                                                                                    | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/p_1_in[15]                                                                                                                                                                   | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/p_1_in[23]                                                                                                                                                                   | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/p_1_in[31]                                                                                                                                                                   | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg5[31]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[31]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[23]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg2[23]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg7[31]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                3 |              8 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/fifo_sof_cnt                                                                                                                                                                        | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/vtg_sof_cnt0                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg2[15]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg7[23]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg7[7]_i_1_n_0                                                                                                                                                               | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                3 |              8 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/vtg_sof_dly                                                                                                                                                                         | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/vtg_sof_cnt0                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg3[7]_i_1_n_0                                                                                                                                                               | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg4[23]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[7]_i_1_n_0                                                                                                                                                               | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[23]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg4[23]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg3[15]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg4[7]_i_1_n_0                                                                                                                                                               | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg5[15]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[15]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg4[31]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg6[31]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg2[31]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg5[23]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/wr_wea                                                                                    |                                                                                                                                                                                                                                |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/gen_endpoint.decerr_slave_inst/gen_axi.gen_read.read_cnt[7]_i_1_n_0                                                                                                    | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                  |                3 |              8 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][38]_0[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                5 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/wr_wea                                                                                    |                                                                                                                                                                                                                                |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg3[7]_i_1_n_0                                                                                                                                                          | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg5[15]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg4[15]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/p_1_in[5]                                                                                                                                                                         | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[15]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg5[31]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg2[7]_i_1_n_0                                                                                                                                                               | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg5[7]_i_1_n_0                                                                                                                                                               | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg7[15]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/p_1_in[15]                                                                                                                                                                        | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg5[23]_i_1_n_0                                                                                                                                                              | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/p_1_in[31]                                                                                                                                                                        | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_cmd_fifo/E[0]                                                                                                                    | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                5 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg3[31]_i_1_n_0                                                                                                                                                         | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_cmd_fifo/gen_pipelined.mesg_reg                                                                               |                                                                                                                                                                                                                                |                5 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_cmd_fifo/gen_srls[15].srl_nx1/shift_qual                                                                      |                                                                                                                                                                                                                                |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/p_1_in[23]                                                                                                                                                                        | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg12[31]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg2[7]_i_1_n_0                                                                                                                                                                  | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/gen_endpoint.decerr_slave_inst/gen_axi.gen_read.read_cnt[7]_i_1_n_0                                                                                                     | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                   |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg1[15]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg14[31]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/p_1_in[0]                                                                                                                                                                                  | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/p_1_in[23]                                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/p_1_in[15]                                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/p_1_in[31]                                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg15[15]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                4 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_AXI_DMA_INTRPT/E[0]                                                                                                                                                                                  | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_REGISTER_MODULE_I/I_DMA_REGISTER/ENABLE_DMACR_DELAY_CNTR.dmacr_i_reg[26]_0[0]                                                                                                    |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.ADDR32.I_MM2S_DMA_MNGR/I_SM/cmnds_queued[7]_i_2_n_0                                                                                                                                  | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/err_i_reg[0]                                                                                                                                                    |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg1[23]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[0]                                                    | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/SS[0]                                                                   |                1 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg15[23]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg15[31]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg5[31]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg3[7]_i_1_n_0                                                                                                                                                                  | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_REGISTER_MODULE_I/I_DMA_REGISTER/GEN_NO_INTERNAL_GENLOCK.DS_GEN_DMACR_REGISTER.dmacr_i_reg[13]_0[0]                                                                             | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                4 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg1[31]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg14[7]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/E[0]                                                                                               | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg13[23]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.b_channel_0/mhandshake_r                                                                                                          | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.b_channel_0/bid_fifo_0/SR[0]                                                                                                       |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg13[31]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg11[31]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg12[7]_i_1_n_0                                                                                                                                                                 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.AXI_LITE_IPIF_I/I_SLAVE_ATTACHMENT/bus2ip_addr_i[8]_i_1_n_0                                                                                                               | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.AXI_LITE_IPIF_I/I_SLAVE_ATTACHMENT/rst                                                                                                                                     |                3 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/i___2/slv_reg11[23]_i_1_n_0                                                                                                                                                                | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                2 |              8 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RD_DATA_CNTL/GEN_DATA_CNTL_FIFO.I_DATA_CNTL_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/CNTR_INCR_DECR_ADDN_F_I/E[0]                          | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RESET/SS[0]                                                                                                                                       |                5 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/w_fill_mask_reg[3][0]                                                                                                 |                                                                                                                                                                                                                                |                3 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/w_fill_mask_reg[3][2]                                                                                                 |                                                                                                                                                                                                                                |                3 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/w_fill_mask_reg[3][1]                                                                                                 |                                                                                                                                                                                                                                |                3 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_payld_fifo/cmd_fifo/last_beat_reg_0[0]                                                                        | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                4 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/cmd_fifo/E[0]                                                                                                         | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                5 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/s_ready_i_reg_1                                                                                                       | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/w_accum_continue_d_reg                                                                                                 |                3 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_REGISTER_MODULE_I/I_DMA_REGISTER/p_4_out                                                                                                                                         |                4 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_wsplitter.w_split_fifo/E[0]                                                                                                   | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |                4 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/Q[0]  |                6 |              9 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/SR[0]                                                                    |                3 |              9 |
|  ADC_DCLK_IBUF_BUFG                           | u1/utg_i/AXI_ADC_0/inst/adc_inst/ultrasound_st_i_1_n_0                                                                                                                                                                        | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                4 |              9 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/gen_thread_loop[0].r_cmd_fifo/E[0]                                                                                                 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |                5 |             10 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.write_ack_e1_i_1_n_0                                                                                                                                                       |                2 |             10 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RD_DATA_CNTL/GEN_DATA_CNTL_FIFO.I_DATA_CNTL_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/DYNSHREG_F_I/sig_wr_fifo                              |                                                                                                                                                                                                                                |                2 |             10 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.AXI_LITE_IPIF_I/I_SLAVE_ATTACHMENT/clear                                                                                                                                   |                3 |             10 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/areset_d1                                                                                                                             |                6 |             10 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/wrst_busy                                                                |                5 |             11 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.AXI_LITE_IPIF_I/I_SLAVE_ATTACHMENT/rst                                                                                                                                     |                5 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_si_handler/inst_arb_stall_late/s_sc_valid                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/areset_r                                                                                                                                                      |                5 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/aw_cmd_fsm_0/state_reg[1]_3[0]                                                                                       |                                                                                                                                                                                                                                |                5 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.ar_channel_0/ar_cmd_fsm_0/FSM_sequential_state_reg[0]_0[0]                                                                        |                                                                                                                                                                                                                                |                4 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_RESET_FOR_ASYNC.HARD_RESET_CDC_I/prmry_reset2                                                                                                               |                3 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.ar_channel_0/ar_cmd_fsm_0/sel_first_reg[0]                                                                                        |                                                                                                                                                                                                                                |                3 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_init_reg                                                                                                                             |                4 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/cmd_translator_0/incr_cmd_0/axaddr_incr[11]_i_1_n_0                                                                  |                                                                                                                                                                                                                                |                3 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_si_handler/inst_arb_stall_late/s_sc_valid                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/areset_r                                                                                                                                                      |                5 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_normal_area.upsizer_valid                                                                                       | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/areset_r                                                                                                                                                      |                5 |             12 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_TC_TOP/GEN_GENERATOR.U_TC_GEN/v_count058_out                                                                                                                                                             | u1/utg_i/v_tc_0/U0/U_TC_TOP/GEN_GENERATOR.U_TC_GEN/v_count[0]_i_1_n_0                                                                                                                                                          |                3 |             12 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/vtg_ce                                                                                                                                                                              | u1/utg_i/v_tc_0/U0/U_TC_TOP/GEN_GENERATOR.U_TC_GEN/h_count[0]_i_1_n_0                                                                                                                                                          |                3 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_si_handler/inst_arb_stall_late/s_sc_valid                                                                                                                              | u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/areset_r                                                                                                                                                     |                4 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_si_handler/inst_arb_stall_late/s_sc_valid                                                                                                                              | u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/areset_r                                                                                                                                                     |                5 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_si_handler/inst_arb_stall_late/s_sc_valid                                                                                                                               | u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/areset_r                                                                                                                                                      |                5 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_si_handler/inst_arb_stall_late/s_sc_valid                                                                                                                                | u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/areset_r                                                                                                                                                       |                5 |             12 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.ADDR32.I_MM2S_DMA_MNGR/I_SM/vert_count[12]_i_1_n_0                                                                                                                                   | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                4 |             13 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/SR[0]                                                                                                                                           |                5 |             13 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[3]                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                4 |             13 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/r_push_r                                                                                                              |                                                                                                                                                                                                                                |                4 |             13 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/fifo_eol_re_dly                                                                                                                                                                     | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/fifo_eol_cnt[0]_i_1_n_0                                                                                                                                                              |                4 |             13 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.vsize_counter[12]_i_2_n_0                                                                                                                       | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/GEN_FREE_RUN_MODE.frame_sync_out_reg[0]                                                                                                                         |                5 |             14 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |                6 |             14 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/SR[0]                                                                                      |                4 |             14 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/b.b_pipe/s_ready_i_reg_0                                                                                                      |                                                                                                                                                                                                                                |                3 |             14 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/b.b_pipe/p_1_in                                                                                                               |                                                                                                                                                                                                                                |                2 |             14 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/areset                                                                                                                                                                  |                9 |             15 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_sm_ld_calc2_reg_ns                                                                                                                  | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_init_reg                                                                                                                             |                6 |             15 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[4]                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                3 |             16 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/next_di                                                                                                                                                                              |                                                                                                                                                                                                                                |                4 |             16 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_addr_cntr_im0_msh[0]_i_1_n_0                                                                                                        | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_init_reg                                                                                                                             |                4 |             16 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_rsplitter.arsplit_len_last_d[3]_i_1_n_0                                                                                       |                                                                                                                                                                                                                                |                9 |             16 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_sm_ld_xfer_reg_ns                                                                                                                   | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_init_reg                                                                                                                             |                4 |             16 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_wsplitter.awsplit_len_last_d[3]_i_1_n_0                                                                                       |                                                                                                                                                                                                                                |                6 |             16 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[5]                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                2 |             16 |
|  ADC_DCLK_IBUF_BUFG                           | u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en                                                                                                                                                                                   | u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_start_pos                                                                                                                                                                                 |                4 |             16 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/aw_cmd_fsm_0/b_push                                                                                                  |                                                                                                                                                                                                                                |                3 |             16 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/vtg_ce                                                                                                                                                                              | u1/utg_i/v_tc_0/U0/U_TC_TOP/GEN_GENERATOR.U_TC_GEN/p_3_in                                                                                                                                                                      |                6 |             17 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.ip2axi_rddata_captured_d1[31]_i_1_n_0                                                          |                6 |             18 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/E[0] | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/Q[0]  |                4 |             19 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/rdp_inst/E[0]          | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/Q[0]  |                6 |             19 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_sm_ld_calc2_reg                                                                                                                     | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_init_reg                                                                                                                             |                6 |             19 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/gen_rsplitter.s_axi_arcache_d                                                                                                      |                                                                                                                                                                                                                                |               10 |             19 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[0]                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                8 |             20 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/rdpp1_inst/E[0]                                                                           | u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.fifo_rd_rst_ic_reg_0                                          |                6 |             21 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |               10 |             21 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_6[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               15 |             22 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/p_0_in1_in                                                                                                                                     | u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/SR[0]                                                                                                                                           |                8 |             23 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/areset                                                                                                                                                                  |               11 |             23 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/ram_wr_en_pf                             | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/Q[0]                                      |                6 |             23 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/rdp_inst/FSM_sequential_gen_fwft.curr_fwft_state_reg[1]    | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/xpm_fifo_rst_inst/Q[0]                                      |                6 |             23 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][36]_2[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                6 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][36]_1[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                5 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][36]_3[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                8 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_11[0]                                                                                                                                  | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                6 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][37]_1[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                8 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_9[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                6 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_8[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                4 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][37]_2[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                7 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][36]_5[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                5 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]_4[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                5 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]_350[0]                                                                                                                                 | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                6 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]_349[0]                                                                                                                                 | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                7 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/AXI4_LITE_INTERFACE.write_ack_int_reg_0[0]                                                                                                                  | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                6 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/AXI4_LITE_INTERFACE.write_ack_int_reg[0]                                                                                                                    | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                4 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]_3[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                8 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.fifo_rd_rst_ic_reg_0                                          |                6 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/E[0]                                                                                                                                                                                | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/SR[0]                                                                                                                                                                                |                4 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_10[0]                                                                                                                                  | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                6 |             24 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][36]_4[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                7 |             24 |
|  ADC_DCLK_IBUF_BUFG                           | u1/utg_i/AXI_ADC_0/inst/adc_inst/adc_sp_cnts[0]_i_1_n_0                                                                                                                                                                       | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |                7 |             25 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_7[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               19 |             25 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_0[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               14 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][36]_0[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                8 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_5[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                5 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_3[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                7 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]_2[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                9 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_2[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                8 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]_0[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               14 |             26 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.ar_channel_0/ar_cmd_fsm_0/E[0]                                                                                                    |                                                                                                                                                                                                                                |               11 |             26 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/s2mm_transfer_bytes_now[6]_i_1_n_0                                                                                                                                                   | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/axi_awvalid_i_1_n_0                                                                                                                                                                   |                7 |             26 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/aw_cmd_fsm_0/E[0]                                                                                                    |                                                                                                                                                                                                                                |               13 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][34]_1[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                7 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/E[0]                                                                                                                                                        | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               12 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][35]_1[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |                7 |             26 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.SYNC2VIDCLK_I/data_sync_reg[2][37]_0[0]                                                                                                                                   | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               12 |             26 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_shelve_d                                                                                                                         |                                                                                                                                                                                                                                |                8 |             27 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/rdp_inst/E[0]                                                                                               | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.fifo_rd_rst_ic_reg_0                                                            |                8 |             27 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/areset                                                                                                                                                         |               11 |             27 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/w_sreg/skid_buffer[2052]_i_1_n_0                                                                                                                                       |                                                                                                                                                                                                                                |               10 |             29 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/w_sreg/m_vector_i                                                                                                                                                      |                                                                                                                                                                                                                                |                9 |             29 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/rom                                                                                                                                                                                   |               11 |             31 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/wr_pntr_plus1_pf_carry                                                  | u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/wrst_busy                                                                |                8 |             31 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.fifo_rd_rst_ic_reg_0                                                            |               13 |             31 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/slv_reg_rden__0                                                                                                                                                                            | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |               28 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/axi_awaddr[31]_i_1_n_0                                                                                                                                                               |                                                                                                                                                                                                                                |                9 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[7]                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |               14 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/AXI_LITE_REG_INTERFACE_I/GEN_AXI_LITE_IF.AXI_LITE_IF_I/GEN_LITE_IS_ASYNC.GEN_MM2S_ONLY_ASYNC_LITE_ACCESS.LITE_WVALID_MM2S_CDC_I/mm2s_axi2ip_wrce[6]                                                    | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                7 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_addr_cntr_lsh_im0[15]_i_1_n_0                                                                                                       | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_init_reg                                                                                                                             |               11 |             32 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/vtg_lag                                                                                                                                                                             | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/sof_ignore0                                                                                                                                                                          |                8 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_push_input_reg11_out                                                                                                                | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_MSTR_PCC/sig_init_reg                                                                                                                             |               11 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/slv_reg_rden__0                                                                                                                                                                   | u1/utg_i/misc_0/inst/misc_v1_0_S00_AXI_inst/u_misc/SR[0]                                                                                                                                                                       |               21 |             32 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.GENR_MUX0/data_sync_reg[2][34]                                                                                                                                             |               10 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.ADDR32.I_MM2S_DMA_MNGR/I_SM/VFLIP_DISABLE.dm_address[31]_i_1_n_0                                                                                                                     | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |                8 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/slv_reg_rden                                                                                                                                                                 | u1/utg_i/axi_dynclk_0/U0/axi_dynclk_S00_AXI_inst/SR[0]                                                                                                                                                                         |               23 |             32 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.AXI_LITE_IPIF_I/I_SLAVE_ATTACHMENT/s_axi_rresp_i                                                                                                                          | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.AXI_LITE_IPIF_I/I_SLAVE_ATTACHMENT/rst                                                                                                                                     |               17 |             33 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/aw_cmd_reg/gen_wsplitter.awtrans_cntr                                                                                             |                                                                                                                                                                                                                                |               12 |             33 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/gen_rsplitter.artrans_cntr                                                                                             |                                                                                                                                                                                                                                |               10 |             33 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/gen_rsplitter.artrans_cntr                                                                                              |                                                                                                                                                                                                                                |               10 |             33 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.I_MSTR_SKID/sig_s_ready_dup                                                                                                      | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/SR[0]                                                                                                                                                           |                7 |             34 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.I_MSTR_SKID/sig_data_reg_out_en                                                                                                  | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/GEN_RESET_FOR_MM2S.RESET_I/SR[0]                                                                                                                                                           |               11 |             34 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.r_channel_0/rd_data_fifo_0/wr_en0                                                                                                 |                                                                                                                                                                                                                                |                9 |             34 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/xbar/inst/gen_sasd.crossbar_sasd_0/addr_arbiter_inst/E[0]                                                                                                                                           |                                                                                                                                                                                                                                |                7 |             35 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum                                                                                                      |                                                                                                                                                                                                                                |                9 |             36 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.gen_upsizer.inst_upsizer/gen_w_ch.accum[bytes][0][userdata][7]_i_1_n_0                                                                       |                                                                                                                                                                                                                                |                9 |             36 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/w_payld_fifo/gen_srls[103].srl_nx1/push                                                                                            |                                                                                                                                                                                                                                |                9 |             36 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/splitter_inst/gen_no_wsplitter.gen_endpoint_woffset.gen_wbypass_offset_fifo.wbypass_offset_fifo/gen_pipelined.state_reg[1]_1[0]                               |                                                                                                                                                                                                                                |               10 |             37 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_RESET/SS[0]                                                                                                                                       |               16 |             38 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/ar_reg_stall/skid_buffer[1144]_i_1_n_0                                                                                                                                 |                                                                                                                                                                                                                                |                8 |             39 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/aw_reg_stall/skid_buffer[1144]_i_1__0_n_0                                                                                                                              |                                                                                                                                                                                                                                |                9 |             39 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/aw_reg_stall/m_vector_i                                                                                                                                                |                                                                                                                                                                                                                                |                9 |             39 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/aw_sreg/m_vector_i                                                                                                                                                     |                                                                                                                                                                                                                                |                7 |             39 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/ar_reg_stall/m_vector_i                                                                                                                                                |                                                                                                                                                                                                                                |                8 |             39 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/aw_sreg/skid_buffer[1144]_i_1__2_n_0                                                                                                                                   |                                                                                                                                                                                                                                |                7 |             39 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/ar_sreg/skid_buffer[1144]_i_1__1_n_0                                                                                                                                   |                                                                                                                                                                                                                                |                7 |             39 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_mmu/inst/ar_sreg/m_vector_i                                                                                                                                                     |                                                                                                                                                                                                                                |                8 |             39 |
|  ADC_DCLK_IBUF_BUFG                           |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/S_AXI_inst/ACTIVE_LOW_BSR_OUT_DFF[0].FDRE_BSR_N                                                                                                                                                        |               15 |             42 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/ar_reg_stall/m_vector_i                                                                                                                                                 |                                                                                                                                                                                                                                |                9 |             42 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/aw_reg_slice/m_vector_i                                                                                                            |                                                                                                                                                                                                                                |               10 |             42 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/ar_reg_stall/skid_buffer[1144]_i_1_n_0                                                                                                                                  |                                                                                                                                                                                                                                |                8 |             42 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/aw_reg_slice/i___2_n_0                                                                                                             |                                                                                                                                                                                                                                |               10 |             42 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_ADDR_CNTL/GEN_ADDR_FIFO.I_ADDR_QUAL_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/DYNSHREG_F_I/sig_wr_fifo                                      |                                                                                                                                                                                                                                |                6 |             42 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/ar_sreg/m_vector_i                                                                                                                                                      |                                                                                                                                                                                                                                |                9 |             43 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/ar_sreg/skid_buffer[1144]_i_1__0_n_0                                                                                                                                    |                                                                                                                                                                                                                                |                8 |             43 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/ar_reg_slice/m_vector_i                                                                                                            |                                                                                                                                                                                                                                |               12 |             43 |
|  ADC_DCLK_IBUF_BUFG                           |                                                                                                                                                                                                                               | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/wrst_busy                                                                                  |               15 |             43 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_entry_pipeline/s00_si_converter/inst/converter.wrap_narrow_inst/ar_reg_slice/i___2_n_0                                                                                                             |                                                                                                                                                                                                                                |               11 |             43 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_ADDR_CNTL/GEN_ADDR_FIFO.I_ADDR_QUAL_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/CNTR_INCR_DECR_ADDN_F_I/sig_halt_reg_reg                      | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_ADDR_CNTL/sig_next_addr_reg[31]_i_1_n_0                                                                                                           |                7 |             45 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/r.r_pipe/s_ready_i_reg_0                                                                                                      |                                                                                                                                                                                                                                |                9 |             47 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/r.r_pipe/p_1_in                                                                                                               |                                                                                                                                                                                                                                |               10 |             47 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.ADDR32.I_MM2S_DMA_MNGR/I_SM/GEN_NORMAL_DM_COMMAND.cmnd_data[63]_i_1_n_0                                                                                                              | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |               12 |             48 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/WR.aw_channel_0/aw_cmd_fsm_0/m_valid_i_reg[0]                                                                                        |                                                                                                                                                                                                                                |               13 |             48 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/RD.ar_channel_0/ar_cmd_fsm_0/FSM_sequential_state_reg[1]_1[0]                                                                        |                                                                                                                                                                                                                                |                8 |             48 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/ar.ar_pipe/s_ready_i_reg_0                                                                                                    |                                                                                                                                                                                                                                |               10 |             48 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/ps7_0_axi_periph/s00_couplers/auto_pc/inst/gen_axilite.gen_b2s_conv.axilite_b2s/SI_REG/aw.aw_pipe/s_ready_i_reg_0                                                                                                    |                                                                                                                                                                                                                                |               11 |             48 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/ar_reg/skid_buffer[1144]_i_1__1_n_0                                                                                                                                    |                                                                                                                                                                                                                                |               10 |             49 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/ar_reg/skid_buffer[1144]_i_1__0_n_0                                                                                                                                     |                                                                                                                                                                                                                                |                9 |             49 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/ar_reg/m_vector_i                                                                                                                                                       |                                                                                                                                                                                                                                |                8 |             49 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/aw_reg/m_vector_i                                                                                                                                                      |                                                                                                                                                                                                                                |               10 |             49 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/aw_reg/skid_buffer[1144]_i_1__2_n_0                                                                                                                                    |                                                                                                                                                                                                                                |               10 |             49 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/ar_reg/m_vector_i                                                                                                                                                      |                                                                                                                                                                                                                                |               10 |             49 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_CMD_STATUS/I_CMD_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/FIFO_Full_reg_0[0]                                                               | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_REGISTER_MODULE_I/I_DMA_REGISTER/halted_reg_2[0]                                                                                                                                 |               11 |             50 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/I_CMD_STATUS/I_CMD_FIFO/USE_SRL_FIFO.I_SYNC_FIFO/I_SRL_FIFO_RBU_F/DYNSHREG_F_I/sel                                                                 |                                                                                                                                                                                                                                |                7 |             50 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |               20 |             52 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/hrd_resetn_i_reg_0[0]                                                                                                                                                                      |               23 |             55 |
|  ADC_DCLK_IBUF_BUFG                           | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/E[0]                                                                                      | u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/wrst_busy                                                                                  |               16 |             57 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/i___2_n_0                                                                                                              |                                                                                                                                                                                                                                |               16 |             59 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/aw_cmd_reg/i___2_n_0                                                                                                              |                                                                                                                                                                                                                                |               11 |             59 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/aw_cmd_reg/m_vector_i                                                                                                             |                                                                                                                                                                                                                                |               15 |             59 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/m_vector_i                                                                                                             |                                                                                                                                                                                                                                |               22 |             59 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/m_vector_i                                                                                                              |                                                                                                                                                                                                                                |               21 |             62 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/splitter_inst/gen_axi3.splitter_inst/ar_cmd_reg/i___2_n_0                                                                                                               |                                                                                                                                                                                                                                |               14 |             62 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/r_reg/skid_buffer[1122]_i_1_n_0                                                                                                                                         |                                                                                                                                                                                                                                |               13 |             67 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/r_sreg/skid_buffer[1122]_i_1_n_0                                                                                                                                        |                                                                                                                                                                                                                                |               13 |             67 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/m00_exit_pipeline/m00_exit/inst/r_reg/m_vector_i                                                                                                                                                        |                                                                                                                                                                                                                                |               11 |             67 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_entry_pipeline/s00_mmu/inst/r_sreg/m_vector_i                                                                                                                                                       |                                                                                                                                                                                                                                |               26 |             67 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_rd_addrb/rd_addrb_incr                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/m_sc_areset_r                                                                                                                                                |               15 |             69 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_rd_addrb/rd_addrb_incr                                               | u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/m_sc_areset_r                                                                                                                                                |               17 |             69 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_rd_addrb/rd_addrb_incr                                                | u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/m_sc_areset_r                                                                                                                                                 |               15 |             72 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/w_reg/m_vector_i                                                                                                                                                       |                                                                                                                                                                                                                                |               17 |             73 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/m00_exit_pipeline/m00_exit/inst/w_reg/skid_buffer[2056]_i_1_n_0                                                                                                                                        |                                                                                                                                                                                                                                |               15 |             73 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_rd_addrb/rd_addrb_incr                                                 | u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/m_sc_areset_r                                                                                                                                                  |               14 |             73 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_rd_addrb/rd_addrb_incr                                                | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/m_sc_areset_r                                                                                                                                                 |               15 |             78 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/wr_wea                                                                                   |                                                                                                                                                                                                                                |               12 |             96 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/wr_wea                                                                                     |                                                                                                                                                                                                                                |               12 |             96 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               49 |            101 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/wr_wea                                                                                    |                                                                                                                                                                                                                                |               13 |            104 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/wr_wea                                                                                    |                                                                                                                                                                                                                                |               13 |            104 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/wr_wea                                                                                   |                                                                                                                                                                                                                                |               13 |            104 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 | u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.ADDR32.I_MM2S_DMA_MNGR/VIDEO_REG_I/GEN_REGISTER_DIRECT.GEN_REGDIRECT_DRES.VIDREGISTER_I/video_reg_update                                                                             | u1/utg_i/axi_vdma_0/U0/I_RST_MODULE/prmry_resetn_i_reg[0]                                                                                                                                                                      |               26 |            109 |
|  ADC_DCLK_IBUF_BUFG                           |                                                                                                                                                                                                                               |                                                                                                                                                                                                                                |               37 |            109 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_axi4s_vid_out_0/inst/SYNC_INST/vtg_ce                                                                                                                                                                              | u1/utg_i/v_tc_0/U0/U_TC_TOP/GEN_GENERATOR.U_TC_GEN/htotal[11]_i_1_n_0                                                                                                                                                          |               28 |            145 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/AXI4_LITE_INTERFACE.time_control_regs2_int[16][28]_i_1_n_0                                                                                                                                    | u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0                                                                                                                                                                        |               43 |            173 |
|  u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |                                                                                                                                                                                                                               |                                                                                                                                                                                                                                |              357 |           1036 |
|  u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |                                                                                                                                                                                                                               |                                                                                                                                                                                                                                |              280 |           1080 |
+-----------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+------------------+----------------+


