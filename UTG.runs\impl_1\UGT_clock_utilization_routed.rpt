Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Aug  4 14:38:59 2025
| Host         : wantong running 64-bit major release  (build 9200)
| Command      : report_clock_utilization -file UGT_clock_utilization_routed.rpt
| Design       : UGT
| Device       : 7z020-clg400
| Speed File   : -2  PRODUCTION 1.11 2014-09-11
------------------------------------------------------------------------------------

Clock Utilization Report

Table of Contents
-----------------
1. Clock Primitive Utilization
2. Global Clock Resources
3. Global Clock Source Details
4. Clock Regions: Key Resource Utilization
5. Clock Regions : Global Clock Summary
6. Device Cell Placement Summary for Global Clock g0
7. Device Cell Placement Summary for Global Clock g1
8. Device Cell Placement Summary for Global Clock g2
9. Device Cell Placement Summary for Global Clock g3
10. Device Cell Placement Summary for Global Clock g4
11. Clock Region Cell Placement per Global Clock: Region X0Y0
12. Clock Region Cell Placement per Global Clock: Region X1Y0
13. Clock Region Cell Placement per Global Clock: Region X0Y1
14. Clock Region Cell Placement per Global Clock: Region X1Y1
15. Clock Region Cell Placement per Global Clock: Region X0Y2

1. Clock Primitive Utilization
------------------------------

+----------+------+-----------+-----+--------------+--------+
| Type     | Used | Available | LOC | Clock Region | Pblock |
+----------+------+-----------+-----+--------------+--------+
| BUFGCTRL |    4 |        32 |   0 |            0 |      0 |
| BUFH     |    0 |        72 |   0 |            0 |      0 |
| BUFIO    |    0 |        16 |   0 |            0 |      0 |
| BUFMR    |    0 |         8 |   0 |            0 |      0 |
| BUFR     |    1 |        16 |   0 |            0 |      0 |
| MMCM     |    1 |         4 |   0 |            0 |      0 |
| PLL      |    1 |         4 |   0 |            0 |      0 |
+----------+------+-----------+-----+--------------+--------+


2. Global Clock Resources
-------------------------

+-----------+-----------+-----------------+------------+----------------+--------------+-------------------+-------------+-----------------+--------------+--------------------+------------------------------------------------------------------------+----------------------------------------------+
| Global Id | Source Id | Driver Type/Pin | Constraint | Site           | Clock Region | Load Clock Region | Clock Loads | Non-Clock Loads | Clock Period | Clock              | Driver Pin                                                             | Net                                          |
+-----------+-----------+-----------------+------------+----------------+--------------+-------------------+-------------+-----------------+--------------+--------------------+------------------------------------------------------------------------+----------------------------------------------+
| g0        | src0      | BUFG/O          | None       | BUFGCTRL_X0Y17 | n/a          |                 5 |        7081 |               0 |       10.000 | clk_fpga_0         | u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O | u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |
| g1        | src1      | BUFR/O          | None       | BUFR_X1Y5      | X1Y1         |                 1 |        2503 |               1 |       10.000 | LCD_CLK_OBUF       | u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O                                   | u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |
| g2        | src2      | BUFG/O          | None       | BUFGCTRL_X0Y18 | n/a          |                 3 |         308 |               0 |        8.000 | ADC_DCLK           | ADC_DCLK_IBUF_BUFG_inst/O                                              | ADC_DCLK_IBUF_BUFG                           |
| g3        | src3      | BUFG/O          | None       | BUFGCTRL_X0Y16 | n/a          |                 1 |           1 |               0 |        8.000 | clk_out2_clk_wiz_0 | clk_gen/inst/clkout2_buf/O                                             | clk_gen/inst/clk_out2                        |
| g4        | src3      | BUFG/O          | None       | BUFGCTRL_X0Y19 | n/a          |                 1 |           1 |               0 |       20.000 | clkfbout_clk_wiz_0 | clk_gen/inst/clkf_buf/O                                                | clk_gen/inst/clkfbout_buf_clk_wiz_0          |
+-----------+-----------+-----------------+------------+----------------+--------------+-------------------+-------------+-----------------+--------------+--------------------+------------------------------------------------------------------------+----------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


3. Global Clock Source Details
------------------------------

+-----------+-----------+--------------------+------------+-----------------+--------------+-------------+-----------------+---------------------+--------------------+----------------------------------------------------------------+-----------------------------------------------------------+
| Source Id | Global Id | Driver Type/Pin    | Constraint | Site            | Clock Region | Clock Loads | Non-Clock Loads | Source Clock Period | Source Clock       | Driver Pin                                                     | Net                                                       |
+-----------+-----------+--------------------+------------+-----------------+--------------+-------------+-----------------+---------------------+--------------------+----------------------------------------------------------------+-----------------------------------------------------------+
| src0      | g0        | PS7/FCLKCLK[0]     | None       | PS7_X0Y0        | X0Y2         |           1 |               0 |              10.000 | clk_fpga_0         | u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]            | u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0] |
| src1      | g1        | MMCME2_ADV/CLKOUT0 | None       | MMCME2_ADV_X1Y1 | X1Y1         |           1 |               0 |               2.000 | I                  | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0 | u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/I                |
| src2      | g2        | IBUF/O             | IOB_X1Y78  | IOB_X1Y78       | X1Y1         |           1 |               0 |               8.000 | ADC_DCLK           | ADC_DCLK_IBUF_inst/O                                           | ADC_DCLK_IBUF                                             |
| src3      | g3        | PLLE2_ADV/CLKOUT1  | None       | PLLE2_ADV_X1Y1  | X1Y1         |           1 |               0 |               8.000 | clk_out2_clk_wiz_0 | clk_gen/inst/plle2_adv_inst/CLKOUT1                            | clk_gen/inst/clk_out2_clk_wiz_0                           |
| src3      | g4        | PLLE2_ADV/CLKFBOUT | None       | PLLE2_ADV_X1Y1  | X1Y1         |           1 |               0 |              20.000 | clkfbout_clk_wiz_0 | clk_gen/inst/plle2_adv_inst/CLKFBOUT                           | clk_gen/inst/clkfbout_clk_wiz_0                           |
+-----------+-----------+--------------------+------------+-----------------+--------------+-------------+-----------------+---------------------+--------------------+----------------------------------------------------------------+-----------------------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)


4. Clock Regions: Key Resource Utilization
------------------------------------------

+-------------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+--------------+
|                   | Global Clock |     BUFRs    |    BUFMRs    |    BUFIOs    |     MMCM     |      PLL     |      GT      |      PCI     |    ILOGIC    |    OLOGIC    |      FF      |     LUTM     |    RAMB18    |    RAMB36    |    DSP48E2   |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| Clock Region Name | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail | Used | Avail |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
| X0Y0              |    2 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 | 2105 |  2500 |  997 |  1000 |    0 |    60 |    7 |    30 |    0 |    60 |
| X1Y0              |    2 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 | 1775 |  3200 |  341 |   850 |    0 |    60 |    2 |    30 |    0 |    40 |
| X0Y1              |    1 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 | 2811 |  1200 | 1145 |   400 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y1              |    5 |    12 |    1 |     4 |    0 |     2 |    0 |     4 |    1 |     1 |    1 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    1 |    50 | 2778 |  2600 |  778 |   850 |    0 |    60 |    1 |    30 |    0 |    40 |
| X0Y2              |    1 |    12 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |    0 |     0 |   73 |  1200 |   35 |   400 |    0 |    20 |    0 |    10 |    0 |    20 |
| X1Y2              |    0 |    12 |    0 |     4 |    0 |     2 |    0 |     4 |    0 |     1 |    0 |     1 |    0 |     0 |    0 |     0 |    0 |    50 |    0 |    50 |    0 |  2600 |    0 |   850 |    0 |    60 |    0 |    30 |    0 |    40 |
+-------------------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+------+-------+
* Global Clock column represents track count; while other columns represents cell counts


5. Clock Regions : Global Clock Summary
---------------------------------------

All Modules
+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y2 |  1 |  0 |
| Y1 |  1 |  5 |
| Y0 |  2 |  2 |
+----+----+----+


6. Device Cell Placement Summary for Global Clock g0
----------------------------------------------------

+-----------+-----------------+-------------------+------------+-------------+---------------+-------------+----------+----------------+----------+----------------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock      | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                          |
+-----------+-----------------+-------------------+------------+-------------+---------------+-------------+----------+----------------+----------+----------------------------------------------+
| g0        | BUFG/O          | n/a               | clk_fpga_0 |      10.000 | {0.000 5.000} |        7009 |        0 |              1 |        0 | u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |
+-----------+-----------------+-------------------+------------+-------------+---------------+-------------+----------+----------------+----------+----------------------------------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+-------+-------+
|    | X0    | X1    |
+----+-------+-------+
| Y2 |    86 |     0 |
| Y1 |  2927 |   282 |
| Y0 |  1898 |  1817 |
+----+-------+-------+


7. Device Cell Placement Summary for Global Clock g1
----------------------------------------------------

+-----------+-----------------+-------------------+--------------+-------------+---------------+-------------+----------+----------------+----------+------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock        | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                |
+-----------+-----------------+-------------------+--------------+-------------+---------------+-------------+----------+----------------+----------+------------------------------------+
| g1        | BUFR/O          | X1Y1              | LCD_CLK_OBUF |      10.000 | {0.000 4.000} |        2503 |        1 |              0 |        0 | u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O |
+-----------+-----------------+-------------------+--------------+-------------+---------------+-------------+----------+----------------+----------+------------------------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+-----------+
|    | X0 | X1        |
+----+----+-----------+
| Y2 |  0 |         0 |
| Y1 |  0 |  (D) 2504 |
| Y0 |  0 |         0 |
+----+----+-----------+


8. Device Cell Placement Summary for Global Clock g2
----------------------------------------------------

+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+--------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock    | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                |
+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+--------------------+
| g2        | BUFG/O          | n/a               | ADC_DCLK |       8.000 | {0.000 4.000} |         308 |        0 |              0 |        0 | ADC_DCLK_IBUF_BUFG |
+-----------+-----------------+-------------------+----------+-------------+---------------+-------------+----------+----------------+----------+--------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+------+-----+
|    | X0   | X1  |
+----+------+-----+
| Y2 |    0 |   0 |
| Y1 |    0 |   3 |
| Y0 |  295 |  10 |
+----+------+-----+


9. Device Cell Placement Summary for Global Clock g3
----------------------------------------------------

+-----------+-----------------+-------------------+--------------------+-------------+---------------+-------------+----------+----------------+----------+-----------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock              | Period (ns) | Waveform (ns) | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                   |
+-----------+-----------------+-------------------+--------------------+-------------+---------------+-------------+----------+----------------+----------+-----------------------+
| g3        | BUFG/O          | n/a               | clk_out2_clk_wiz_0 |       8.000 | {0.000 4.000} |           1 |        0 |              0 |        0 | clk_gen/inst/clk_out2 |
+-----------+-----------------+-------------------+--------------------+-------------+---------------+-------------+----------+----------------+----------+-----------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y2 |  0 |  0 |
| Y1 |  0 |  1 |
| Y0 |  0 |  0 |
+----+----+----+


10. Device Cell Placement Summary for Global Clock g4
-----------------------------------------------------

+-----------+-----------------+-------------------+--------------------+-------------+----------------+-------------+----------+----------------+----------+-------------------------------------+
| Global Id | Driver Type/Pin | Driver Region (D) | Clock              | Period (ns) | Waveform (ns)  | Slice Loads | IO Loads | Clocking Loads | GT Loads | Net                                 |
+-----------+-----------------+-------------------+--------------------+-------------+----------------+-------------+----------+----------------+----------+-------------------------------------+
| g4        | BUFG/O          | n/a               | clkfbout_clk_wiz_0 |      20.000 | {0.000 10.000} |           0 |        0 |              1 |        0 | clk_gen/inst/clkfbout_buf_clk_wiz_0 |
+-----------+-----------------+-------------------+--------------------+-------------+----------------+-------------+----------+----------------+----------+-------------------------------------+
* Logic Loads column represents load cell count of all cell types other than IO, GT and clock resources
** IO Loads column represents load cell count of IO types
*** Clocking Loads column represents load cell count that are clock resources (global clock buffer, MMCM, PLL, etc)
**** GT Loads column represents load cell count of GT types


+----+----+----+
|    | X0 | X1 |
+----+----+----+
| Y2 |  0 |  0 |
| Y1 |  0 |  1 |
| Y0 |  0 |  0 |
+----+----+----+


11. Clock Region Cell Placement per Global Clock: Region X0Y0
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |        1898 |               0 | 1817 |     74 |    7 |   0 |  0 |    0 |   0 |       0 | u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |
| g2        | n/a   | BUFG/O          | None       |         295 |               0 |  288 |      1 |    6 |   0 |  0 |    0 |   0 |       0 | ADC_DCLK_IBUF_BUFG                           |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


12. Clock Region Cell Placement per Global Clock: Region X1Y0
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |        1817 |               0 | 1765 |     50 |    2 |   0 |  0 |    0 |   0 |       0 | u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |
| g2        | n/a   | BUFG/O          | None       |          10 |               0 |   10 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | ADC_DCLK_IBUF_BUFG                           |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


13. Clock Region Cell Placement per Global Clock: Region X0Y1
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |        2927 |               0 | 2811 |    116 |    0 |   0 |  0 |    0 |   0 |       0 | u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


14. Clock Region Cell Placement per Global Clock: Region X1Y1
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF   | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |         282 |               0 |  279 |      1 |    1 |   0 |  0 |    1 |   0 |       0 | u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |
| g1        | n/a   | BUFR/O          | None       |        2503 |               1 | 2496 |      6 |    1 |   0 |  0 |    0 |   0 |       0 | u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O           |
| g2        | n/a   | BUFG/O          | None       |           3 |               0 |    3 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | ADC_DCLK_IBUF_BUFG                           |
| g3        | n/a   | BUFG/O          | None       |           1 |               0 |    0 |      0 |    0 |   0 |  0 |    0 |   0 |       0 | clk_gen/inst/clk_out2                        |
| g4        | n/a   | BUFG/O          | None       |           1 |               0 |    0 |      0 |    0 |   0 |  0 |    0 |   1 |       0 | clk_gen/inst/clkfbout_buf_clk_wiz_0          |
+-----------+-------+-----------------+------------+-------------+-----------------+------+--------+------+-----+----+------+-----+---------+----------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts


15. Clock Region Cell Placement per Global Clock: Region X0Y2
-------------------------------------------------------------

+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| Global Id | Track | Driver Type/Pin | Constraint | Clock Loads | Non-Clock Loads | FF | LUTRAM | RAMB | DSP | GT | MMCM | PLL | Hard IP | Net                                          |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+----------------------------------------------+
| g0        | n/a   | BUFG/O          | None       |          86 |               0 | 73 |     12 |    0 |   0 |  0 |    0 |   0 |       0 | u1/utg_i/processing_system7_0/inst/FCLK_CLK0 |
+-----------+-------+-----------------+------------+-------------+-----------------+----+--------+------+-----+----+------+-----+---------+----------------------------------------------+
* Clock Loads column represents the clock pin loads (pin count)
** Non-Clock Loads column represents the non-clock pin loads (pin count)
*** Columns FF, LUTRAM, RAMB through 'Hard IP' represents load cell counts



# Location of BUFG Primitives 
set_property LOC BUFGCTRL_X0Y17 [get_cells u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG]
set_property LOC BUFGCTRL_X0Y19 [get_cells clk_gen/inst/clkf_buf]
set_property LOC BUFGCTRL_X0Y16 [get_cells clk_gen/inst/clkout2_buf]
set_property LOC BUFGCTRL_X0Y18 [get_cells ADC_DCLK_IBUF_BUFG_inst]

# Location of BUFR Primitives 
set_property LOC BUFR_X1Y5 [get_cells u1/utg_i/axi_dynclk_0/U0/BUFR_inst]

# Location of IO Primitives which is load of clock spine
set_property LOC IOB_X1Y73 [get_cells LCD_CLK_OBUF_inst]

# Location of clock ports
set_property LOC IOB_X1Y78 [get_ports ADC_DCLK]
set_property LOC IOB_X1Y76 [get_ports clk_50M]

# Clock net "u1/utg_i/processing_system7_0/inst/FCLK_CLK0" driven by instance "u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG" located at site "BUFGCTRL_X0Y17"
#startgroup
create_pblock {CLKAG_u1/utg_i/processing_system7_0/inst/FCLK_CLK0}
add_cells_to_pblock [get_pblocks  {CLKAG_u1/utg_i/processing_system7_0/inst/FCLK_CLK0}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL && NAME!=u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst} -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="u1/utg_i/processing_system7_0/inst/FCLK_CLK0"}]]]
resize_pblock [get_pblocks {CLKAG_u1/utg_i/processing_system7_0/inst/FCLK_CLK0}] -add {CLOCKREGION_X0Y0:CLOCKREGION_X0Y0 CLOCKREGION_X0Y1:CLOCKREGION_X0Y1 CLOCKREGION_X0Y2:CLOCKREGION_X0Y2 CLOCKREGION_X1Y0:CLOCKREGION_X1Y0 CLOCKREGION_X1Y1:CLOCKREGION_X1Y1}
#endgroup

# Clock net "u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O" driven by instance "u1/utg_i/axi_dynclk_0/U0/BUFR_inst" located at site "BUFR_X1Y5"
#startgroup
create_pblock {CLKAG_u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O}
add_cells_to_pblock [get_pblocks  {CLKAG_u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL && NAME!=LCD_CLK_OBUF_inst} -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O"}]]]
resize_pblock [get_pblocks {CLKAG_u1/utg_i/axi_dynclk_0/U0/PXL_CLK_O}] -add {CLOCKREGION_X1Y1:CLOCKREGION_X1Y1}
#endgroup

# Clock net "clk_gen/inst/clk_out2" driven by instance "clk_gen/inst/clkout2_buf" located at site "BUFGCTRL_X0Y16"
#startgroup
create_pblock {CLKAG_clk_gen/inst/clk_out2}
add_cells_to_pblock [get_pblocks  {CLKAG_clk_gen/inst/clk_out2}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="clk_gen/inst/clk_out2"}]]]
resize_pblock [get_pblocks {CLKAG_clk_gen/inst/clk_out2}] -add {CLOCKREGION_X1Y1:CLOCKREGION_X1Y1}
#endgroup

# Clock net "ADC_DCLK_IBUF_BUFG" driven by instance "ADC_DCLK_IBUF_BUFG_inst" located at site "BUFGCTRL_X0Y18"
#startgroup
create_pblock {CLKAG_ADC_DCLK_IBUF_BUFG}
add_cells_to_pblock [get_pblocks  {CLKAG_ADC_DCLK_IBUF_BUFG}] [get_cells -filter { PRIMITIVE_GROUP != I/O && IS_PRIMITIVE==1 && PRIMITIVE_LEVEL !=INTERNAL } -of_object [get_pins -filter {DIRECTION==IN} -of_objects [get_nets -hierarchical -filter {PARENT=="ADC_DCLK_IBUF_BUFG"}]]]
resize_pblock [get_pblocks {CLKAG_ADC_DCLK_IBUF_BUFG}] -add {CLOCKREGION_X0Y0:CLOCKREGION_X0Y0 CLOCKREGION_X1Y0:CLOCKREGION_X1Y0 CLOCKREGION_X1Y1:CLOCKREGION_X1Y1}
#endgroup
