################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_arc.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_blend.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_dither.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_gradient.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_img.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_layer.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_letter.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_line.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_polygon.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_rect.c \
../src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_transform.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_arc.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_blend.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_dither.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_gradient.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_img.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_layer.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_letter.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_line.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_polygon.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_rect.o \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_transform.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_arc.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_blend.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_dither.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_gradient.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_img.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_layer.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_letter.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_line.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_polygon.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_rect.d \
./src/components/gui/lvgl_8.4.0/src/draw/sw/lv_draw_sw_transform.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/src/draw/sw/%.o: ../src/components/gui/lvgl_8.4.0/src/draw/sw/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


