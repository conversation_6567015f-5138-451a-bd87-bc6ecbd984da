<?xml version="1.0" encoding="UTF-8"?>
<GenRun Id="impl_1" LaunchPart="xc7z020clg400-2" LaunchTime="1754289103">
  <File Type="ROUTE-PWR" Name="UGT_power_routed.rpt"/>
  <File Type="PA-TCL" Name="UGT.tcl"/>
  <File Type="BITSTR-BMM" Name="UGT_bd.bmm"/>
  <File Type="ROUTE-PWR-SUM" Name="UGT_power_summary_routed.pb"/>
  <File Type="REPORTS-TCL" Name="UGT_reports.tcl"/>
  <File Type="OPT-DCP" Name="UGT_opt.dcp"/>
  <File Type="OPT-DRC" Name="UGT_drc_opted.rpt"/>
  <File Type="OPT-HWDEF" Name="UGT.hwdef"/>
  <File Type="PWROPT-DCP" Name="UGT_pwropt.dcp"/>
  <File Type="PLACE-DCP" Name="UGT_placed.dcp"/>
  <File Type="PLACE-IO" Name="UGT_io_placed.rpt"/>
  <File Type="PLACE-UTIL" Name="UGT_utilization_placed.rpt"/>
  <File Type="PLACE-UTIL-PB" Name="UGT_utilization_placed.pb"/>
  <File Type="PLACE-CTRL" Name="UGT_control_sets_placed.rpt"/>
  <File Type="PLACE-PRE-SIMILARITY" Name="UGT_incremental_reuse_pre_placed.rpt"/>
  <File Type="BG-BGN" Name="UGT.bgn"/>
  <File Type="POSTPLACE-PWROPT-DCP" Name="UGT_postplace_pwropt.dcp"/>
  <File Type="BG-BIN" Name="UGT.bin"/>
  <File Type="PHYSOPT-DCP" Name="UGT_physopt.dcp"/>
  <File Type="BITSTR-MSK" Name="UGT.msk"/>
  <File Type="ROUTE-ERROR-DCP" Name="UGT_routed_error.dcp"/>
  <File Type="ROUTE-DCP" Name="UGT_routed.dcp"/>
  <File Type="ROUTE-BLACKBOX-DCP" Name="UGT_routed_bb.dcp"/>
  <File Type="ROUTE-DRC" Name="UGT_drc_routed.rpt"/>
  <File Type="ROUTE-DRC-PB" Name="UGT_drc_routed.pb"/>
  <File Type="ROUTE-DRC-RPX" Name="UGT_drc_routed.rpx"/>
  <File Type="BITSTR-LTX" Name="UGT.ltx"/>
  <File Type="ROUTE-METHODOLOGY-DRC" Name="UGT_methodology_drc_routed.rpt"/>
  <File Type="BITSTR-MMI" Name="UGT.mmi"/>
  <File Type="ROUTE-METHODOLOGY-DRC-RPX" Name="UGT_methodology_drc_routed.rpx"/>
  <File Type="ROUTE-METHODOLOGY-DRC-PB" Name="UGT_methodology_drc_routed.pb"/>
  <File Type="BITSTR-SYSDEF" Name="UGT.sysdef"/>
  <File Type="ROUTE-PWR-RPX" Name="UGT_power_routed.rpx"/>
  <File Type="ROUTE-STATUS" Name="UGT_route_status.rpt"/>
  <File Type="ROUTE-STATUS-PB" Name="UGT_route_status.pb"/>
  <File Type="ROUTE-TIMINGSUMMARY" Name="UGT_timing_summary_routed.rpt"/>
  <File Type="ROUTE-TIMING-PB" Name="UGT_timing_summary_routed.pb"/>
  <File Type="ROUTE-TIMING-RPX" Name="UGT_timing_summary_routed.rpx"/>
  <File Type="ROUTE-CLK" Name="UGT_clock_utilization_routed.rpt"/>
  <File Type="ROUTE-BUS-SKEW" Name="UGT_bus_skew_routed.rpt"/>
  <File Type="ROUTE-BUS-SKEW-PB" Name="UGT_bus_skew_routed.pb"/>
  <File Type="ROUTE-BUS-SKEW-RPX" Name="UGT_bus_skew_routed.rpx"/>
  <File Type="POSTROUTE-PHYSOPT-DCP" Name="UGT_postroute_physopt.dcp"/>
  <File Type="POSTROUTE-PHYSOPT-BLACKBOX-DCP" Name="UGT_postroute_physopt_bb.dcp"/>
  <File Type="BG-BIT" Name="UGT.bit"/>
  <File Type="BITSTR-RBT" Name="UGT.rbt"/>
  <File Type="BITSTR-NKY" Name="UGT.nky"/>
  <File Type="BG-DRC" Name="UGT.drc"/>
  <File Type="RDI-RDI" Name="UGT.vdi"/>
  <File Type="WBT-USG" Name="usage_statistics_webtalk.html"/>
  <FileSet Name="sources" Type="DesignSrcs" RelSrcDir="$PSRCDIR/sources_1">
    <Filter Type="Srcs"/>
    <File Path="$PSRCDIR/sources_1/bd/utg/utg.bd">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/bd/utg/hdl/utg_wrapper.v">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/sources_1/imports/UTG/Top_Module.v">
      <FileInfo>
        <Attr Name="ImportPath" Val="$PPRDIR/Top_Module.v"/>
        <Attr Name="ImportTime" Val="1735189957"/>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
        <Attr Name="UsedIn" Val="simulation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="DesignMode" Val="RTL"/>
      <Option Name="TopModule" Val="UGT"/>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <FileSet Name="constrs_in" Type="Constrs" RelSrcDir="$PSRCDIR/constrs_1">
    <Filter Type="Constrs"/>
    <File Path="$PSRCDIR/constrs_1/new/utg_timing.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <File Path="$PSRCDIR/constrs_1/new/utg_pins.xdc">
      <FileInfo>
        <Attr Name="UsedIn" Val="synthesis"/>
        <Attr Name="UsedIn" Val="implementation"/>
      </FileInfo>
    </File>
    <Config>
      <Option Name="TargetConstrsFile" Val="$PSRCDIR/constrs_1/new/utg_timing.xdc"/>
      <Option Name="ConstrsType" Val="XDC"/>
    </Config>
  </FileSet>
  <FileSet Name="utils" Type="Utils" RelSrcDir="$PSRCDIR/utils_1">
    <Filter Type="Utils"/>
    <Config>
      <Option Name="TopAutoSet" Val="TRUE"/>
    </Config>
  </FileSet>
  <Strategy Version="1" Minor="2">
    <StratHandle Name="Vivado Implementation Defaults" Flow="Vivado Implementation 2018"/>
    <Step Id="init_design"/>
    <Step Id="opt_design"/>
    <Step Id="power_opt_design"/>
    <Step Id="place_design"/>
    <Step Id="post_place_power_opt_design"/>
    <Step Id="phys_opt_design"/>
    <Step Id="route_design"/>
    <Step Id="post_route_phys_opt_design"/>
    <Step Id="write_bitstream"/>
  </Strategy>
  <BlockFileSet Type="BlockSrcs" Name="clk_wiz_0"/>
</GenRun>
