################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_arc.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_bg.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_composite.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_img.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_label.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_layer.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_line.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_mask.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_polygon.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_rect.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_stack_blur.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_texture_cache.c \
../src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_utils.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_arc.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_bg.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_composite.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_img.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_label.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_layer.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_line.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_mask.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_polygon.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_rect.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_stack_blur.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_texture_cache.o \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_utils.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_arc.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_bg.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_composite.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_img.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_label.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_layer.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_line.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_mask.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_polygon.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_rect.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_stack_blur.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_texture_cache.d \
./src/components/gui/lvgl_8.4.0/src/draw/sdl/lv_draw_sdl_utils.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/src/draw/sdl/%.o: ../src/components/gui/lvgl_8.4.0/src/draw/sdl/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


