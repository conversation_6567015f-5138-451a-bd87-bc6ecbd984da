<?xml version="1.0" encoding="UTF-8" ?>
<webTalkData  fileName='usage_statistics_webtalk.xml'  majorVersion='1' minorVersion='0' timeStamp='Mon Aug 11 10:50:26 2025'>
<section name="__ROOT__" level="0" order="1" description="">
 <section name="software_version_and_target_device" level="1" order="1" description="">
  <keyValuePair key="beta" value="FALSE" description="" />
  <keyValuePair key="build_version" value="2405991" description="" />
  <keyValuePair key="date_generated" value="Mon Aug 11 10:50:25 2025" description="" />
  <keyValuePair key="os_platform" value="WIN64" description="" />
  <keyValuePair key="product_version" value="Vivado v2018.3 (64-bit)" description="" />
  <keyValuePair key="project_id" value="c9d39f15029a4714b0cb9c76acd34d9a" description="" />
  <keyValuePair key="project_iteration" value="60" description="" />
  <keyValuePair key="random_id" value="6f2e0274765f583a9fcfacd97777ebed" description="" />
  <keyValuePair key="registration_id" value="135256_15690819_173552794_661" description="" />
  <keyValuePair key="route_design" value="TRUE" description="" />
  <keyValuePair key="target_device" value="xc7z020" description="" />
  <keyValuePair key="target_family" value="zynq" description="" />
  <keyValuePair key="target_package" value="clg400" description="" />
  <keyValuePair key="target_speed" value="-2" description="" />
  <keyValuePair key="tool_flow" value="Vivado" description="" />
 </section>
 <section name="user_environment" level="1" order="2" description="">
  <keyValuePair key="cpu_name" value="13th Gen Intel(R) Core(TM) i5-13500H" description="" />
  <keyValuePair key="cpu_speed" value="3187 MHz" description="" />
  <keyValuePair key="os_name" value="Microsoft Windows 8 or later , 64-bit" description="" />
  <keyValuePair key="os_release" value="major release  (build 9200)" description="" />
  <keyValuePair key="system_ram" value="34.000 GB" description="" />
  <keyValuePair key="total_processors" value="1" description="" />
 </section>
 <section name="ip_statistics" level="1" order="3" description="">
  <section name="IP_Integrator/1" level="2" order="1" description="">
   <keyValuePair key="bdsource" value="SBD" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="maxhierdepth" value="1" description="" />
   <keyValuePair key="numblks" value="14" description="" />
   <keyValuePair key="numhdlrefblks" value="0" description="" />
   <keyValuePair key="numhierblks" value="4" description="" />
   <keyValuePair key="numhlsblks" value="0" description="" />
   <keyValuePair key="numnonxlnxblks" value="0" description="" />
   <keyValuePair key="numpkgbdblks" value="0" description="" />
   <keyValuePair key="numreposblks" value="10" description="" />
   <keyValuePair key="numsysgenblks" value="0" description="" />
   <keyValuePair key="synth_mode" value="Global" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="BlockDiagram" description="" />
   <keyValuePair key="x_ipname" value="bd_64d3" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.00.a" description="" />
  </section>
  <section name="IP_Integrator/2" level="2" order="2" description="">
   <keyValuePair key="bdsource" value="SBD" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="maxhierdepth" value="1" description="" />
   <keyValuePair key="numblks" value="17" description="" />
   <keyValuePair key="numhdlrefblks" value="0" description="" />
   <keyValuePair key="numhierblks" value="4" description="" />
   <keyValuePair key="numhlsblks" value="0" description="" />
   <keyValuePair key="numnonxlnxblks" value="0" description="" />
   <keyValuePair key="numpkgbdblks" value="0" description="" />
   <keyValuePair key="numreposblks" value="13" description="" />
   <keyValuePair key="numsysgenblks" value="0" description="" />
   <keyValuePair key="synth_mode" value="Global" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="BlockDiagram" description="" />
   <keyValuePair key="x_ipname" value="bd_a552" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.00.a" description="" />
  </section>
  <section name="IP_Integrator/3" level="2" order="3" description="">
   <keyValuePair key="bdsource" value="USER" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="da_axi4_cnt" value="22" description="" />
   <keyValuePair key="da_board_cnt" value="2" description="" />
   <keyValuePair key="da_clkrst_cnt" value="25" description="" />
   <keyValuePair key="da_ps7_cnt" value="1" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="maxhierdepth" value="0" description="" />
   <keyValuePair key="numblks" value="23" description="" />
   <keyValuePair key="numhdlrefblks" value="0" description="" />
   <keyValuePair key="numhierblks" value="7" description="" />
   <keyValuePair key="numhlsblks" value="0" description="" />
   <keyValuePair key="numnonxlnxblks" value="3" description="" />
   <keyValuePair key="numpkgbdblks" value="0" description="" />
   <keyValuePair key="numreposblks" value="16" description="" />
   <keyValuePair key="numsysgenblks" value="0" description="" />
   <keyValuePair key="synth_mode" value="Global" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="BlockDiagram" description="" />
   <keyValuePair key="x_ipname" value="utg" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.00.a" description="" />
  </section>
  <section name="axi_crossbar_v2_1_19_axi_crossbar/1" level="2" order="4" description="">
   <keyValuePair key="c_axi_addr_width" value="32" description="" />
   <keyValuePair key="c_axi_aruser_width" value="1" description="" />
   <keyValuePair key="c_axi_awuser_width" value="1" description="" />
   <keyValuePair key="c_axi_buser_width" value="1" description="" />
   <keyValuePair key="c_axi_data_width" value="32" description="" />
   <keyValuePair key="c_axi_id_width" value="1" description="" />
   <keyValuePair key="c_axi_protocol" value="2" description="" />
   <keyValuePair key="c_axi_ruser_width" value="1" description="" />
   <keyValuePair key="c_axi_supports_user_signals" value="0" description="" />
   <keyValuePair key="c_axi_wuser_width" value="1" description="" />
   <keyValuePair key="c_connectivity_mode" value="0" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_m_axi_addr_width" value="0x0000001000000010000000100000001000000010" description="" />
   <keyValuePair key="c_m_axi_base_addr" value="0x0000000043c300000000000043c200000000000043c100000000000043c000000000000043000000" description="" />
   <keyValuePair key="c_m_axi_read_connectivity" value="0x0000000100000001000000010000000100000001" description="" />
   <keyValuePair key="c_m_axi_read_issuing" value="0x0000000100000001000000010000000100000001" description="" />
   <keyValuePair key="c_m_axi_secure" value="0x0000000000000000000000000000000000000000" description="" />
   <keyValuePair key="c_m_axi_write_connectivity" value="0x0000000100000001000000010000000100000001" description="" />
   <keyValuePair key="c_m_axi_write_issuing" value="0x0000000100000001000000010000000100000001" description="" />
   <keyValuePair key="c_num_addr_ranges" value="1" description="" />
   <keyValuePair key="c_num_master_slots" value="5" description="" />
   <keyValuePair key="c_num_slave_slots" value="1" description="" />
   <keyValuePair key="c_r_register" value="1" description="" />
   <keyValuePair key="c_s_axi_arb_priority" value="0x00000000" description="" />
   <keyValuePair key="c_s_axi_base_id" value="0x00000000" description="" />
   <keyValuePair key="c_s_axi_read_acceptance" value="0x00000001" description="" />
   <keyValuePair key="c_s_axi_single_thread" value="0x00000001" description="" />
   <keyValuePair key="c_s_axi_thread_id_width" value="0x00000000" description="" />
   <keyValuePair key="c_s_axi_write_acceptance" value="0x00000001" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="19" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_crossbar" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="axi_protocol_converter_v2_1_18_axi_protocol_converter/1" level="2" order="5" description="">
   <keyValuePair key="c_axi_addr_width" value="32" description="" />
   <keyValuePair key="c_axi_aruser_width" value="1" description="" />
   <keyValuePair key="c_axi_awuser_width" value="1" description="" />
   <keyValuePair key="c_axi_buser_width" value="1" description="" />
   <keyValuePair key="c_axi_data_width" value="32" description="" />
   <keyValuePair key="c_axi_id_width" value="12" description="" />
   <keyValuePair key="c_axi_ruser_width" value="1" description="" />
   <keyValuePair key="c_axi_supports_read" value="1" description="" />
   <keyValuePair key="c_axi_supports_user_signals" value="0" description="" />
   <keyValuePair key="c_axi_supports_write" value="1" description="" />
   <keyValuePair key="c_axi_wuser_width" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_ignore_id" value="0" description="" />
   <keyValuePair key="c_m_axi_protocol" value="2" description="" />
   <keyValuePair key="c_s_axi_protocol" value="1" description="" />
   <keyValuePair key="c_translation_mode" value="2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="18" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_protocol_converter" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="axi_vdma/1" level="2" order="6" description="">
   <keyValuePair key="c_dlytmr_resolution" value="125" description="" />
   <keyValuePair key="c_dynamic_resolution" value="1" description="" />
   <keyValuePair key="c_enable_debug_all" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_0" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_1" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_10" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_11" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_12" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_13" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_14" value="1" description="" />
   <keyValuePair key="c_enable_debug_info_15" value="1" description="" />
   <keyValuePair key="c_enable_debug_info_2" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_3" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_4" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_5" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_6" value="1" description="" />
   <keyValuePair key="c_enable_debug_info_7" value="1" description="" />
   <keyValuePair key="c_enable_debug_info_8" value="0" description="" />
   <keyValuePair key="c_enable_debug_info_9" value="0" description="" />
   <keyValuePair key="c_enable_vert_flip" value="0" description="" />
   <keyValuePair key="c_enable_vidprmtr_reads" value="1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_flush_on_fsync" value="1" description="" />
   <keyValuePair key="c_include_internal_genlock" value="1" description="" />
   <keyValuePair key="c_include_mm2s" value="1" description="" />
   <keyValuePair key="c_include_mm2s_dre" value="0" description="" />
   <keyValuePair key="c_include_mm2s_sf" value="0" description="" />
   <keyValuePair key="c_include_s2mm" value="0" description="" />
   <keyValuePair key="c_include_s2mm_dre" value="0" description="" />
   <keyValuePair key="c_include_s2mm_sf" value="1" description="" />
   <keyValuePair key="c_include_sg" value="0" description="" />
   <keyValuePair key="c_instance" value="axi_vdma" description="" />
   <keyValuePair key="c_m_axi_mm2s_addr_width" value="32" description="" />
   <keyValuePair key="c_m_axi_mm2s_data_width" value="64" description="" />
   <keyValuePair key="c_m_axi_s2mm_addr_width" value="32" description="" />
   <keyValuePair key="c_m_axi_s2mm_data_width" value="64" description="" />
   <keyValuePair key="c_m_axi_sg_addr_width" value="32" description="" />
   <keyValuePair key="c_m_axi_sg_data_width" value="32" description="" />
   <keyValuePair key="c_m_axis_mm2s_tdata_width" value="32" description="" />
   <keyValuePair key="c_m_axis_mm2s_tuser_bits" value="1" description="" />
   <keyValuePair key="c_mm2s_genlock_mode" value="3" description="" />
   <keyValuePair key="c_mm2s_genlock_num_masters" value="1" description="" />
   <keyValuePair key="c_mm2s_genlock_repeat_en" value="0" description="" />
   <keyValuePair key="c_mm2s_linebuffer_depth" value="2048" description="" />
   <keyValuePair key="c_mm2s_linebuffer_thresh" value="4" description="" />
   <keyValuePair key="c_mm2s_max_burst_length" value="64" description="" />
   <keyValuePair key="c_mm2s_sof_enable" value="1" description="" />
   <keyValuePair key="c_num_fstores" value="2" description="" />
   <keyValuePair key="c_prmry_is_aclk_async" value="0" description="" />
   <keyValuePair key="c_s2mm_genlock_mode" value="0" description="" />
   <keyValuePair key="c_s2mm_genlock_num_masters" value="1" description="" />
   <keyValuePair key="c_s2mm_genlock_repeat_en" value="1" description="" />
   <keyValuePair key="c_s2mm_linebuffer_depth" value="512" description="" />
   <keyValuePair key="c_s2mm_linebuffer_thresh" value="4" description="" />
   <keyValuePair key="c_s2mm_max_burst_length" value="8" description="" />
   <keyValuePair key="c_s2mm_sof_enable" value="1" description="" />
   <keyValuePair key="c_s_axi_lite_addr_width" value="9" description="" />
   <keyValuePair key="c_s_axi_lite_data_width" value="32" description="" />
   <keyValuePair key="c_s_axis_s2mm_tdata_width" value="32" description="" />
   <keyValuePair key="c_s_axis_s2mm_tuser_bits" value="1" description="" />
   <keyValuePair key="c_select_xpm" value="0" description="" />
   <keyValuePair key="c_use_fsync" value="1" description="" />
   <keyValuePair key="c_use_mm2s_fsync" value="0" description="" />
   <keyValuePair key="c_use_s2mm_fsync" value="2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="6" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="axi_vdma" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="6.3" description="" />
  </section>
  <section name="bd_64d3/1" level="2" order="7" description="">
   <keyValuePair key="advanced_properties" value="0" description="" />
   <keyValuePair key="component_name" value="utg_axi_smc_2" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="has_aresetn" value="1" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="num_clks" value="1" description="" />
   <keyValuePair key="num_mi" value="1" description="" />
   <keyValuePair key="num_si" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="smartconnect" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="bd_a552/1" level="2" order="8" description="">
   <keyValuePair key="advanced_properties" value="0" description="" />
   <keyValuePair key="component_name" value="utg_axi_smc_0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="has_aresetn" value="1" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="num_clks" value="1" description="" />
   <keyValuePair key="num_mi" value="1" description="" />
   <keyValuePair key="num_si" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="smartconnect" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="clk_wiz_v6_0_2_0_0/1" level="2" order="9" description="">
   <keyValuePair key="clkin1_period" value="20.000" description="" />
   <keyValuePair key="clkin2_period" value="10.0" description="" />
   <keyValuePair key="clock_mgr_type" value="NA" description="" />
   <keyValuePair key="component_name" value="clk_wiz_0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="enable_axi" value="0" description="" />
   <keyValuePair key="feedback_source" value="FDBK_AUTO" description="" />
   <keyValuePair key="feedback_type" value="SINGLE" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="manual_override" value="false" description="" />
   <keyValuePair key="num_out_clk" value="2" description="" />
   <keyValuePair key="primitive" value="PLL" description="" />
   <keyValuePair key="use_dyn_phase_shift" value="false" description="" />
   <keyValuePair key="use_dyn_reconfig" value="false" description="" />
   <keyValuePair key="use_inclk_stopped" value="false" description="" />
   <keyValuePair key="use_inclk_switchover" value="false" description="" />
   <keyValuePair key="use_locked" value="true" description="" />
   <keyValuePair key="use_max_i_jitter" value="false" description="" />
   <keyValuePair key="use_min_o_jitter" value="false" description="" />
   <keyValuePair key="use_phase_alignment" value="true" description="" />
   <keyValuePair key="use_power_down" value="false" description="" />
   <keyValuePair key="use_reset" value="true" description="" />
  </section>
  <section name="proc_sys_reset/1" level="2" order="10" description="">
   <keyValuePair key="c_aux_reset_high" value="0" description="" />
   <keyValuePair key="c_aux_rst_width" value="4" description="" />
   <keyValuePair key="c_ext_reset_high" value="0" description="" />
   <keyValuePair key="c_ext_rst_width" value="4" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_num_bus_rst" value="1" description="" />
   <keyValuePair key="c_num_interconnect_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_rst" value="1" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="13" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="proc_sys_reset" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="5.0" description="" />
  </section>
  <section name="proc_sys_reset/2" level="2" order="11" description="">
   <keyValuePair key="c_aux_reset_high" value="0" description="" />
   <keyValuePair key="c_aux_rst_width" value="4" description="" />
   <keyValuePair key="c_ext_reset_high" value="0" description="" />
   <keyValuePair key="c_ext_rst_width" value="4" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_num_bus_rst" value="1" description="" />
   <keyValuePair key="c_num_interconnect_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_aresetn" value="1" description="" />
   <keyValuePair key="c_num_perp_rst" value="1" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="13" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="proc_sys_reset" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="5.0" description="" />
  </section>
  <section name="processing_system7_v5.5_user_configuration/1" level="2" order="12" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="pcw_apu_clk_ratio_enable" value="6:2:1" description="" />
   <keyValuePair key="pcw_apu_peripheral_freqmhz" value="666.666666" description="" />
   <keyValuePair key="pcw_armpll_ctrl_fbdiv" value="40" description="" />
   <keyValuePair key="pcw_can0_grp_clk_enable" value="0" description="" />
   <keyValuePair key="pcw_can0_peripheral_clksrc" value="External" description="" />
   <keyValuePair key="pcw_can0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_can0_peripheral_freqmhz" value="-1" description="" />
   <keyValuePair key="pcw_can1_grp_clk_enable" value="0" description="" />
   <keyValuePair key="pcw_can1_peripheral_clksrc" value="External" description="" />
   <keyValuePair key="pcw_can1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_can1_peripheral_freqmhz" value="-1" description="" />
   <keyValuePair key="pcw_can_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_can_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_cpu_cpu_pll_freqmhz" value="1333.333" description="" />
   <keyValuePair key="pcw_cpu_peripheral_clksrc" value="ARM PLL" description="" />
   <keyValuePair key="pcw_crystal_peripheral_freqmhz" value="33.333333" description="" />
   <keyValuePair key="pcw_dci_peripheral_clksrc" value="DDR PLL" description="" />
   <keyValuePair key="pcw_dci_peripheral_freqmhz" value="10.159" description="" />
   <keyValuePair key="pcw_ddr_ddr_pll_freqmhz" value="1066.667" description="" />
   <keyValuePair key="pcw_ddr_hpr_to_critical_priority_level" value="15" description="" />
   <keyValuePair key="pcw_ddr_hprlpr_queue_partition" value="HPR(0)/LPR(32)" description="" />
   <keyValuePair key="pcw_ddr_lpr_to_critical_priority_level" value="2" description="" />
   <keyValuePair key="pcw_ddr_peripheral_clksrc" value="DDR PLL" description="" />
   <keyValuePair key="pcw_ddr_port0_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_port1_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_port2_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_port3_hpr_enable" value="0" description="" />
   <keyValuePair key="pcw_ddr_write_to_critical_priority_level" value="2" description="" />
   <keyValuePair key="pcw_ddrpll_ctrl_fbdiv" value="32" description="" />
   <keyValuePair key="pcw_enet0_grp_mdio_enable" value="0" description="" />
   <keyValuePair key="pcw_enet0_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_enet0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_enet0_peripheral_freqmhz" value="1000 Mbps" description="" />
   <keyValuePair key="pcw_enet0_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_enet1_grp_mdio_enable" value="0" description="" />
   <keyValuePair key="pcw_enet1_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_enet1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_enet1_peripheral_freqmhz" value="1000 Mbps" description="" />
   <keyValuePair key="pcw_enet1_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_enet_reset_polarity" value="Active Low" description="" />
   <keyValuePair key="pcw_fclk0_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fclk1_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fclk2_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fclk3_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_fpga0_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_fpga1_peripheral_freqmhz" value="150" description="" />
   <keyValuePair key="pcw_fpga2_peripheral_freqmhz" value="50" description="" />
   <keyValuePair key="pcw_fpga3_peripheral_freqmhz" value="50" description="" />
   <keyValuePair key="pcw_fpga_fclk0_enable" value="1" description="" />
   <keyValuePair key="pcw_fpga_fclk1_enable" value="0" description="" />
   <keyValuePair key="pcw_fpga_fclk2_enable" value="0" description="" />
   <keyValuePair key="pcw_fpga_fclk3_enable" value="0" description="" />
   <keyValuePair key="pcw_gpio_emio_gpio_enable" value="1" description="" />
   <keyValuePair key="pcw_gpio_emio_gpio_io" value="3" description="" />
   <keyValuePair key="pcw_gpio_mio_gpio_enable" value="0" description="" />
   <keyValuePair key="pcw_gpio_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c0_grp_int_enable" value="1" description="" />
   <keyValuePair key="pcw_i2c0_grp_int_io" value="EMIO" description="" />
   <keyValuePair key="pcw_i2c0_i2c0_io" value="EMIO" description="" />
   <keyValuePair key="pcw_i2c0_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_i2c0_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c1_grp_int_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c1_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_i2c_reset_polarity" value="Active Low" description="" />
   <keyValuePair key="pcw_io_io_pll_freqmhz" value="1800.000" description="" />
   <keyValuePair key="pcw_iopll_ctrl_fbdiv" value="54" description="" />
   <keyValuePair key="pcw_irq_f2p_mode" value="DIRECT" description="" />
   <keyValuePair key="pcw_m_axi_gp0_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_m_axi_gp1_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_nand_cycles_t_ar" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_clr" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nand_cycles_t_rea" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_rr" value="1" description="" />
   <keyValuePair key="pcw_nand_cycles_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nand_cycles_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nand_grp_d8_enable" value="0" description="" />
   <keyValuePair key="pcw_nand_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_cs0_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs0_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs0_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_cs0_we_time" value="0" description="" />
   <keyValuePair key="pcw_nor_cs1_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs1_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_cs1_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_cs1_we_time" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_a25_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_cs0_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_cs1_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_sram_cs0_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_sram_cs1_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_grp_sram_int_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs0_we_time" value="0" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_ceoe" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_pc" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_rc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_tr" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_wc" value="11" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_t_wp" value="1" description="" />
   <keyValuePair key="pcw_nor_sram_cs1_we_time" value="0" description="" />
   <keyValuePair key="pcw_override_basic_clock" value="0" description="" />
   <keyValuePair key="pcw_pcap_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_pcap_peripheral_freqmhz" value="200" description="" />
   <keyValuePair key="pcw_pjtag_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_preset_bank0_voltage" value="LVCMOS 3.3V" description="" />
   <keyValuePair key="pcw_preset_bank1_voltage" value="LVCMOS 1.8V" description="" />
   <keyValuePair key="pcw_qspi_grp_fbclk_enable" value="0" description="" />
   <keyValuePair key="pcw_qspi_grp_io1_enable" value="0" description="" />
   <keyValuePair key="pcw_qspi_grp_single_ss_enable" value="1" description="" />
   <keyValuePair key="pcw_qspi_grp_single_ss_io" value="MIO 1 .. 6" description="" />
   <keyValuePair key="pcw_qspi_grp_ss1_enable" value="0" description="" />
   <keyValuePair key="pcw_qspi_internal_highaddress" value="0xFCFFFFFF" description="" />
   <keyValuePair key="pcw_qspi_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_qspi_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_qspi_peripheral_freqmhz" value="200" description="" />
   <keyValuePair key="pcw_qspi_qspi_io" value="MIO 1 .. 6" description="" />
   <keyValuePair key="pcw_s_axi_acp_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_s_axi_gp0_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_s_axi_gp1_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_s_axi_hp0_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp0_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_s_axi_hp1_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp1_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_s_axi_hp2_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp2_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_s_axi_hp3_data_width" value="64" description="" />
   <keyValuePair key="pcw_s_axi_hp3_freqmhz" value="10" description="" />
   <keyValuePair key="pcw_sd0_grp_cd_enable" value="1" description="" />
   <keyValuePair key="pcw_sd0_grp_cd_io" value="MIO 10" description="" />
   <keyValuePair key="pcw_sd0_grp_pow_enable" value="0" description="" />
   <keyValuePair key="pcw_sd0_grp_wp_enable" value="0" description="" />
   <keyValuePair key="pcw_sd0_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_sd0_sd0_io" value="MIO 40 .. 45" description="" />
   <keyValuePair key="pcw_sd1_grp_cd_enable" value="0" description="" />
   <keyValuePair key="pcw_sd1_grp_pow_enable" value="0" description="" />
   <keyValuePair key="pcw_sd1_grp_wp_enable" value="0" description="" />
   <keyValuePair key="pcw_sd1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_sdio_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_sdio_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_single_qspi_data_mode" value="x4" description="" />
   <keyValuePair key="pcw_smc_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_smc_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_spi0_grp_ss0_enable" value="0" description="" />
   <keyValuePair key="pcw_spi0_grp_ss1_enable" value="0" description="" />
   <keyValuePair key="pcw_spi0_grp_ss2_enable" value="0" description="" />
   <keyValuePair key="pcw_spi0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_spi1_grp_ss0_enable" value="0" description="" />
   <keyValuePair key="pcw_spi1_grp_ss1_enable" value="0" description="" />
   <keyValuePair key="pcw_spi1_grp_ss2_enable" value="0" description="" />
   <keyValuePair key="pcw_spi1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_spi_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_spi_peripheral_freqmhz" value="166.666666" description="" />
   <keyValuePair key="pcw_tpiu_peripheral_clksrc" value="External" description="" />
   <keyValuePair key="pcw_tpiu_peripheral_freqmhz" value="200" description="" />
   <keyValuePair key="pcw_trace_grp_16bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_2bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_32bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_4bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_grp_8bit_enable" value="0" description="" />
   <keyValuePair key="pcw_trace_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_ttc0_clk0_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc0_clk0_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc0_clk1_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc0_clk1_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc0_clk2_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc0_clk2_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_ttc1_clk0_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc1_clk0_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc1_clk1_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc1_clk1_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc1_clk2_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_ttc1_clk2_peripheral_freqmhz" value="133.333333" description="" />
   <keyValuePair key="pcw_ttc1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_ttc_peripheral_freqmhz" value="50" description="" />
   <keyValuePair key="pcw_uart0_baud_rate" value="115200" description="" />
   <keyValuePair key="pcw_uart0_grp_full_enable" value="0" description="" />
   <keyValuePair key="pcw_uart0_peripheral_enable" value="1" description="" />
   <keyValuePair key="pcw_uart0_uart0_io" value="MIO 14 .. 15" description="" />
   <keyValuePair key="pcw_uart1_baud_rate" value="115200" description="" />
   <keyValuePair key="pcw_uart1_grp_full_enable" value="0" description="" />
   <keyValuePair key="pcw_uart1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_uart_peripheral_clksrc" value="IO PLL" description="" />
   <keyValuePair key="pcw_uart_peripheral_freqmhz" value="100" description="" />
   <keyValuePair key="pcw_uiparam_ddr_adv_enable" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_al" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_bank_addr_count" value="3" description="" />
   <keyValuePair key="pcw_uiparam_ddr_bl" value="8" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay0" value="0.25" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay1" value="0.25" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay2" value="0.25" description="" />
   <keyValuePair key="pcw_uiparam_ddr_board_delay3" value="0.25" description="" />
   <keyValuePair key="pcw_uiparam_ddr_bus_width" value="32 Bit" description="" />
   <keyValuePair key="pcw_uiparam_ddr_cl" value="7" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_0_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_0_package_length" value="54.563" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_0_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_1_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_1_package_length" value="54.563" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_1_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_2_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_2_package_length" value="54.563" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_2_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_3_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_3_package_length" value="54.563" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_3_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_clock_stop_en" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_col_addr_count" value="10" description="" />
   <keyValuePair key="pcw_uiparam_ddr_cwl" value="6" description="" />
   <keyValuePair key="pcw_uiparam_ddr_device_capacity" value="4096 MBits" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_0_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_0_package_length" value="104.5365" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_0_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_1_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_1_package_length" value="70.676" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_1_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_2_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_2_package_length" value="59.1615" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_2_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_3_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_3_package_length" value="81.319" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dq_3_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_0_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_0_package_length" value="101.239" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_0_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_1_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_1_package_length" value="79.5025" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_1_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_2_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_2_package_length" value="60.536" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_2_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_3_length_mm" value="0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_3_package_length" value="71.7715" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_3_propogation_delay" value="160" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_0" value="0.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_1" value="0.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_2" value="0.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dqs_to_clk_delay_3" value="0.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_dram_width" value="16 Bits" description="" />
   <keyValuePair key="pcw_uiparam_ddr_ecc" value="Disabled" description="" />
   <keyValuePair key="pcw_uiparam_ddr_enable" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_freq_mhz" value="533.333333" description="" />
   <keyValuePair key="pcw_uiparam_ddr_high_temp" value="Normal (0-85)" description="" />
   <keyValuePair key="pcw_uiparam_ddr_memory_type" value="DDR 3" description="" />
   <keyValuePair key="pcw_uiparam_ddr_partno" value="MT41J256M16 RE-125" description="" />
   <keyValuePair key="pcw_uiparam_ddr_row_addr_count" value="15" description="" />
   <keyValuePair key="pcw_uiparam_ddr_speed_bin" value="DDR3_1066F" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_faw" value="40.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_ras_min" value="35.0" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_rc" value="48.91" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_rcd" value="7" description="" />
   <keyValuePair key="pcw_uiparam_ddr_t_rp" value="7" description="" />
   <keyValuePair key="pcw_uiparam_ddr_train_data_eye" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_train_read_gate" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_train_write_level" value="1" description="" />
   <keyValuePair key="pcw_uiparam_ddr_use_internal_vref" value="0" description="" />
   <keyValuePair key="pcw_usb0_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_usb0_peripheral_freqmhz" value="60" description="" />
   <keyValuePair key="pcw_usb0_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_usb1_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_usb1_peripheral_freqmhz" value="60" description="" />
   <keyValuePair key="pcw_usb1_reset_enable" value="0" description="" />
   <keyValuePair key="pcw_usb_reset_polarity" value="Active Low" description="" />
   <keyValuePair key="pcw_use_cross_trigger" value="0" description="" />
   <keyValuePair key="pcw_use_m_axi_gp0" value="1" description="" />
   <keyValuePair key="pcw_use_m_axi_gp1" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_acp" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_gp0" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_gp1" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_hp0" value="1" description="" />
   <keyValuePair key="pcw_use_s_axi_hp1" value="1" description="" />
   <keyValuePair key="pcw_use_s_axi_hp2" value="0" description="" />
   <keyValuePair key="pcw_use_s_axi_hp3" value="0" description="" />
   <keyValuePair key="pcw_wdt_peripheral_clksrc" value="CPU_1X" description="" />
   <keyValuePair key="pcw_wdt_peripheral_enable" value="0" description="" />
   <keyValuePair key="pcw_wdt_peripheral_freqmhz" value="133.333333" description="" />
  </section>
  <section name="processing_system7_v5_5_processing_system7/1" level="2" order="13" description="">
   <keyValuePair key="c_dm_width" value="4" description="" />
   <keyValuePair key="c_dq_width" value="32" description="" />
   <keyValuePair key="c_dqs_width" value="4" description="" />
   <keyValuePair key="c_emio_gpio_width" value="3" description="" />
   <keyValuePair key="c_en_emio_enet0" value="0" description="" />
   <keyValuePair key="c_en_emio_enet1" value="0" description="" />
   <keyValuePair key="c_en_emio_pjtag" value="0" description="" />
   <keyValuePair key="c_en_emio_trace" value="0" description="" />
   <keyValuePair key="c_fclk_clk0_buf" value="TRUE" description="" />
   <keyValuePair key="c_fclk_clk1_buf" value="FALSE" description="" />
   <keyValuePair key="c_fclk_clk2_buf" value="FALSE" description="" />
   <keyValuePair key="c_fclk_clk3_buf" value="FALSE" description="" />
   <keyValuePair key="c_gp0_en_modifiable_txn" value="1" description="" />
   <keyValuePair key="c_gp1_en_modifiable_txn" value="1" description="" />
   <keyValuePair key="c_include_acp_trans_check" value="0" description="" />
   <keyValuePair key="c_include_trace_buffer" value="0" description="" />
   <keyValuePair key="c_irq_f2p_mode" value="DIRECT" description="" />
   <keyValuePair key="c_m_axi_gp0_enable_static_remap" value="0" description="" />
   <keyValuePair key="c_m_axi_gp0_id_width" value="12" description="" />
   <keyValuePair key="c_m_axi_gp0_thread_id_width" value="12" description="" />
   <keyValuePair key="c_m_axi_gp1_enable_static_remap" value="0" description="" />
   <keyValuePair key="c_m_axi_gp1_id_width" value="12" description="" />
   <keyValuePair key="c_m_axi_gp1_thread_id_width" value="12" description="" />
   <keyValuePair key="c_mio_primitive" value="54" description="" />
   <keyValuePair key="c_num_f2p_intr_inputs" value="2" description="" />
   <keyValuePair key="c_package_name" value="clg400" description="" />
   <keyValuePair key="c_ps7_si_rev" value="PRODUCTION" description="" />
   <keyValuePair key="c_s_axi_acp_aruser_val" value="31" description="" />
   <keyValuePair key="c_s_axi_acp_awuser_val" value="31" description="" />
   <keyValuePair key="c_s_axi_acp_id_width" value="3" description="" />
   <keyValuePair key="c_s_axi_gp0_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_gp1_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp0_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp0_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp1_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp1_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp2_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp2_id_width" value="6" description="" />
   <keyValuePair key="c_s_axi_hp3_data_width" value="64" description="" />
   <keyValuePair key="c_s_axi_hp3_id_width" value="6" description="" />
   <keyValuePair key="c_trace_buffer_clock_delay" value="12" description="" />
   <keyValuePair key="c_trace_buffer_fifo_size" value="128" description="" />
   <keyValuePair key="c_trace_internal_width" value="2" description="" />
   <keyValuePair key="c_trace_pipeline_width" value="8" description="" />
   <keyValuePair key="c_use_axi_nonsecure" value="0" description="" />
   <keyValuePair key="c_use_default_acp_user_val" value="0" description="" />
   <keyValuePair key="c_use_m_axi_gp0" value="1" description="" />
   <keyValuePair key="c_use_m_axi_gp1" value="0" description="" />
   <keyValuePair key="c_use_s_axi_acp" value="0" description="" />
   <keyValuePair key="c_use_s_axi_gp0" value="0" description="" />
   <keyValuePair key="c_use_s_axi_gp1" value="0" description="" />
   <keyValuePair key="c_use_s_axi_hp0" value="1" description="" />
   <keyValuePair key="c_use_s_axi_hp1" value="1" description="" />
   <keyValuePair key="c_use_s_axi_hp2" value="0" description="" />
   <keyValuePair key="c_use_s_axi_hp3" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="use_trace_data_edge_detector" value="0" description="" />
   <keyValuePair key="x_ipcorerevision" value="6" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="processing_system7" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="5.5" description="" />
  </section>
  <section name="sc_exit_v1_0_8_top/1" level="2" order="14" description="">
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_has_lock" value="0" description="" />
   <keyValuePair key="c_is_cascaded" value="0" description="" />
   <keyValuePair key="c_m_aruser_width" value="0" description="" />
   <keyValuePair key="c_m_awuser_width" value="0" description="" />
   <keyValuePair key="c_m_buser_width" value="0" description="" />
   <keyValuePair key="c_m_id_width" value="0" description="" />
   <keyValuePair key="c_m_limit_read_length" value="16" description="" />
   <keyValuePair key="c_m_limit_write_length" value="16" description="" />
   <keyValuePair key="c_m_protocol" value="1" description="" />
   <keyValuePair key="c_m_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_m_ruser_width" value="0" description="" />
   <keyValuePair key="c_m_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_m_wuser_width" value="0" description="" />
   <keyValuePair key="c_max_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_max_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_mep_identifier_width" value="1" description="" />
   <keyValuePair key="c_num_msc" value="1" description="" />
   <keyValuePair key="c_num_read_outstanding" value="2" description="" />
   <keyValuePair key="c_num_write_outstanding" value="2" description="" />
   <keyValuePair key="c_rdata_width" value="64" description="" />
   <keyValuePair key="c_read_acceptance" value="32" description="" />
   <keyValuePair key="c_s_id_width" value="1" description="" />
   <keyValuePair key="c_single_issuing" value="0" description="" />
   <keyValuePair key="c_ssc_route_array" value="0b10" description="" />
   <keyValuePair key="c_ssc_route_width" value="1" description="" />
   <keyValuePair key="c_wdata_width" value="64" description="" />
   <keyValuePair key="c_write_acceptance" value="32" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="8" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_exit" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_exit_v1_0_8_top/2" level="2" order="15" description="">
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_has_lock" value="0" description="" />
   <keyValuePair key="c_is_cascaded" value="0" description="" />
   <keyValuePair key="c_m_aruser_width" value="0" description="" />
   <keyValuePair key="c_m_awuser_width" value="0" description="" />
   <keyValuePair key="c_m_buser_width" value="0" description="" />
   <keyValuePair key="c_m_id_width" value="0" description="" />
   <keyValuePair key="c_m_limit_read_length" value="16" description="" />
   <keyValuePair key="c_m_limit_write_length" value="16" description="" />
   <keyValuePair key="c_m_protocol" value="1" description="" />
   <keyValuePair key="c_m_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_m_ruser_width" value="0" description="" />
   <keyValuePair key="c_m_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_m_wuser_width" value="0" description="" />
   <keyValuePair key="c_max_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_max_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_mep_identifier_width" value="1" description="" />
   <keyValuePair key="c_num_msc" value="1" description="" />
   <keyValuePair key="c_num_read_outstanding" value="2" description="" />
   <keyValuePair key="c_num_write_outstanding" value="16" description="" />
   <keyValuePair key="c_rdata_width" value="64" description="" />
   <keyValuePair key="c_read_acceptance" value="32" description="" />
   <keyValuePair key="c_s_id_width" value="1" description="" />
   <keyValuePair key="c_single_issuing" value="0" description="" />
   <keyValuePair key="c_ssc_route_array" value="0b01" description="" />
   <keyValuePair key="c_ssc_route_width" value="1" description="" />
   <keyValuePair key="c_wdata_width" value="64" description="" />
   <keyValuePair key="c_write_acceptance" value="32" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="8" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_exit" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_mmu_v1_0_7_top/1" level="2" order="16" description="">
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_id_width" value="0" description="" />
   <keyValuePair key="c_is_cascaded" value="0" description="" />
   <keyValuePair key="c_msc_route_array" value="0b1" description="" />
   <keyValuePair key="c_msc_route_width" value="1" description="" />
   <keyValuePair key="c_num_msc" value="1" description="" />
   <keyValuePair key="c_num_read_outstanding" value="2" description="" />
   <keyValuePair key="c_num_seg" value="1" description="" />
   <keyValuePair key="c_num_write_outstanding" value="2" description="" />
   <keyValuePair key="c_rdata_width" value="64" description="" />
   <keyValuePair key="c_read_acceptance" value="32" description="" />
   <keyValuePair key="c_s_aruser_width" value="0" description="" />
   <keyValuePair key="c_s_awuser_width" value="0" description="" />
   <keyValuePair key="c_s_buser_width" value="0" description="" />
   <keyValuePair key="c_s_protocol" value="0" description="" />
   <keyValuePair key="c_s_ruser_width" value="0" description="" />
   <keyValuePair key="c_s_wuser_width" value="0" description="" />
   <keyValuePair key="c_seg_base_addr_array" value="0x0000000000000000" description="" />
   <keyValuePair key="c_seg_secure_read_array" value="0b0" description="" />
   <keyValuePair key="c_seg_secure_write_array" value="0b0" description="" />
   <keyValuePair key="c_seg_sep_route_array" value="0x0000000000000000" description="" />
   <keyValuePair key="c_seg_size_array" value="0x0000001c" description="" />
   <keyValuePair key="c_seg_supports_read_array" value="0x1" description="" />
   <keyValuePair key="c_seg_supports_write_array" value="0x1" description="" />
   <keyValuePair key="c_single_issuing" value="0" description="" />
   <keyValuePair key="c_supports_narrow" value="0" description="" />
   <keyValuePair key="c_supports_read_decerr" value="1" description="" />
   <keyValuePair key="c_supports_wrap" value="1" description="" />
   <keyValuePair key="c_supports_write_decerr" value="1" description="" />
   <keyValuePair key="c_wdata_width" value="64" description="" />
   <keyValuePair key="c_write_acceptance" value="32" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="7" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_mmu" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_mmu_v1_0_7_top/2" level="2" order="17" description="">
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_is_cascaded" value="0" description="" />
   <keyValuePair key="c_msc_route_array" value="0b1" description="" />
   <keyValuePair key="c_msc_route_width" value="1" description="" />
   <keyValuePair key="c_num_msc" value="1" description="" />
   <keyValuePair key="c_num_read_outstanding" value="2" description="" />
   <keyValuePair key="c_num_seg" value="1" description="" />
   <keyValuePair key="c_num_write_outstanding" value="16" description="" />
   <keyValuePair key="c_rdata_width" value="32" description="" />
   <keyValuePair key="c_read_acceptance" value="32" description="" />
   <keyValuePair key="c_s_aruser_width" value="0" description="" />
   <keyValuePair key="c_s_awuser_width" value="0" description="" />
   <keyValuePair key="c_s_buser_width" value="0" description="" />
   <keyValuePair key="c_s_protocol" value="0" description="" />
   <keyValuePair key="c_s_ruser_width" value="0" description="" />
   <keyValuePair key="c_s_wuser_width" value="0" description="" />
   <keyValuePair key="c_seg_base_addr_array" value="0x0000000010000000" description="" />
   <keyValuePair key="c_seg_secure_read_array" value="0b0" description="" />
   <keyValuePair key="c_seg_secure_write_array" value="0b0" description="" />
   <keyValuePair key="c_seg_sep_route_array" value="0x0000000000000000" description="" />
   <keyValuePair key="c_seg_size_array" value="0x0000001c" description="" />
   <keyValuePair key="c_seg_supports_read_array" value="0x1" description="" />
   <keyValuePair key="c_seg_supports_write_array" value="0x1" description="" />
   <keyValuePair key="c_single_issuing" value="0" description="" />
   <keyValuePair key="c_supports_narrow" value="0" description="" />
   <keyValuePair key="c_supports_read_decerr" value="1" description="" />
   <keyValuePair key="c_supports_wrap" value="1" description="" />
   <keyValuePair key="c_supports_write_decerr" value="1" description="" />
   <keyValuePair key="c_wdata_width" value="32" description="" />
   <keyValuePair key="c_write_acceptance" value="32" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="7" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_mmu" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_node_v1_0_10_top/1" level="2" order="18" description="">
   <keyValuePair key="c_aclk_relationship" value="1" description="" />
   <keyValuePair key="c_aclken_conversion" value="0" description="" />
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_arbiter_mode" value="1" description="" />
   <keyValuePair key="c_channel" value="2" description="" />
   <keyValuePair key="c_disable_ip" value="0" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x01" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_fifo_ip" value="0" description="" />
   <keyValuePair key="c_fifo_output_reg" value="1" description="" />
   <keyValuePair key="c_fifo_size" value="5" description="" />
   <keyValuePair key="c_fifo_type" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_m_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_m_pipeline" value="0" description="" />
   <keyValuePair key="c_m_send_pipeline" value="0" description="" />
   <keyValuePair key="c_max_payld_bytes" value="8" description="" />
   <keyValuePair key="c_num_mi" value="1" description="" />
   <keyValuePair key="c_num_outstanding" value="2" description="" />
   <keyValuePair key="c_num_si" value="1" description="" />
   <keyValuePair key="c_payld_width" value="138" description="" />
   <keyValuePair key="c_s_latency" value="0" description="" />
   <keyValuePair key="c_s_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_s_pipeline" value="0" description="" />
   <keyValuePair key="c_sc_route_width" value="1" description="" />
   <keyValuePair key="c_synchronization_stages" value="3" description="" />
   <keyValuePair key="c_user_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_user_width" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_node" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_node_v1_0_10_top/2" level="2" order="19" description="">
   <keyValuePair key="c_aclk_relationship" value="1" description="" />
   <keyValuePair key="c_aclken_conversion" value="0" description="" />
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_arbiter_mode" value="1" description="" />
   <keyValuePair key="c_channel" value="0" description="" />
   <keyValuePair key="c_disable_ip" value="0" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x01" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_fifo_ip" value="0" description="" />
   <keyValuePair key="c_fifo_output_reg" value="1" description="" />
   <keyValuePair key="c_fifo_size" value="5" description="" />
   <keyValuePair key="c_fifo_type" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_m_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_m_pipeline" value="0" description="" />
   <keyValuePair key="c_m_send_pipeline" value="0" description="" />
   <keyValuePair key="c_max_payld_bytes" value="8" description="" />
   <keyValuePair key="c_num_mi" value="1" description="" />
   <keyValuePair key="c_num_outstanding" value="2" description="" />
   <keyValuePair key="c_num_si" value="1" description="" />
   <keyValuePair key="c_payld_width" value="83" description="" />
   <keyValuePair key="c_s_latency" value="0" description="" />
   <keyValuePair key="c_s_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_s_pipeline" value="0" description="" />
   <keyValuePair key="c_sc_route_width" value="1" description="" />
   <keyValuePair key="c_synchronization_stages" value="3" description="" />
   <keyValuePair key="c_user_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_user_width" value="512" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_node" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_node_v1_0_10_top/3" level="2" order="20" description="">
   <keyValuePair key="c_aclk_relationship" value="1" description="" />
   <keyValuePair key="c_aclken_conversion" value="0" description="" />
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_arbiter_mode" value="1" description="" />
   <keyValuePair key="c_channel" value="2" description="" />
   <keyValuePair key="c_disable_ip" value="0" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x01" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_fifo_ip" value="0" description="" />
   <keyValuePair key="c_fifo_output_reg" value="1" description="" />
   <keyValuePair key="c_fifo_size" value="5" description="" />
   <keyValuePair key="c_fifo_type" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_m_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_m_pipeline" value="0" description="" />
   <keyValuePair key="c_m_send_pipeline" value="0" description="" />
   <keyValuePair key="c_max_payld_bytes" value="8" description="" />
   <keyValuePair key="c_num_mi" value="1" description="" />
   <keyValuePair key="c_num_outstanding" value="2" description="" />
   <keyValuePair key="c_num_si" value="1" description="" />
   <keyValuePair key="c_payld_width" value="138" description="" />
   <keyValuePair key="c_s_latency" value="0" description="" />
   <keyValuePair key="c_s_num_bytes_array" value="0x00000004" description="" />
   <keyValuePair key="c_s_pipeline" value="0" description="" />
   <keyValuePair key="c_sc_route_width" value="1" description="" />
   <keyValuePair key="c_synchronization_stages" value="3" description="" />
   <keyValuePair key="c_user_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_user_width" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_node" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_node_v1_0_10_top/4" level="2" order="21" description="">
   <keyValuePair key="c_aclk_relationship" value="1" description="" />
   <keyValuePair key="c_aclken_conversion" value="0" description="" />
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_arbiter_mode" value="1" description="" />
   <keyValuePair key="c_channel" value="3" description="" />
   <keyValuePair key="c_disable_ip" value="0" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x01" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_fifo_ip" value="0" description="" />
   <keyValuePair key="c_fifo_output_reg" value="1" description="" />
   <keyValuePair key="c_fifo_size" value="5" description="" />
   <keyValuePair key="c_fifo_type" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_m_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_m_pipeline" value="0" description="" />
   <keyValuePair key="c_m_send_pipeline" value="0" description="" />
   <keyValuePair key="c_max_payld_bytes" value="8" description="" />
   <keyValuePair key="c_num_mi" value="1" description="" />
   <keyValuePair key="c_num_outstanding" value="16" description="" />
   <keyValuePair key="c_num_si" value="1" description="" />
   <keyValuePair key="c_payld_width" value="138" description="" />
   <keyValuePair key="c_s_latency" value="0" description="" />
   <keyValuePair key="c_s_num_bytes_array" value="0x00000004" description="" />
   <keyValuePair key="c_s_pipeline" value="0" description="" />
   <keyValuePair key="c_sc_route_width" value="1" description="" />
   <keyValuePair key="c_synchronization_stages" value="3" description="" />
   <keyValuePair key="c_user_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_user_width" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_node" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_node_v1_0_10_top/5" level="2" order="22" description="">
   <keyValuePair key="c_aclk_relationship" value="1" description="" />
   <keyValuePair key="c_aclken_conversion" value="0" description="" />
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_arbiter_mode" value="1" description="" />
   <keyValuePair key="c_channel" value="4" description="" />
   <keyValuePair key="c_disable_ip" value="0" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x01" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_fifo_ip" value="0" description="" />
   <keyValuePair key="c_fifo_output_reg" value="1" description="" />
   <keyValuePair key="c_fifo_size" value="5" description="" />
   <keyValuePair key="c_fifo_type" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_m_num_bytes_array" value="0x0000000400000004" description="" />
   <keyValuePair key="c_m_pipeline" value="0" description="" />
   <keyValuePair key="c_m_send_pipeline" value="0" description="" />
   <keyValuePair key="c_max_payld_bytes" value="8" description="" />
   <keyValuePair key="c_num_mi" value="1" description="" />
   <keyValuePair key="c_num_outstanding" value="16" description="" />
   <keyValuePair key="c_num_si" value="1" description="" />
   <keyValuePair key="c_payld_width" value="5" description="" />
   <keyValuePair key="c_s_latency" value="0" description="" />
   <keyValuePair key="c_s_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_s_pipeline" value="0" description="" />
   <keyValuePair key="c_sc_route_width" value="1" description="" />
   <keyValuePair key="c_synchronization_stages" value="3" description="" />
   <keyValuePair key="c_user_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_user_width" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_node" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_node_v1_0_10_top/6" level="2" order="23" description="">
   <keyValuePair key="c_aclk_relationship" value="1" description="" />
   <keyValuePair key="c_aclken_conversion" value="0" description="" />
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_arbiter_mode" value="1" description="" />
   <keyValuePair key="c_channel" value="0" description="" />
   <keyValuePair key="c_disable_ip" value="0" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x01" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_fifo_ip" value="0" description="" />
   <keyValuePair key="c_fifo_output_reg" value="1" description="" />
   <keyValuePair key="c_fifo_size" value="5" description="" />
   <keyValuePair key="c_fifo_type" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_m_num_bytes_array" value="0x0000000400000004" description="" />
   <keyValuePair key="c_m_pipeline" value="0" description="" />
   <keyValuePair key="c_m_send_pipeline" value="0" description="" />
   <keyValuePair key="c_max_payld_bytes" value="8" description="" />
   <keyValuePair key="c_num_mi" value="1" description="" />
   <keyValuePair key="c_num_outstanding" value="2" description="" />
   <keyValuePair key="c_num_si" value="1" description="" />
   <keyValuePair key="c_payld_width" value="83" description="" />
   <keyValuePair key="c_s_latency" value="0" description="" />
   <keyValuePair key="c_s_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_s_pipeline" value="0" description="" />
   <keyValuePair key="c_sc_route_width" value="1" description="" />
   <keyValuePair key="c_synchronization_stages" value="3" description="" />
   <keyValuePair key="c_user_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_user_width" value="512" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_node" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_node_v1_0_10_top/7" level="2" order="24" description="">
   <keyValuePair key="c_aclk_relationship" value="1" description="" />
   <keyValuePair key="c_aclken_conversion" value="0" description="" />
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_arbiter_mode" value="1" description="" />
   <keyValuePair key="c_channel" value="1" description="" />
   <keyValuePair key="c_disable_ip" value="0" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x01" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_fifo_ip" value="0" description="" />
   <keyValuePair key="c_fifo_output_reg" value="1" description="" />
   <keyValuePair key="c_fifo_size" value="5" description="" />
   <keyValuePair key="c_fifo_type" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_m_num_bytes_array" value="0x00000008" description="" />
   <keyValuePair key="c_m_pipeline" value="0" description="" />
   <keyValuePair key="c_m_send_pipeline" value="0" description="" />
   <keyValuePair key="c_max_payld_bytes" value="8" description="" />
   <keyValuePair key="c_num_mi" value="1" description="" />
   <keyValuePair key="c_num_outstanding" value="16" description="" />
   <keyValuePair key="c_num_si" value="1" description="" />
   <keyValuePair key="c_payld_width" value="88" description="" />
   <keyValuePair key="c_s_latency" value="0" description="" />
   <keyValuePair key="c_s_num_bytes_array" value="0x00000004" description="" />
   <keyValuePair key="c_s_pipeline" value="0" description="" />
   <keyValuePair key="c_sc_route_width" value="1" description="" />
   <keyValuePair key="c_synchronization_stages" value="3" description="" />
   <keyValuePair key="c_user_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_user_width" value="512" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_node" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_si_converter_v1_0_7_top/1" level="2" order="25" description="">
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x1" description="" />
   <keyValuePair key="c_has_burst" value="0" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_is_cascaded" value="0" description="" />
   <keyValuePair key="c_limit_read_length" value="0" description="" />
   <keyValuePair key="c_limit_write_length" value="0" description="" />
   <keyValuePair key="c_max_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_max_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_mep_identifier_width" value="1" description="" />
   <keyValuePair key="c_msc_rdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_msc_wdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_num_msc" value="1" description="" />
   <keyValuePair key="c_num_read_outstanding" value="2" description="" />
   <keyValuePair key="c_num_read_threads" value="1" description="" />
   <keyValuePair key="c_num_seg" value="1" description="" />
   <keyValuePair key="c_num_write_outstanding" value="2" description="" />
   <keyValuePair key="c_num_write_threads" value="1" description="" />
   <keyValuePair key="c_rdata_width" value="64" description="" />
   <keyValuePair key="c_read_acceptance" value="32" description="" />
   <keyValuePair key="c_read_watermark" value="0" description="" />
   <keyValuePair key="c_s_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_s_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_sep_protocol_array" value="0x00000001" description="" />
   <keyValuePair key="c_sep_rdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_sep_wdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_single_issuing" value="0" description="" />
   <keyValuePair key="c_supports_narrow" value="0" description="" />
   <keyValuePair key="c_wdata_width" value="64" description="" />
   <keyValuePair key="c_write_acceptance" value="32" description="" />
   <keyValuePair key="c_write_watermark" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="7" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_si_converter" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_si_converter_v1_0_7_top/2" level="2" order="26" description="">
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x1" description="" />
   <keyValuePair key="c_has_burst" value="1" description="" />
   <keyValuePair key="c_id_width" value="1" description="" />
   <keyValuePair key="c_is_cascaded" value="0" description="" />
   <keyValuePair key="c_limit_read_length" value="0" description="" />
   <keyValuePair key="c_limit_write_length" value="0" description="" />
   <keyValuePair key="c_max_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_max_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_mep_identifier_width" value="1" description="" />
   <keyValuePair key="c_msc_rdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_msc_wdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_num_msc" value="1" description="" />
   <keyValuePair key="c_num_read_outstanding" value="2" description="" />
   <keyValuePair key="c_num_read_threads" value="1" description="" />
   <keyValuePair key="c_num_seg" value="1" description="" />
   <keyValuePair key="c_num_write_outstanding" value="16" description="" />
   <keyValuePair key="c_num_write_threads" value="1" description="" />
   <keyValuePair key="c_rdata_width" value="32" description="" />
   <keyValuePair key="c_read_acceptance" value="32" description="" />
   <keyValuePair key="c_read_watermark" value="0" description="" />
   <keyValuePair key="c_s_ruser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_s_wuser_bits_per_byte" value="0" description="" />
   <keyValuePair key="c_sep_protocol_array" value="0x00000001" description="" />
   <keyValuePair key="c_sep_rdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_sep_wdata_width_array" value="0x00000040" description="" />
   <keyValuePair key="c_single_issuing" value="0" description="" />
   <keyValuePair key="c_supports_narrow" value="0" description="" />
   <keyValuePair key="c_wdata_width" value="32" description="" />
   <keyValuePair key="c_write_acceptance" value="32" description="" />
   <keyValuePair key="c_write_watermark" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="7" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_si_converter" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="sc_transaction_regulator_v1_0_8_top/1" level="2" order="27" description="">
   <keyValuePair key="c_addr_width" value="32" description="" />
   <keyValuePair key="c_enable_pipelining" value="0x1" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_is_cascaded" value="0" description="" />
   <keyValuePair key="c_m_id_width" value="1" description="" />
   <keyValuePair key="c_mep_identifier" value="0" description="" />
   <keyValuePair key="c_mep_identifier_width" value="1" description="" />
   <keyValuePair key="c_num_read_outstanding" value="2" description="" />
   <keyValuePair key="c_num_read_threads" value="1" description="" />
   <keyValuePair key="c_num_write_outstanding" value="16" description="" />
   <keyValuePair key="c_num_write_threads" value="1" description="" />
   <keyValuePair key="c_rdata_width" value="32" description="" />
   <keyValuePair key="c_read_acceptance" value="32" description="" />
   <keyValuePair key="c_s_id_width" value="1" description="" />
   <keyValuePair key="c_sep_route_width" value="1" description="" />
   <keyValuePair key="c_single_issuing" value="0" description="" />
   <keyValuePair key="c_supports_read_deadlock" value="0" description="" />
   <keyValuePair key="c_supports_write_deadlock" value="0" description="" />
   <keyValuePair key="c_wdata_width" value="32" description="" />
   <keyValuePair key="c_write_acceptance" value="32" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="8" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="sc_transaction_regulator" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.0" description="" />
  </section>
  <section name="v_axi4s_vid_out_v4_0_10/1" level="2" order="28" description="">
   <keyValuePair key="c_addr_width" value="10" description="" />
   <keyValuePair key="c_addr_width_pixel_remap_420" value="10" description="" />
   <keyValuePair key="c_components_per_pixel" value="4" description="" />
   <keyValuePair key="c_family" value="zynq" description="" />
   <keyValuePair key="c_has_async_clk" value="1" description="" />
   <keyValuePair key="c_hysteresis_level" value="12" description="" />
   <keyValuePair key="c_include_pixel_remap_420" value="0" description="" />
   <keyValuePair key="c_include_pixel_repeat" value="0" description="" />
   <keyValuePair key="c_native_component_width" value="8" description="" />
   <keyValuePair key="c_native_data_width" value="32" description="" />
   <keyValuePair key="c_pixels_per_clock" value="1" description="" />
   <keyValuePair key="c_s_axis_component_width" value="8" description="" />
   <keyValuePair key="c_s_axis_tdata_width" value="32" description="" />
   <keyValuePair key="c_sync_lock_threshold" value="4" description="" />
   <keyValuePair key="c_vtg_master_slave" value="0" description="" />
   <keyValuePair key="core_container" value="false" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="10" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="v_axi4s_vid_out" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="4.0" description="" />
  </section>
  <section name="v_tc/1" level="2" order="29" description="">
   <keyValuePair key="c_det_achroma_en" value="0" description="" />
   <keyValuePair key="c_det_avideo_en" value="1" description="" />
   <keyValuePair key="c_det_fieldid_en" value="0" description="" />
   <keyValuePair key="c_det_hblank_en" value="1" description="" />
   <keyValuePair key="c_det_hsync_en" value="1" description="" />
   <keyValuePair key="c_det_vblank_en" value="1" description="" />
   <keyValuePair key="c_det_vsync_en" value="1" description="" />
   <keyValuePair key="c_detect_en" value="0" description="" />
   <keyValuePair key="c_fsync_hstart0" value="0" description="" />
   <keyValuePair key="c_fsync_hstart1" value="0" description="" />
   <keyValuePair key="c_fsync_hstart10" value="0" description="" />
   <keyValuePair key="c_fsync_hstart11" value="0" description="" />
   <keyValuePair key="c_fsync_hstart12" value="0" description="" />
   <keyValuePair key="c_fsync_hstart13" value="0" description="" />
   <keyValuePair key="c_fsync_hstart14" value="0" description="" />
   <keyValuePair key="c_fsync_hstart15" value="0" description="" />
   <keyValuePair key="c_fsync_hstart2" value="0" description="" />
   <keyValuePair key="c_fsync_hstart3" value="0" description="" />
   <keyValuePair key="c_fsync_hstart4" value="0" description="" />
   <keyValuePair key="c_fsync_hstart5" value="0" description="" />
   <keyValuePair key="c_fsync_hstart6" value="0" description="" />
   <keyValuePair key="c_fsync_hstart7" value="0" description="" />
   <keyValuePair key="c_fsync_hstart8" value="0" description="" />
   <keyValuePair key="c_fsync_hstart9" value="0" description="" />
   <keyValuePair key="c_fsync_vstart0" value="0" description="" />
   <keyValuePair key="c_fsync_vstart1" value="0" description="" />
   <keyValuePair key="c_fsync_vstart10" value="0" description="" />
   <keyValuePair key="c_fsync_vstart11" value="0" description="" />
   <keyValuePair key="c_fsync_vstart12" value="0" description="" />
   <keyValuePair key="c_fsync_vstart13" value="0" description="" />
   <keyValuePair key="c_fsync_vstart14" value="0" description="" />
   <keyValuePair key="c_fsync_vstart15" value="0" description="" />
   <keyValuePair key="c_fsync_vstart2" value="0" description="" />
   <keyValuePair key="c_fsync_vstart3" value="0" description="" />
   <keyValuePair key="c_fsync_vstart4" value="0" description="" />
   <keyValuePair key="c_fsync_vstart5" value="0" description="" />
   <keyValuePair key="c_fsync_vstart6" value="0" description="" />
   <keyValuePair key="c_fsync_vstart7" value="0" description="" />
   <keyValuePair key="c_fsync_vstart8" value="0" description="" />
   <keyValuePair key="c_fsync_vstart9" value="0" description="" />
   <keyValuePair key="c_gen_achroma_en" value="0" description="" />
   <keyValuePair key="c_gen_achroma_polarity" value="1" description="" />
   <keyValuePair key="c_gen_auto_switch" value="0" description="" />
   <keyValuePair key="c_gen_avideo_en" value="1" description="" />
   <keyValuePair key="c_gen_avideo_polarity" value="1" description="" />
   <keyValuePair key="c_gen_cparity" value="0" description="" />
   <keyValuePair key="c_gen_f0_vblank_hend" value="1280" description="" />
   <keyValuePair key="c_gen_f0_vblank_hstart" value="1280" description="" />
   <keyValuePair key="c_gen_f0_vframe_size" value="750" description="" />
   <keyValuePair key="c_gen_f0_vsync_hend" value="1280" description="" />
   <keyValuePair key="c_gen_f0_vsync_hstart" value="1280" description="" />
   <keyValuePair key="c_gen_f0_vsync_vend" value="729" description="" />
   <keyValuePair key="c_gen_f0_vsync_vstart" value="724" description="" />
   <keyValuePair key="c_gen_f1_vblank_hend" value="1280" description="" />
   <keyValuePair key="c_gen_f1_vblank_hstart" value="1280" description="" />
   <keyValuePair key="c_gen_f1_vframe_size" value="750" description="" />
   <keyValuePair key="c_gen_f1_vsync_hend" value="1280" description="" />
   <keyValuePair key="c_gen_f1_vsync_hstart" value="1280" description="" />
   <keyValuePair key="c_gen_f1_vsync_vend" value="729" description="" />
   <keyValuePair key="c_gen_f1_vsync_vstart" value="724" description="" />
   <keyValuePair key="c_gen_fieldid_en" value="0" description="" />
   <keyValuePair key="c_gen_fieldid_polarity" value="1" description="" />
   <keyValuePair key="c_gen_hactive_size" value="1280" description="" />
   <keyValuePair key="c_gen_hblank_en" value="1" description="" />
   <keyValuePair key="c_gen_hblank_polarity" value="1" description="" />
   <keyValuePair key="c_gen_hframe_size" value="1650" description="" />
   <keyValuePair key="c_gen_hsync_en" value="1" description="" />
   <keyValuePair key="c_gen_hsync_end" value="1430" description="" />
   <keyValuePair key="c_gen_hsync_polarity" value="1" description="" />
   <keyValuePair key="c_gen_hsync_start" value="1390" description="" />
   <keyValuePair key="c_gen_interlaced" value="0" description="" />
   <keyValuePair key="c_gen_vactive_size" value="720" description="" />
   <keyValuePair key="c_gen_vblank_en" value="1" description="" />
   <keyValuePair key="c_gen_vblank_polarity" value="1" description="" />
   <keyValuePair key="c_gen_video_format" value="2" description="" />
   <keyValuePair key="c_gen_vsync_en" value="1" description="" />
   <keyValuePair key="c_gen_vsync_polarity" value="1" description="" />
   <keyValuePair key="c_generate_en" value="1" description="" />
   <keyValuePair key="c_has_axi4_lite" value="1" description="" />
   <keyValuePair key="c_has_intc_if" value="0" description="" />
   <keyValuePair key="c_interlace_en" value="0" description="" />
   <keyValuePair key="c_max_lines" value="4096" description="" />
   <keyValuePair key="c_max_pixels" value="4096" description="" />
   <keyValuePair key="c_num_fsyncs" value="1" description="" />
   <keyValuePair key="c_sync_en" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="13" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="v_tc" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="6.1" description="" />
  </section>
  <section name="xlconcat_v2_1_1_xlconcat/1" level="2" order="30" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="dout_width" value="2" description="" />
   <keyValuePair key="in0_width" value="1" description="" />
   <keyValuePair key="in10_width" value="1" description="" />
   <keyValuePair key="in11_width" value="1" description="" />
   <keyValuePair key="in12_width" value="1" description="" />
   <keyValuePair key="in13_width" value="1" description="" />
   <keyValuePair key="in14_width" value="1" description="" />
   <keyValuePair key="in15_width" value="1" description="" />
   <keyValuePair key="in16_width" value="1" description="" />
   <keyValuePair key="in17_width" value="1" description="" />
   <keyValuePair key="in18_width" value="1" description="" />
   <keyValuePair key="in19_width" value="1" description="" />
   <keyValuePair key="in1_width" value="1" description="" />
   <keyValuePair key="in20_width" value="1" description="" />
   <keyValuePair key="in21_width" value="1" description="" />
   <keyValuePair key="in22_width" value="1" description="" />
   <keyValuePair key="in23_width" value="1" description="" />
   <keyValuePair key="in24_width" value="1" description="" />
   <keyValuePair key="in25_width" value="1" description="" />
   <keyValuePair key="in26_width" value="1" description="" />
   <keyValuePair key="in27_width" value="1" description="" />
   <keyValuePair key="in28_width" value="1" description="" />
   <keyValuePair key="in29_width" value="1" description="" />
   <keyValuePair key="in2_width" value="1" description="" />
   <keyValuePair key="in30_width" value="1" description="" />
   <keyValuePair key="in31_width" value="1" description="" />
   <keyValuePair key="in3_width" value="1" description="" />
   <keyValuePair key="in4_width" value="1" description="" />
   <keyValuePair key="in5_width" value="1" description="" />
   <keyValuePair key="in6_width" value="1" description="" />
   <keyValuePair key="in7_width" value="1" description="" />
   <keyValuePair key="in8_width" value="1" description="" />
   <keyValuePair key="in9_width" value="1" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="num_ports" value="2" description="" />
   <keyValuePair key="x_ipcorerevision" value="1" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="xlconcat" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="2.1" description="" />
  </section>
  <section name="xlconstant_v1_1_5_xlconstant/1" level="2" order="31" description="">
   <keyValuePair key="const_val" value="0x1" description="" />
   <keyValuePair key="const_width" value="1" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="5" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="xlconstant" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.1" description="" />
  </section>
  <section name="xlconstant_v1_1_5_xlconstant/2" level="2" order="32" description="">
   <keyValuePair key="const_val" value="0x1" description="" />
   <keyValuePair key="const_width" value="1" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="iptotal" value="1" description="" />
   <keyValuePair key="x_ipcorerevision" value="5" description="" />
   <keyValuePair key="x_iplanguage" value="VERILOG" description="" />
   <keyValuePair key="x_iplibrary" value="ip" description="" />
   <keyValuePair key="x_ipname" value="xlconstant" description="" />
   <keyValuePair key="x_ipproduct" value="Vivado 2018.3" description="" />
   <keyValuePair key="x_ipsimlanguage" value="MIXED" description="" />
   <keyValuePair key="x_ipvendor" value="xilinx.com" description="" />
   <keyValuePair key="x_ipversion" value="1.1" description="" />
  </section>
  <section name="xpm_cdc_gray/1" level="2" order="33" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="dest_sync_ff" value="4" description="" />
   <keyValuePair key="init_sync_ff" value="1" description="" />
   <keyValuePair key="iptotal" value="7" description="" />
   <keyValuePair key="reg_output" value="0" description="" />
   <keyValuePair key="sim_assert_chk" value="0" description="" />
   <keyValuePair key="sim_lossless_gray_chk" value="0" description="" />
   <keyValuePair key="version" value="0" description="" />
   <keyValuePair key="width" value="10" description="" />
  </section>
  <section name="xpm_cdc_sync_rst/1" level="2" order="34" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="def_val" value="1&apos;b0" description="" />
   <keyValuePair key="dest_sync_ff" value="4" description="" />
   <keyValuePair key="init" value="0" description="" />
   <keyValuePair key="init_sync_ff" value="1" description="" />
   <keyValuePair key="iptotal" value="4" description="" />
   <keyValuePair key="sim_assert_chk" value="0" description="" />
   <keyValuePair key="version" value="0" description="" />
  </section>
  <section name="xpm_fifo_async/1" level="2" order="35" description="">
   <keyValuePair key="cdc_sync_stages" value="4" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="dout_reset_value" value="0" description="" />
   <keyValuePair key="ecc_mode" value="no_ecc" description="" />
   <keyValuePair key="en_adv_feature_async" value="16&apos;b0000011100000111" description="" />
   <keyValuePair key="fifo_memory_type" value="auto" description="" />
   <keyValuePair key="fifo_read_latency" value="0" description="" />
   <keyValuePair key="fifo_write_depth" value="1024" description="" />
   <keyValuePair key="full_reset_value" value="1" description="" />
   <keyValuePair key="iptotal" value="2" description="" />
   <keyValuePair key="p_common_clock" value="0" description="" />
   <keyValuePair key="p_ecc_mode" value="0" description="" />
   <keyValuePair key="p_fifo_memory_type" value="0" description="" />
   <keyValuePair key="p_read_mode" value="1" description="" />
   <keyValuePair key="p_wakeup_time" value="2" description="" />
   <keyValuePair key="prog_empty_thresh" value="10" description="" />
   <keyValuePair key="prog_full_thresh" value="10" description="" />
   <keyValuePair key="rd_data_count_width" value="11" description="" />
   <keyValuePair key="read_data_width" value="35" description="" />
   <keyValuePair key="read_mode" value="fwft" description="" />
   <keyValuePair key="related_clocks" value="0" description="" />
   <keyValuePair key="use_adv_features" value="0707" description="" />
   <keyValuePair key="wakeup_time" value="0" description="" />
   <keyValuePair key="wr_data_count_width" value="11" description="" />
   <keyValuePair key="write_data_width" value="35" description="" />
  </section>
  <section name="xpm_fifo_base/1" level="2" order="36" description="">
   <keyValuePair key="both_stages_valid" value="3" description="" />
   <keyValuePair key="cdc_dest_sync_ff" value="4" description="" />
   <keyValuePair key="common_clock" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="dout_reset_value" value="0" description="" />
   <keyValuePair key="ecc_mode" value="0" description="" />
   <keyValuePair key="en_adv_feature" value="16&apos;b0000011100000111" description="" />
   <keyValuePair key="en_ae" value="1&apos;b0" description="" />
   <keyValuePair key="en_af" value="1&apos;b0" description="" />
   <keyValuePair key="en_dvld" value="1&apos;b0" description="" />
   <keyValuePair key="en_of" value="1&apos;b1" description="" />
   <keyValuePair key="en_pe" value="1&apos;b1" description="" />
   <keyValuePair key="en_pf" value="1&apos;b1" description="" />
   <keyValuePair key="en_rdc" value="1&apos;b1" description="" />
   <keyValuePair key="en_uf" value="1&apos;b1" description="" />
   <keyValuePair key="en_wack" value="1&apos;b0" description="" />
   <keyValuePair key="en_wdc" value="1&apos;b1" description="" />
   <keyValuePair key="enable_ecc" value="0" description="" />
   <keyValuePair key="fg_eq_asym_dout" value="1&apos;b0" description="" />
   <keyValuePair key="fifo_mem_type" value="0" description="" />
   <keyValuePair key="fifo_memory_type" value="0" description="" />
   <keyValuePair key="fifo_read_depth" value="1024" description="" />
   <keyValuePair key="fifo_read_latency" value="0" description="" />
   <keyValuePair key="fifo_size" value="35840" description="" />
   <keyValuePair key="fifo_write_depth" value="1024" description="" />
   <keyValuePair key="full_reset_value" value="1" description="" />
   <keyValuePair key="full_rst_val" value="1&apos;b1" description="" />
   <keyValuePair key="invalid" value="0" description="" />
   <keyValuePair key="iptotal" value="4" description="" />
   <keyValuePair key="pe_thresh_adj" value="8" description="" />
   <keyValuePair key="pe_thresh_max" value="1019" description="" />
   <keyValuePair key="pe_thresh_min" value="5" description="" />
   <keyValuePair key="pf_thresh_adj" value="8" description="" />
   <keyValuePair key="pf_thresh_max" value="1019" description="" />
   <keyValuePair key="pf_thresh_min" value="9" description="" />
   <keyValuePair key="prog_empty_thresh" value="10" description="" />
   <keyValuePair key="prog_full_thresh" value="10" description="" />
   <keyValuePair key="rd_data_count_width" value="11" description="" />
   <keyValuePair key="rd_dc_width_ext" value="11" description="" />
   <keyValuePair key="rd_latency" value="2" description="" />
   <keyValuePair key="rd_mode" value="1" description="" />
   <keyValuePair key="rd_pntr_width" value="10" description="" />
   <keyValuePair key="read_data_width" value="35" description="" />
   <keyValuePair key="read_mode" value="1" description="" />
   <keyValuePair key="related_clocks" value="0" description="" />
   <keyValuePair key="remove_wr_rd_prot_logic" value="0" description="" />
   <keyValuePair key="sim_assert_chk" value="0" description="" />
   <keyValuePair key="stage1_valid" value="2" description="" />
   <keyValuePair key="stage2_valid" value="1" description="" />
   <keyValuePair key="use_adv_features" value="0707" description="" />
   <keyValuePair key="version" value="0" description="" />
   <keyValuePair key="wakeup_time" value="0" description="" />
   <keyValuePair key="width_ratio" value="1" description="" />
   <keyValuePair key="wr_data_count_width" value="11" description="" />
   <keyValuePair key="wr_dc_width_ext" value="11" description="" />
   <keyValuePair key="wr_depth_log" value="10" description="" />
   <keyValuePair key="wr_pntr_width" value="10" description="" />
   <keyValuePair key="wr_rd_ratio" value="0" description="" />
   <keyValuePair key="wr_width_log" value="6" description="" />
   <keyValuePair key="write_data_width" value="35" description="" />
  </section>
  <section name="xpm_fifo_sync/1" level="2" order="37" description="">
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="dout_reset_value" value="0" description="" />
   <keyValuePair key="ecc_mode" value="no_ecc" description="" />
   <keyValuePair key="en_adv_feature_sync" value="16&apos;b0001111100011111" description="" />
   <keyValuePair key="fifo_memory_type" value="block" description="" />
   <keyValuePair key="fifo_read_latency" value="0" description="" />
   <keyValuePair key="fifo_write_depth" value="512" description="" />
   <keyValuePair key="full_reset_value" value="1" description="" />
   <keyValuePair key="iptotal" value="2" description="" />
   <keyValuePair key="p_common_clock" value="1" description="" />
   <keyValuePair key="p_ecc_mode" value="0" description="" />
   <keyValuePair key="p_fifo_memory_type" value="2" description="" />
   <keyValuePair key="p_read_mode" value="1" description="" />
   <keyValuePair key="p_wakeup_time" value="2" description="" />
   <keyValuePair key="prog_empty_thresh" value="10" description="" />
   <keyValuePair key="prog_full_thresh" value="10" description="" />
   <keyValuePair key="rd_data_count_width" value="4" description="" />
   <keyValuePair key="read_data_width" value="67" description="" />
   <keyValuePair key="read_mode" value="fwft" description="" />
   <keyValuePair key="use_adv_features" value="1F1F" description="" />
   <keyValuePair key="wakeup_time" value="0" description="" />
   <keyValuePair key="wr_data_count_width" value="10" description="" />
   <keyValuePair key="write_data_width" value="67" description="" />
  </section>
  <section name="xpm_memory_base/1" level="2" order="38" description="">
   <keyValuePair key="addr_width_a" value="10" description="" />
   <keyValuePair key="addr_width_b" value="10" description="" />
   <keyValuePair key="auto_sleep_time" value="0" description="" />
   <keyValuePair key="byte_write_width_a" value="35" description="" />
   <keyValuePair key="byte_write_width_b" value="35" description="" />
   <keyValuePair key="clocking_mode" value="1" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="ecc_mode" value="0" description="" />
   <keyValuePair key="iptotal" value="11" description="" />
   <keyValuePair key="max_num_char" value="0" description="" />
   <keyValuePair key="memory_optimization" value="true" description="" />
   <keyValuePair key="memory_primitive" value="0" description="" />
   <keyValuePair key="memory_size" value="35840" description="" />
   <keyValuePair key="memory_type" value="1" description="" />
   <keyValuePair key="message_control" value="0" description="" />
   <keyValuePair key="num_char_loc" value="0" description="" />
   <keyValuePair key="p_ecc_mode" value="no_ecc" description="" />
   <keyValuePair key="p_enable_byte_write_a" value="0" description="" />
   <keyValuePair key="p_enable_byte_write_b" value="0" description="" />
   <keyValuePair key="p_max_depth_data" value="1024" description="" />
   <keyValuePair key="p_memory_opt" value="yes" description="" />
   <keyValuePair key="p_memory_primitive" value="auto" description="" />
   <keyValuePair key="p_min_width_data" value="35" description="" />
   <keyValuePair key="p_min_width_data_a" value="35" description="" />
   <keyValuePair key="p_min_width_data_b" value="35" description="" />
   <keyValuePair key="p_min_width_data_ecc" value="35" description="" />
   <keyValuePair key="p_min_width_data_ldw" value="4" description="" />
   <keyValuePair key="p_min_width_data_shft" value="35" description="" />
   <keyValuePair key="p_num_cols_write_a" value="1" description="" />
   <keyValuePair key="p_num_cols_write_b" value="1" description="" />
   <keyValuePair key="p_num_rows_read_a" value="1" description="" />
   <keyValuePair key="p_num_rows_read_b" value="1" description="" />
   <keyValuePair key="p_num_rows_write_a" value="1" description="" />
   <keyValuePair key="p_num_rows_write_b" value="1" description="" />
   <keyValuePair key="p_sdp_write_mode" value="yes" description="" />
   <keyValuePair key="p_width_addr_lsb_read_a" value="0" description="" />
   <keyValuePair key="p_width_addr_lsb_read_b" value="0" description="" />
   <keyValuePair key="p_width_addr_lsb_write_a" value="0" description="" />
   <keyValuePair key="p_width_addr_lsb_write_b" value="0" description="" />
   <keyValuePair key="p_width_addr_read_a" value="10" description="" />
   <keyValuePair key="p_width_addr_read_b" value="10" description="" />
   <keyValuePair key="p_width_addr_write_a" value="10" description="" />
   <keyValuePair key="p_width_addr_write_b" value="10" description="" />
   <keyValuePair key="p_width_col_write_a" value="35" description="" />
   <keyValuePair key="p_width_col_write_b" value="35" description="" />
   <keyValuePair key="read_data_width_a" value="35" description="" />
   <keyValuePair key="read_data_width_b" value="35" description="" />
   <keyValuePair key="read_latency_a" value="2" description="" />
   <keyValuePair key="read_latency_b" value="2" description="" />
   <keyValuePair key="read_reset_value_a" value="0" description="" />
   <keyValuePair key="read_reset_value_b" value="0" description="" />
   <keyValuePair key="rst_mode_a" value="SYNC" description="" />
   <keyValuePair key="rst_mode_b" value="SYNC" description="" />
   <keyValuePair key="use_embedded_constraint" value="0" description="" />
   <keyValuePair key="use_mem_init" value="0" description="" />
   <keyValuePair key="version" value="0" description="" />
   <keyValuePair key="wakeup_time" value="0" description="" />
   <keyValuePair key="write_data_width" value="67" description="" />
   <keyValuePair key="write_data_width_a" value="35" description="" />
   <keyValuePair key="write_data_width_b" value="35" description="" />
   <keyValuePair key="write_mode_a" value="2" description="" />
   <keyValuePair key="write_mode_b" value="2" description="" />
  </section>
  <section name="xpm_memory_sdpram/1" level="2" order="39" description="">
   <keyValuePair key="addr_width_a" value="5" description="" />
   <keyValuePair key="addr_width_b" value="5" description="" />
   <keyValuePair key="auto_sleep_time" value="0" description="" />
   <keyValuePair key="byte_write_width_a" value="106" description="" />
   <keyValuePair key="clocking_mode" value="0" description="" />
   <keyValuePair key="core_container" value="NA" description="" />
   <keyValuePair key="ecc_mode" value="0" description="" />
   <keyValuePair key="iptotal" value="7" description="" />
   <keyValuePair key="memory_optimization" value="true" description="" />
   <keyValuePair key="memory_primitive" value="1" description="" />
   <keyValuePair key="memory_size" value="3392" description="" />
   <keyValuePair key="message_control" value="0" description="" />
   <keyValuePair key="p_clocking_mode" value="0" description="" />
   <keyValuePair key="p_ecc_mode" value="0" description="" />
   <keyValuePair key="p_memory_optimization" value="1" description="" />
   <keyValuePair key="p_memory_primitive" value="1" description="" />
   <keyValuePair key="p_wakeup_time" value="0" description="" />
   <keyValuePair key="p_write_mode_b" value="1" description="" />
   <keyValuePair key="read_data_width_b" value="106" description="" />
   <keyValuePair key="read_latency_b" value="1" description="" />
   <keyValuePair key="read_reset_value_b" value="0" description="" />
   <keyValuePair key="rst_mode_a" value="SYNC" description="" />
   <keyValuePair key="rst_mode_b" value="SYNC" description="" />
   <keyValuePair key="use_embedded_constraint" value="0" description="" />
   <keyValuePair key="use_mem_init" value="0" description="" />
   <keyValuePair key="wakeup_time" value="0" description="" />
   <keyValuePair key="write_data_width_a" value="106" description="" />
   <keyValuePair key="write_mode_b" value="2" description="" />
   <keyValuePair key="write_mode_b" value="1" description="" />
  </section>
 </section>
 <section name="power_opt_design" level="1" order="4" description="">
  <section name="command_line_options_spo" level="2" order="1" description="">
   <keyValuePair key="-cell_types" value="default::all" description="" />
   <keyValuePair key="-clocks" value="default::[not_specified]" description="" />
   <keyValuePair key="-exclude_cells" value="default::[not_specified]" description="" />
   <keyValuePair key="-include_cells" value="default::[not_specified]" description="" />
  </section>
  <section name="usage" level="2" order="2" description="">
   <keyValuePair key="bram_ports_augmented" value="3" description="" />
   <keyValuePair key="bram_ports_newly_gated" value="0" description="" />
   <keyValuePair key="bram_ports_total" value="20" description="" />
   <keyValuePair key="flow_state" value="default" description="" />
   <keyValuePair key="slice_registers_augmented" value="0" description="" />
   <keyValuePair key="slice_registers_newly_gated" value="0" description="" />
   <keyValuePair key="slice_registers_total" value="9542" description="" />
   <keyValuePair key="srls_augmented" value="0" description="" />
   <keyValuePair key="srls_newly_gated" value="0" description="" />
   <keyValuePair key="srls_total" value="260" description="" />
  </section>
 </section>
 <section name="report_drc" level="1" order="5" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-append" value="default::[not_specified]" description="" />
   <keyValuePair key="-checks" value="default::[not_specified]" description="" />
   <keyValuePair key="-fail_on" value="default::[not_specified]" description="" />
   <keyValuePair key="-force" value="default::[not_specified]" description="" />
   <keyValuePair key="-format" value="default::[not_specified]" description="" />
   <keyValuePair key="-internal" value="default::[not_specified]" description="" />
   <keyValuePair key="-internal_only" value="default::[not_specified]" description="" />
   <keyValuePair key="-messages" value="default::[not_specified]" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_waivers" value="default::[not_specified]" description="" />
   <keyValuePair key="-return_string" value="default::[not_specified]" description="" />
   <keyValuePair key="-ruledecks" value="default::[not_specified]" description="" />
   <keyValuePair key="-upgrade_cw" value="default::[not_specified]" description="" />
   <keyValuePair key="-waived" value="default::[not_specified]" description="" />
  </section>
  <section name="results" level="2" order="2" description="">
   <keyValuePair key="reqp-181" value="1" description="" />
   <keyValuePair key="reqp-1839" value="12" description="" />
  </section>
 </section>
 <section name="report_utilization" level="1" order="6" description="">
  <section name="clocking" level="2" order="1" description="">
   <keyValuePair key="bufgctrl_available" value="32" description="" />
   <keyValuePair key="bufgctrl_fixed" value="0" description="" />
   <keyValuePair key="bufgctrl_used" value="4" description="" />
   <keyValuePair key="bufgctrl_util_percentage" value="12.50" description="" />
   <keyValuePair key="bufhce_available" value="72" description="" />
   <keyValuePair key="bufhce_fixed" value="0" description="" />
   <keyValuePair key="bufhce_used" value="0" description="" />
   <keyValuePair key="bufhce_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufio_available" value="16" description="" />
   <keyValuePair key="bufio_fixed" value="0" description="" />
   <keyValuePair key="bufio_used" value="0" description="" />
   <keyValuePair key="bufio_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufmrce_available" value="8" description="" />
   <keyValuePair key="bufmrce_fixed" value="0" description="" />
   <keyValuePair key="bufmrce_used" value="0" description="" />
   <keyValuePair key="bufmrce_util_percentage" value="0.00" description="" />
   <keyValuePair key="bufr_available" value="16" description="" />
   <keyValuePair key="bufr_fixed" value="0" description="" />
   <keyValuePair key="bufr_used" value="1" description="" />
   <keyValuePair key="bufr_util_percentage" value="6.25" description="" />
   <keyValuePair key="mmcme2_adv_available" value="4" description="" />
   <keyValuePair key="mmcme2_adv_fixed" value="0" description="" />
   <keyValuePair key="mmcme2_adv_used" value="1" description="" />
   <keyValuePair key="mmcme2_adv_util_percentage" value="25.00" description="" />
   <keyValuePair key="plle2_adv_available" value="4" description="" />
   <keyValuePair key="plle2_adv_fixed" value="0" description="" />
   <keyValuePair key="plle2_adv_used" value="1" description="" />
   <keyValuePair key="plle2_adv_util_percentage" value="25.00" description="" />
  </section>
  <section name="dsp" level="2" order="2" description="">
   <keyValuePair key="dsps_available" value="220" description="" />
   <keyValuePair key="dsps_fixed" value="0" description="" />
   <keyValuePair key="dsps_used" value="0" description="" />
   <keyValuePair key="dsps_util_percentage" value="0.00" description="" />
  </section>
  <section name="io_standard" level="2" order="3" description="">
   <keyValuePair key="blvds_25" value="0" description="" />
   <keyValuePair key="diff_hstl_i" value="0" description="" />
   <keyValuePair key="diff_hstl_i_18" value="0" description="" />
   <keyValuePair key="diff_hstl_ii" value="0" description="" />
   <keyValuePair key="diff_hstl_ii_18" value="0" description="" />
   <keyValuePair key="diff_hsul_12" value="0" description="" />
   <keyValuePair key="diff_mobile_ddr" value="0" description="" />
   <keyValuePair key="diff_sstl135" value="0" description="" />
   <keyValuePair key="diff_sstl135_r" value="0" description="" />
   <keyValuePair key="diff_sstl15" value="0" description="" />
   <keyValuePair key="diff_sstl15_r" value="0" description="" />
   <keyValuePair key="diff_sstl18_i" value="0" description="" />
   <keyValuePair key="diff_sstl18_ii" value="0" description="" />
   <keyValuePair key="hstl_i" value="0" description="" />
   <keyValuePair key="hstl_i_18" value="0" description="" />
   <keyValuePair key="hstl_ii" value="0" description="" />
   <keyValuePair key="hstl_ii_18" value="0" description="" />
   <keyValuePair key="hsul_12" value="0" description="" />
   <keyValuePair key="lvcmos12" value="0" description="" />
   <keyValuePair key="lvcmos15" value="0" description="" />
   <keyValuePair key="lvcmos18" value="0" description="" />
   <keyValuePair key="lvcmos25" value="0" description="" />
   <keyValuePair key="lvcmos33" value="1" description="" />
   <keyValuePair key="lvds_25" value="0" description="" />
   <keyValuePair key="lvttl" value="0" description="" />
   <keyValuePair key="mini_lvds_25" value="0" description="" />
   <keyValuePair key="mobile_ddr" value="0" description="" />
   <keyValuePair key="pci33_3" value="0" description="" />
   <keyValuePair key="ppds_25" value="0" description="" />
   <keyValuePair key="rsds_25" value="0" description="" />
   <keyValuePair key="sstl135" value="0" description="" />
   <keyValuePair key="sstl135_r" value="0" description="" />
   <keyValuePair key="sstl15" value="0" description="" />
   <keyValuePair key="sstl15_r" value="0" description="" />
   <keyValuePair key="sstl18_i" value="0" description="" />
   <keyValuePair key="sstl18_ii" value="0" description="" />
   <keyValuePair key="tmds_33" value="0" description="" />
  </section>
  <section name="memory" level="2" order="4" description="">
   <keyValuePair key="block_ram_tile_available" value="140" description="" />
   <keyValuePair key="block_ram_tile_fixed" value="0" description="" />
   <keyValuePair key="block_ram_tile_used" value="10" description="" />
   <keyValuePair key="block_ram_tile_util_percentage" value="7.14" description="" />
   <keyValuePair key="ramb18_available" value="280" description="" />
   <keyValuePair key="ramb18_fixed" value="0" description="" />
   <keyValuePair key="ramb18_used" value="0" description="" />
   <keyValuePair key="ramb18_util_percentage" value="0.00" description="" />
   <keyValuePair key="ramb36_fifo_available" value="140" description="" />
   <keyValuePair key="ramb36_fifo_fixed" value="0" description="" />
   <keyValuePair key="ramb36_fifo_used" value="10" description="" />
   <keyValuePair key="ramb36_fifo_util_percentage" value="7.14" description="" />
   <keyValuePair key="ramb36e1_only_used" value="10" description="" />
  </section>
  <section name="primitives" level="2" order="5" description="">
   <keyValuePair key="bibuf_functional_category" value="IO" description="" />
   <keyValuePair key="bibuf_used" value="130" description="" />
   <keyValuePair key="bufg_functional_category" value="Clock" description="" />
   <keyValuePair key="bufg_used" value="4" description="" />
   <keyValuePair key="bufr_functional_category" value="Clock" description="" />
   <keyValuePair key="bufr_used" value="1" description="" />
   <keyValuePair key="carry4_functional_category" value="CarryLogic" description="" />
   <keyValuePair key="carry4_used" value="254" description="" />
   <keyValuePair key="fdce_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdce_used" value="67" description="" />
   <keyValuePair key="fdpe_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdpe_used" value="9" description="" />
   <keyValuePair key="fdre_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdre_used" value="9080" description="" />
   <keyValuePair key="fdse_functional_category" value="Flop &amp; Latch" description="" />
   <keyValuePair key="fdse_used" value="386" description="" />
   <keyValuePair key="ibuf_functional_category" value="IO" description="" />
   <keyValuePair key="ibuf_used" value="20" description="" />
   <keyValuePair key="lut1_functional_category" value="LUT" description="" />
   <keyValuePair key="lut1_used" value="211" description="" />
   <keyValuePair key="lut2_functional_category" value="LUT" description="" />
   <keyValuePair key="lut2_used" value="736" description="" />
   <keyValuePair key="lut3_functional_category" value="LUT" description="" />
   <keyValuePair key="lut3_used" value="1827" description="" />
   <keyValuePair key="lut4_functional_category" value="LUT" description="" />
   <keyValuePair key="lut4_used" value="908" description="" />
   <keyValuePair key="lut5_functional_category" value="LUT" description="" />
   <keyValuePair key="lut5_used" value="1146" description="" />
   <keyValuePair key="lut6_functional_category" value="LUT" description="" />
   <keyValuePair key="lut6_used" value="1664" description="" />
   <keyValuePair key="mmcme2_adv_functional_category" value="Clock" description="" />
   <keyValuePair key="mmcme2_adv_used" value="1" description="" />
   <keyValuePair key="muxf7_functional_category" value="MuxFx" description="" />
   <keyValuePair key="muxf7_used" value="220" description="" />
   <keyValuePair key="muxf8_functional_category" value="MuxFx" description="" />
   <keyValuePair key="muxf8_used" value="11" description="" />
   <keyValuePair key="obuf_functional_category" value="IO" description="" />
   <keyValuePair key="obuf_used" value="34" description="" />
   <keyValuePair key="obuft_functional_category" value="IO" description="" />
   <keyValuePair key="obuft_used" value="5" description="" />
   <keyValuePair key="oddr_functional_category" value="IO" description="" />
   <keyValuePair key="oddr_used" value="1" description="" />
   <keyValuePair key="plle2_adv_functional_category" value="Clock" description="" />
   <keyValuePair key="plle2_adv_used" value="1" description="" />
   <keyValuePair key="ps7_functional_category" value="Specialized Resource" description="" />
   <keyValuePair key="ps7_used" value="1" description="" />
   <keyValuePair key="ramb36e1_functional_category" value="Block Memory" description="" />
   <keyValuePair key="ramb36e1_used" value="10" description="" />
   <keyValuePair key="ramd32_functional_category" value="Distributed Memory" description="" />
   <keyValuePair key="ramd32_used" value="390" description="" />
   <keyValuePair key="rams32_functional_category" value="Distributed Memory" description="" />
   <keyValuePair key="rams32_used" value="130" description="" />
   <keyValuePair key="srl16e_functional_category" value="Distributed Memory" description="" />
   <keyValuePair key="srl16e_used" value="177" description="" />
   <keyValuePair key="srlc32e_functional_category" value="Distributed Memory" description="" />
   <keyValuePair key="srlc32e_used" value="83" description="" />
  </section>
  <section name="slice_logic" level="2" order="6" description="">
   <keyValuePair key="f7_muxes_available" value="26600" description="" />
   <keyValuePair key="f7_muxes_fixed" value="0" description="" />
   <keyValuePair key="f7_muxes_used" value="220" description="" />
   <keyValuePair key="f7_muxes_util_percentage" value="0.83" description="" />
   <keyValuePair key="f8_muxes_available" value="13300" description="" />
   <keyValuePair key="f8_muxes_fixed" value="0" description="" />
   <keyValuePair key="f8_muxes_used" value="11" description="" />
   <keyValuePair key="f8_muxes_util_percentage" value="0.08" description="" />
   <keyValuePair key="lut_as_distributed_ram_fixed" value="0" description="" />
   <keyValuePair key="lut_as_distributed_ram_fixed" value="0" description="" />
   <keyValuePair key="lut_as_distributed_ram_used" value="260" description="" />
   <keyValuePair key="lut_as_distributed_ram_used" value="260" description="" />
   <keyValuePair key="lut_as_logic_available" value="53200" description="" />
   <keyValuePair key="lut_as_logic_available" value="53200" description="" />
   <keyValuePair key="lut_as_logic_fixed" value="0" description="" />
   <keyValuePair key="lut_as_logic_fixed" value="0" description="" />
   <keyValuePair key="lut_as_logic_used" value="5360" description="" />
   <keyValuePair key="lut_as_logic_used" value="5360" description="" />
   <keyValuePair key="lut_as_logic_util_percentage" value="10.08" description="" />
   <keyValuePair key="lut_as_logic_util_percentage" value="10.08" description="" />
   <keyValuePair key="lut_as_memory_available" value="17400" description="" />
   <keyValuePair key="lut_as_memory_available" value="17400" description="" />
   <keyValuePair key="lut_as_memory_fixed" value="0" description="" />
   <keyValuePair key="lut_as_memory_fixed" value="0" description="" />
   <keyValuePair key="lut_as_memory_used" value="469" description="" />
   <keyValuePair key="lut_as_memory_used" value="469" description="" />
   <keyValuePair key="lut_as_memory_util_percentage" value="2.70" description="" />
   <keyValuePair key="lut_as_memory_util_percentage" value="2.70" description="" />
   <keyValuePair key="lut_as_shift_register_fixed" value="0" description="" />
   <keyValuePair key="lut_as_shift_register_fixed" value="0" description="" />
   <keyValuePair key="lut_as_shift_register_used" value="209" description="" />
   <keyValuePair key="lut_as_shift_register_used" value="209" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_unused_fixed" value="209" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_unused_used" value="4023" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_used_fixed" value="4023" description="" />
   <keyValuePair key="lut_in_front_of_the_register_is_used_used" value="783" description="" />
   <keyValuePair key="register_as_flip_flop_available" value="106400" description="" />
   <keyValuePair key="register_as_flip_flop_fixed" value="0" description="" />
   <keyValuePair key="register_as_flip_flop_used" value="9542" description="" />
   <keyValuePair key="register_as_flip_flop_util_percentage" value="8.97" description="" />
   <keyValuePair key="register_as_latch_available" value="106400" description="" />
   <keyValuePair key="register_as_latch_fixed" value="0" description="" />
   <keyValuePair key="register_as_latch_used" value="0" description="" />
   <keyValuePair key="register_as_latch_util_percentage" value="0.00" description="" />
   <keyValuePair key="register_driven_from_outside_the_slice_fixed" value="783" description="" />
   <keyValuePair key="register_driven_from_outside_the_slice_used" value="4806" description="" />
   <keyValuePair key="register_driven_from_within_the_slice_fixed" value="4806" description="" />
   <keyValuePair key="register_driven_from_within_the_slice_used" value="4736" description="" />
   <keyValuePair key="slice_available" value="13300" description="" />
   <keyValuePair key="slice_fixed" value="0" description="" />
   <keyValuePair key="slice_luts_available" value="53200" description="" />
   <keyValuePair key="slice_luts_fixed" value="0" description="" />
   <keyValuePair key="slice_luts_used" value="5829" description="" />
   <keyValuePair key="slice_luts_util_percentage" value="10.96" description="" />
   <keyValuePair key="slice_registers_available" value="106400" description="" />
   <keyValuePair key="slice_registers_available" value="106400" description="" />
   <keyValuePair key="slice_registers_fixed" value="0" description="" />
   <keyValuePair key="slice_registers_fixed" value="0" description="" />
   <keyValuePair key="slice_registers_used" value="9542" description="" />
   <keyValuePair key="slice_registers_used" value="9542" description="" />
   <keyValuePair key="slice_registers_util_percentage" value="8.97" description="" />
   <keyValuePair key="slice_registers_util_percentage" value="8.97" description="" />
   <keyValuePair key="slice_used" value="2994" description="" />
   <keyValuePair key="slice_util_percentage" value="22.51" description="" />
   <keyValuePair key="slicel_fixed" value="0" description="" />
   <keyValuePair key="slicel_used" value="2069" description="" />
   <keyValuePair key="slicem_fixed" value="0" description="" />
   <keyValuePair key="slicem_used" value="925" description="" />
   <keyValuePair key="unique_control_sets_available" value="13300" description="" />
   <keyValuePair key="unique_control_sets_fixed" value="13300" description="" />
   <keyValuePair key="unique_control_sets_used" value="428" description="" />
   <keyValuePair key="unique_control_sets_util_percentage" value="3.22" description="" />
   <keyValuePair key="using_o5_and_o6_fixed" value="3.22" description="" />
   <keyValuePair key="using_o5_and_o6_used" value="51" description="" />
   <keyValuePair key="using_o5_output_only_fixed" value="51" description="" />
   <keyValuePair key="using_o5_output_only_used" value="8" description="" />
   <keyValuePair key="using_o6_output_only_fixed" value="8" description="" />
   <keyValuePair key="using_o6_output_only_used" value="150" description="" />
  </section>
  <section name="specific_feature" level="2" order="7" description="">
   <keyValuePair key="bscane2_available" value="4" description="" />
   <keyValuePair key="bscane2_fixed" value="0" description="" />
   <keyValuePair key="bscane2_used" value="0" description="" />
   <keyValuePair key="bscane2_util_percentage" value="0.00" description="" />
   <keyValuePair key="capturee2_available" value="1" description="" />
   <keyValuePair key="capturee2_fixed" value="0" description="" />
   <keyValuePair key="capturee2_used" value="0" description="" />
   <keyValuePair key="capturee2_util_percentage" value="0.00" description="" />
   <keyValuePair key="dna_port_available" value="1" description="" />
   <keyValuePair key="dna_port_fixed" value="0" description="" />
   <keyValuePair key="dna_port_used" value="0" description="" />
   <keyValuePair key="dna_port_util_percentage" value="0.00" description="" />
   <keyValuePair key="efuse_usr_available" value="1" description="" />
   <keyValuePair key="efuse_usr_fixed" value="0" description="" />
   <keyValuePair key="efuse_usr_used" value="0" description="" />
   <keyValuePair key="efuse_usr_util_percentage" value="0.00" description="" />
   <keyValuePair key="frame_ecce2_available" value="1" description="" />
   <keyValuePair key="frame_ecce2_fixed" value="0" description="" />
   <keyValuePair key="frame_ecce2_used" value="0" description="" />
   <keyValuePair key="frame_ecce2_util_percentage" value="0.00" description="" />
   <keyValuePair key="icape2_available" value="2" description="" />
   <keyValuePair key="icape2_fixed" value="0" description="" />
   <keyValuePair key="icape2_used" value="0" description="" />
   <keyValuePair key="icape2_util_percentage" value="0.00" description="" />
   <keyValuePair key="startupe2_available" value="1" description="" />
   <keyValuePair key="startupe2_fixed" value="0" description="" />
   <keyValuePair key="startupe2_used" value="0" description="" />
   <keyValuePair key="startupe2_util_percentage" value="0.00" description="" />
   <keyValuePair key="xadc_available" value="1" description="" />
   <keyValuePair key="xadc_fixed" value="0" description="" />
   <keyValuePair key="xadc_used" value="0" description="" />
   <keyValuePair key="xadc_util_percentage" value="0.00" description="" />
  </section>
 </section>
 <section name="synthesis" level="1" order="7" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-assert" value="default::[not_specified]" description="" />
   <keyValuePair key="-bufg" value="default::12" description="" />
   <keyValuePair key="-cascade_dsp" value="default::auto" description="" />
   <keyValuePair key="-constrset" value="default::[not_specified]" description="" />
   <keyValuePair key="-control_set_opt_threshold" value="default::auto" description="" />
   <keyValuePair key="-directive" value="default::default" description="" />
   <keyValuePair key="-fanout_limit" value="default::10000" description="" />
   <keyValuePair key="-flatten_hierarchy" value="default::rebuilt" description="" />
   <keyValuePair key="-fsm_extraction" value="default::auto" description="" />
   <keyValuePair key="-gated_clock_conversion" value="default::off" description="" />
   <keyValuePair key="-generic" value="default::[not_specified]" description="" />
   <keyValuePair key="-include_dirs" value="default::[not_specified]" description="" />
   <keyValuePair key="-keep_equivalent_registers" value="default::[not_specified]" description="" />
   <keyValuePair key="-max_bram" value="default::-1" description="" />
   <keyValuePair key="-max_bram_cascade_height" value="default::-1" description="" />
   <keyValuePair key="-max_dsp" value="default::-1" description="" />
   <keyValuePair key="-max_uram" value="default::-1" description="" />
   <keyValuePair key="-max_uram_cascade_height" value="default::-1" description="" />
   <keyValuePair key="-mode" value="default::default" description="" />
   <keyValuePair key="-name" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_lc" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_srlextract" value="default::[not_specified]" description="" />
   <keyValuePair key="-no_timing_driven" value="default::[not_specified]" description="" />
   <keyValuePair key="-part" value="xc7z020clg400-2" description="" />
   <keyValuePair key="-resource_sharing" value="default::auto" description="" />
   <keyValuePair key="-retiming" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl_skip_constraints" value="default::[not_specified]" description="" />
   <keyValuePair key="-rtl_skip_ip" value="default::[not_specified]" description="" />
   <keyValuePair key="-seu_protect" value="default::none" description="" />
   <keyValuePair key="-sfcu" value="default::[not_specified]" description="" />
   <keyValuePair key="-shreg_min_size" value="default::3" description="" />
   <keyValuePair key="-top" value="UGT" description="" />
   <keyValuePair key="-verilog_define" value="default::[not_specified]" description="" />
  </section>
  <section name="usage" level="2" order="2" description="">
   <keyValuePair key="elapsed" value="00:05:07s" description="" />
   <keyValuePair key="hls_ip" value="0" description="" />
   <keyValuePair key="memory_gain" value="1589.668MB" description="" />
   <keyValuePair key="memory_peak" value="1972.723MB" description="" />
  </section>
 </section>
 <section name="unisim_transformation" level="1" order="8" description="">
  <section name="post_unisim_transformation" level="2" order="1" description="">
   <keyValuePair key="bibuf" value="130" description="" />
   <keyValuePair key="bufg" value="5" description="" />
   <keyValuePair key="bufr" value="1" description="" />
   <keyValuePair key="carry4" value="298" description="" />
   <keyValuePair key="fdce" value="67" description="" />
   <keyValuePair key="fdpe" value="9" description="" />
   <keyValuePair key="fdre" value="11213" description="" />
   <keyValuePair key="fdse" value="433" description="" />
   <keyValuePair key="gnd" value="300" description="" />
   <keyValuePair key="ibuf" value="20" description="" />
   <keyValuePair key="lut1" value="512" description="" />
   <keyValuePair key="lut2" value="782" description="" />
   <keyValuePair key="lut3" value="2529" description="" />
   <keyValuePair key="lut4" value="1092" description="" />
   <keyValuePair key="lut5" value="1188" description="" />
   <keyValuePair key="lut6" value="2020" description="" />
   <keyValuePair key="mmcme2_adv" value="1" description="" />
   <keyValuePair key="muxf7" value="236" description="" />
   <keyValuePair key="muxf8" value="11" description="" />
   <keyValuePair key="obuf" value="34" description="" />
   <keyValuePair key="obuft" value="5" description="" />
   <keyValuePair key="oddr" value="1" description="" />
   <keyValuePair key="plle2_adv" value="1" description="" />
   <keyValuePair key="ps7" value="1" description="" />
   <keyValuePair key="ramb36e1" value="10" description="" />
   <keyValuePair key="ramd32" value="804" description="" />
   <keyValuePair key="rams32" value="268" description="" />
   <keyValuePair key="srl16e" value="241" description="" />
   <keyValuePair key="srlc32e" value="117" description="" />
   <keyValuePair key="vcc" value="292" description="" />
  </section>
  <section name="pre_unisim_transformation" level="2" order="2" description="">
   <keyValuePair key="bibuf" value="130" description="" />
   <keyValuePair key="bufg" value="5" description="" />
   <keyValuePair key="bufr" value="1" description="" />
   <keyValuePair key="carry4" value="298" description="" />
   <keyValuePair key="fdce" value="67" description="" />
   <keyValuePair key="fdpe" value="9" description="" />
   <keyValuePair key="fdre" value="11213" description="" />
   <keyValuePair key="fdse" value="433" description="" />
   <keyValuePair key="gnd" value="300" description="" />
   <keyValuePair key="ibuf" value="15" description="" />
   <keyValuePair key="iobuf" value="5" description="" />
   <keyValuePair key="lut1" value="512" description="" />
   <keyValuePair key="lut2" value="782" description="" />
   <keyValuePair key="lut3" value="2529" description="" />
   <keyValuePair key="lut4" value="1092" description="" />
   <keyValuePair key="lut5" value="1188" description="" />
   <keyValuePair key="lut6" value="2020" description="" />
   <keyValuePair key="mmcme2_adv" value="1" description="" />
   <keyValuePair key="muxf7" value="236" description="" />
   <keyValuePair key="muxf8" value="11" description="" />
   <keyValuePair key="obuf" value="34" description="" />
   <keyValuePair key="oddr" value="1" description="" />
   <keyValuePair key="plle2_adv" value="1" description="" />
   <keyValuePair key="ps7" value="1" description="" />
   <keyValuePair key="ram32m" value="134" description="" />
   <keyValuePair key="ramb36e1" value="10" description="" />
   <keyValuePair key="srl16e" value="241" description="" />
   <keyValuePair key="srlc32e" value="117" description="" />
   <keyValuePair key="vcc" value="292" description="" />
  </section>
 </section>
 <section name="vivado_usage" level="1" order="9" description="">
  <section name="gui_handlers" level="2" order="1" description="">
   <keyValuePair key="abstractcombinedpanel_remove_selected_elements" value="2" description="" />
   <keyValuePair key="abstractfileview_reload" value="11" description="" />
   <keyValuePair key="abstractsearchablepanel_show_search" value="2" description="" />
   <keyValuePair key="addilaprobespopup_ok" value="3" description="" />
   <keyValuePair key="addrepositoryinfodialog_ok" value="3" description="" />
   <keyValuePair key="addrepositoryinfodialog_repository_tree" value="2" description="" />
   <keyValuePair key="addresstreetablepanel_address_tree_table" value="286" description="" />
   <keyValuePair key="addsrcwizard_specify_hdl_netlist_block_design" value="1" description="" />
   <keyValuePair key="addsrcwizard_specify_or_create_constraint_files" value="2" description="" />
   <keyValuePair key="applyrsbmultiautomationdialog_checkbox_tree" value="25" description="" />
   <keyValuePair key="basedialog_cancel" value="262" description="" />
   <keyValuePair key="basedialog_close" value="5" description="" />
   <keyValuePair key="basedialog_ok" value="613" description="" />
   <keyValuePair key="basedialog_yes" value="49" description="" />
   <keyValuePair key="basedialogutils_open_in_new_tab" value="1" description="" />
   <keyValuePair key="basedialogutils_open_in_specified_layout" value="2" description="" />
   <keyValuePair key="basereporttab_rerun" value="12" description="" />
   <keyValuePair key="checktimingresulttreetablepanel_check_timing_result_tree_table" value="10" description="" />
   <keyValuePair key="checktimingsectionpanel_check_timing_selection_table" value="2" description="" />
   <keyValuePair key="clkconfigmainpanel_tabbed_pane" value="2" description="" />
   <keyValuePair key="clkconfigtreetablepanel_clk_config_tree_table" value="59" description="" />
   <keyValuePair key="clocknetworksreportview_clock_network_tree" value="20" description="" />
   <keyValuePair key="clocksummarytreetablepanel_clock_summary_tree_table" value="28" description="" />
   <keyValuePair key="closeplanner_cancel" value="1" description="" />
   <keyValuePair key="closeplanner_yes" value="3" description="" />
   <keyValuePair key="cmdmsgdialog_copy_message" value="7" description="" />
   <keyValuePair key="cmdmsgdialog_messages" value="112" description="" />
   <keyValuePair key="cmdmsgdialog_ok" value="141" description="" />
   <keyValuePair key="cmdmsgdialog_open_messages_view" value="7" description="" />
   <keyValuePair key="confirmsavetexteditsdialog_no" value="2" description="" />
   <keyValuePair key="constraintschooserpanel_add_existing_or_create_new_constraints" value="3" description="" />
   <keyValuePair key="constraintschooserpanel_add_files_below_to_this_constraint_set" value="1" description="" />
   <keyValuePair key="constraintschooserpanel_create_file" value="4" description="" />
   <keyValuePair key="constraintschooserpanel_file_table" value="18" description="" />
   <keyValuePair key="coreandinterfacesbasetreetablepanel_refresh_all_repositories" value="2" description="" />
   <keyValuePair key="coreandinterfacesbasetreetablepanel_refresh_repository" value="1" description="" />
   <keyValuePair key="coretreetablepanel_add_ip_to_repository" value="1" description="" />
   <keyValuePair key="coretreetablepanel_core_tree_table" value="521" description="" />
   <keyValuePair key="coretreetablepanel_delete_ip" value="1" description="" />
   <keyValuePair key="createconstraintsfilepanel_file_name" value="5" description="" />
   <keyValuePair key="createnewdiagramdialog_design_name" value="5" description="" />
   <keyValuePair key="creatersbinterfacedialog_table" value="8" description="" />
   <keyValuePair key="creatersbportdialog_create_vector" value="7" description="" />
   <keyValuePair key="creatersbportdialog_direction" value="15" description="" />
   <keyValuePair key="creatersbportdialog_frequency" value="1" description="" />
   <keyValuePair key="creatersbportdialog_from" value="7" description="" />
   <keyValuePair key="creatersbportdialog_port_name" value="19" description="" />
   <keyValuePair key="creatersbportdialog_type" value="18" description="" />
   <keyValuePair key="customizecoredialog_documentation" value="1" description="" />
   <keyValuePair key="customizecoredialog_ip_location" value="2" description="" />
   <keyValuePair key="ddrconfigtreetablepanel_ddr_config_tree_table" value="15" description="" />
   <keyValuePair key="debugview_debug_cores_tree_table" value="12" description="" />
   <keyValuePair key="debugwizard_advanced_trigger" value="4" description="" />
   <keyValuePair key="debugwizard_capture_control" value="5" description="" />
   <keyValuePair key="debugwizard_chipscope_tree_table" value="7" description="" />
   <keyValuePair key="debugwizard_find_nets_to_add" value="3" description="" />
   <keyValuePair key="debugwizard_netlist_view" value="1" description="" />
   <keyValuePair key="debugwizard_sample_of_data_depth" value="2" description="" />
   <keyValuePair key="designtimingsumsectionpanel_worst_hold_slack" value="1" description="" />
   <keyValuePair key="designtimingsumsectionpanel_worst_negative_slack" value="3" description="" />
   <keyValuePair key="editcreateclocktablepanel_edit_create_clock_table" value="23" description="" />
   <keyValuePair key="editcreategeneratedclocktablepanel_edit_create_generated_clock_table" value="1" description="" />
   <keyValuePair key="editiodelaytablepanel_edit_io_delay_table" value="16" description="" />
   <keyValuePair key="existingconstraintslistpanel_copy_text_from_selected_constraints" value="1" description="" />
   <keyValuePair key="expreporttreepanel_exp_report_tree_table" value="19" description="" />
   <keyValuePair key="expruntreepanel_exp_run_tree_table" value="44" description="" />
   <keyValuePair key="filesetpanel_file_set_panel_tree" value="798" description="" />
   <keyValuePair key="flownavigatortreepanel_flow_navigator_tree" value="365" description="" />
   <keyValuePair key="flownavigatortreepanel_open" value="2" description="" />
   <keyValuePair key="fpgachooser_category" value="2" description="" />
   <keyValuePair key="fpgachooser_family" value="2" description="" />
   <keyValuePair key="fpgachooser_fpga_table" value="7" description="" />
   <keyValuePair key="fpgachooser_package" value="1" description="" />
   <keyValuePair key="fpgachooser_speed" value="1" description="" />
   <keyValuePair key="fromtargetspecifierpanel_specify_start_points" value="3" description="" />
   <keyValuePair key="gensettingtreetablepanel_gen_setting_tree_table" value="81" description="" />
   <keyValuePair key="getobjectsdialog_find" value="12" description="" />
   <keyValuePair key="getobjectspanel_append" value="4" description="" />
   <keyValuePair key="getobjectspanel_set" value="2" description="" />
   <keyValuePair key="gettingstartedview_create_new_project" value="2" description="" />
   <keyValuePair key="gettingstartedview_open_project" value="3" description="" />
   <keyValuePair key="gictreetablepanel_gic_tree_table" value="19" description="" />
   <keyValuePair key="graphicalview_zoom_fit" value="4" description="" />
   <keyValuePair key="graphicalview_zoom_out" value="4" description="" />
   <keyValuePair key="hacgccheckbox_value_of_specified_parameter" value="1" description="" />
   <keyValuePair key="hacgccombobox_value_of_specified_parameter" value="16" description="" />
   <keyValuePair key="hacgccombobox_value_of_specified_parameter_manual" value="11" description="" />
   <keyValuePair key="hacgcipsymbol_show_disabled_ports" value="11" description="" />
   <keyValuePair key="hacgctabbedpane_tabbed_pane" value="1" description="" />
   <keyValuePair key="hardwaredashboardview_show_dashboard_options" value="1" description="" />
   <keyValuePair key="hardwareilawaveformview_run_trigger_for_this_ila_core" value="21" description="" />
   <keyValuePair key="hardwareilawaveformview_run_trigger_immediate_for_this_ila_core" value="9" description="" />
   <keyValuePair key="hardwareilawaveformview_stop_trigger_for_this_ila_core" value="5" description="" />
   <keyValuePair key="hardwareilawaveformview_toggle_auto_re_trigger_mode" value="12" description="" />
   <keyValuePair key="hardwaretreepanel_hardware_tree_table" value="51" description="" />
   <keyValuePair key="hardwareview_expand_next_level" value="1" description="" />
   <keyValuePair key="hcodeeditor_blank_operations" value="1" description="" />
   <keyValuePair key="hcodeeditor_search_text_combo_box" value="14" description="" />
   <keyValuePair key="hduallist_find_results" value="21" description="" />
   <keyValuePair key="hduallist_move_selected_items_to_right" value="6" description="" />
   <keyValuePair key="hfolderchooserhelpers_up_one_level" value="4" description="" />
   <keyValuePair key="hinputhandler_toggle_block_comments" value="1" description="" />
   <keyValuePair key="hpopuptitle_close" value="8" description="" />
   <keyValuePair key="htable_set_eliding_for_table_cells" value="9" description="" />
   <keyValuePair key="ictelementsummarysectionpanel_hold" value="1" description="" />
   <keyValuePair key="ilaprobetablepanel_add_probe" value="4" description="" />
   <keyValuePair key="ilaprobetablepanel_add_probes" value="1" description="" />
   <keyValuePair key="ilaprobetablepanel_set_trigger_condition_to_global" value="1" description="" />
   <keyValuePair key="instancemenu_floorplanning" value="2" description="" />
   <keyValuePair key="intraclockssectionpanel_intra_clocks_section_table" value="40" description="" />
   <keyValuePair key="iodelaycreationpanel_delay_value" value="5" description="" />
   <keyValuePair key="iodelaycreationpanel_delay_value_specifies" value="5" description="" />
   <keyValuePair key="iodelaycreationpanel_delay_value_specifies_rise_fall_delay" value="2" description="" />
   <keyValuePair key="iodelaycreationpanel_specify_clock_pin_or_port" value="3" description="" />
   <keyValuePair key="iodelaycreationpanel_specify_list_of_ports" value="3" description="" />
   <keyValuePair key="ipicomponentname_component_name" value="3" description="" />
   <keyValuePair key="ipstatussectionpanel_upgrade_selected" value="13" description="" />
   <keyValuePair key="ipstatustablepanel_ip_status_table" value="31" description="" />
   <keyValuePair key="ipstatustablepanel_more_info" value="11" description="" />
   <keyValuePair key="labtoolsmenu_name" value="9" description="" />
   <keyValuePair key="languagetemplatesdialog_templates_tree" value="157" description="" />
   <keyValuePair key="logmonitor_monitor" value="5" description="" />
   <keyValuePair key="logpanel_log_navigator" value="4" description="" />
   <keyValuePair key="mainmenumgr_checkpoint" value="190" description="" />
   <keyValuePair key="mainmenumgr_constraints" value="15" description="" />
   <keyValuePair key="mainmenumgr_edit" value="26" description="" />
   <keyValuePair key="mainmenumgr_export" value="263" description="" />
   <keyValuePair key="mainmenumgr_file" value="374" description="" />
   <keyValuePair key="mainmenumgr_floorplanning" value="2" description="" />
   <keyValuePair key="mainmenumgr_flow" value="36" description="" />
   <keyValuePair key="mainmenumgr_help" value="2" description="" />
   <keyValuePair key="mainmenumgr_import" value="46" description="" />
   <keyValuePair key="mainmenumgr_io" value="4" description="" />
   <keyValuePair key="mainmenumgr_io_planning" value="2" description="" />
   <keyValuePair key="mainmenumgr_ip" value="193" description="" />
   <keyValuePair key="mainmenumgr_open_block_design" value="3" description="" />
   <keyValuePair key="mainmenumgr_project" value="191" description="" />
   <keyValuePair key="mainmenumgr_reports" value="42" description="" />
   <keyValuePair key="mainmenumgr_settings" value="3" description="" />
   <keyValuePair key="mainmenumgr_text_editor" value="195" description="" />
   <keyValuePair key="mainmenumgr_timing" value="5" description="" />
   <keyValuePair key="mainmenumgr_tools" value="49" description="" />
   <keyValuePair key="mainmenumgr_unselect_type" value="1" description="" />
   <keyValuePair key="mainmenumgr_view" value="32" description="" />
   <keyValuePair key="mainmenumgr_window" value="60" description="" />
   <keyValuePair key="maintoolbarmgr_run" value="6" description="" />
   <keyValuePair key="mainwinmenumgr_layout" value="48" description="" />
   <keyValuePair key="mainwinmenumgr_load" value="2" description="" />
   <keyValuePair key="messagewithoptiondialog_dont_show_this_dialog_again" value="1" description="" />
   <keyValuePair key="mioconfigtreetablepanel_mio_config_tree_table" value="89" description="" />
   <keyValuePair key="miotablepagepanel_mio_table" value="21" description="" />
   <keyValuePair key="msgtreepanel_discard_user_created_messages" value="10" description="" />
   <keyValuePair key="msgtreepanel_message_severity" value="24" description="" />
   <keyValuePair key="msgtreepanel_message_view_tree" value="477" description="" />
   <keyValuePair key="msgtreepanel_suppress_messages_with_this_id" value="1" description="" />
   <keyValuePair key="msgview_critical_warnings" value="1" description="" />
   <keyValuePair key="navigabletimingreporttab_timing_report_navigation_tree" value="284" description="" />
   <keyValuePair key="netlisttreeview_netlist_tree" value="73" description="" />
   <keyValuePair key="newexporthardwaredialog_export_to" value="4" description="" />
   <keyValuePair key="newexporthardwaredialog_include_bitstream" value="34" description="" />
   <keyValuePair key="packagetreepanel_package_tree_panel" value="3" description="" />
   <keyValuePair key="pacommandnames_add_probes_to_waveform" value="2" description="" />
   <keyValuePair key="pacommandnames_add_sources" value="3" description="" />
   <keyValuePair key="pacommandnames_addresseditor_window" value="5" description="" />
   <keyValuePair key="pacommandnames_auto_assign_address" value="18" description="" />
   <keyValuePair key="pacommandnames_auto_connect_ports" value="18" description="" />
   <keyValuePair key="pacommandnames_auto_connect_target" value="64" description="" />
   <keyValuePair key="pacommandnames_auto_fit_selection" value="4" description="" />
   <keyValuePair key="pacommandnames_auto_update_hier" value="10" description="" />
   <keyValuePair key="pacommandnames_close_server" value="11" description="" />
   <keyValuePair key="pacommandnames_close_target" value="4" description="" />
   <keyValuePair key="pacommandnames_create_hardware_dashboards" value="1" description="" />
   <keyValuePair key="pacommandnames_create_top_hdl" value="7" description="" />
   <keyValuePair key="pacommandnames_disable_auto_retrigger" value="1" description="" />
   <keyValuePair key="pacommandnames_edit_constraint_sets" value="1" description="" />
   <keyValuePair key="pacommandnames_enable_auto_retrigger" value="1" description="" />
   <keyValuePair key="pacommandnames_exit" value="4" description="" />
   <keyValuePair key="pacommandnames_export_bd_tcl" value="1" description="" />
   <keyValuePair key="pacommandnames_export_hardware" value="57" description="" />
   <keyValuePair key="pacommandnames_export_ila_data" value="1" description="" />
   <keyValuePair key="pacommandnames_generate_composite_file" value="30" description="" />
   <keyValuePair key="pacommandnames_goto_implemented_design" value="2" description="" />
   <keyValuePair key="pacommandnames_ip_settings" value="2" description="" />
   <keyValuePair key="pacommandnames_language_templates" value="2" description="" />
   <keyValuePair key="pacommandnames_launch_hardware" value="101" description="" />
   <keyValuePair key="pacommandnames_log_window" value="1" description="" />
   <keyValuePair key="pacommandnames_make_connection" value="1" description="" />
   <keyValuePair key="pacommandnames_message_window" value="1" description="" />
   <keyValuePair key="pacommandnames_open_hardware_manager" value="2" description="" />
   <keyValuePair key="pacommandnames_open_target" value="3" description="" />
   <keyValuePair key="pacommandnames_open_target_wizard" value="2" description="" />
   <keyValuePair key="pacommandnames_package_pins_window" value="1" description="" />
   <keyValuePair key="pacommandnames_ports_window" value="2" description="" />
   <keyValuePair key="pacommandnames_program_fpga" value="2" description="" />
   <keyValuePair key="pacommandnames_refresh_server" value="1" description="" />
   <keyValuePair key="pacommandnames_regenerate_layout" value="11" description="" />
   <keyValuePair key="pacommandnames_reload_rtl_design" value="1" description="" />
   <keyValuePair key="pacommandnames_report_clock_networks" value="2" description="" />
   <keyValuePair key="pacommandnames_report_ip_status" value="1" description="" />
   <keyValuePair key="pacommandnames_reports_window" value="5" description="" />
   <keyValuePair key="pacommandnames_reset_composite_file" value="7" description="" />
   <keyValuePair key="pacommandnames_run_bitgen" value="27" description="" />
   <keyValuePair key="pacommandnames_run_implementation" value="3" description="" />
   <keyValuePair key="pacommandnames_run_synthesis" value="3" description="" />
   <keyValuePair key="pacommandnames_run_trigger" value="5" description="" />
   <keyValuePair key="pacommandnames_save_design" value="10" description="" />
   <keyValuePair key="pacommandnames_save_rsb_design" value="57" description="" />
   <keyValuePair key="pacommandnames_schematic" value="8" description="" />
   <keyValuePair key="pacommandnames_select_area" value="7" description="" />
   <keyValuePair key="pacommandnames_simulation_run" value="3" description="" />
   <keyValuePair key="pacommandnames_simulation_run_behavioral" value="1" description="" />
   <keyValuePair key="pacommandnames_stop_trigger" value="2" description="" />
   <keyValuePair key="pacommandnames_tcl_console_window" value="1" description="" />
   <keyValuePair key="pacommandnames_timing_results_window" value="2" description="" />
   <keyValuePair key="pacommandnames_trigger_immediate" value="4" description="" />
   <keyValuePair key="pacommandnames_unmap_segment" value="4" description="" />
   <keyValuePair key="pacommandnames_validate_rsb_design" value="88" description="" />
   <keyValuePair key="pacommandnames_zoom_fit" value="1" description="" />
   <keyValuePair key="pacommandnames_zoom_in" value="1" description="" />
   <keyValuePair key="pacommandnames_zoom_out" value="1" description="" />
   <keyValuePair key="pathmenu_set_false_path" value="3" description="" />
   <keyValuePair key="pathmenu_set_maximum_delay" value="2" description="" />
   <keyValuePair key="pathmenu_set_multicycle_path" value="2" description="" />
   <keyValuePair key="pathreporttableview_description" value="48" description="" />
   <keyValuePair key="pathreporttableview_floorplanning" value="5" description="" />
   <keyValuePair key="pathreporttableview_select" value="7" description="" />
   <keyValuePair key="paviews_address_editor" value="22" description="" />
   <keyValuePair key="paviews_code" value="61" description="" />
   <keyValuePair key="paviews_dashboard" value="4" description="" />
   <keyValuePair key="paviews_device" value="13" description="" />
   <keyValuePair key="paviews_ip_catalog" value="4" description="" />
   <keyValuePair key="paviews_path_table" value="4" description="" />
   <keyValuePair key="paviews_project_summary" value="94" description="" />
   <keyValuePair key="paviews_schematic" value="8" description="" />
   <keyValuePair key="paviews_system" value="8" description="" />
   <keyValuePair key="paviews_timing_constraints" value="6" description="" />
   <keyValuePair key="planaheadtab_refresh_ip_catalog" value="15" description="" />
   <keyValuePair key="primaryclockspanel_recommended_constraints_table" value="9" description="" />
   <keyValuePair key="primitivesmenu_highlight_leaf_cells" value="2" description="" />
   <keyValuePair key="probesview_probes_tree" value="13" description="" />
   <keyValuePair key="programdebugtab_available_targets_on_server" value="1" description="" />
   <keyValuePair key="programdebugtab_open_recently_opened_target" value="1" description="" />
   <keyValuePair key="programdebugtab_open_target" value="30" description="" />
   <keyValuePair key="programdebugtab_program_device" value="1" description="" />
   <keyValuePair key="programdebugtab_refresh_device" value="17" description="" />
   <keyValuePair key="programfpgadialog_program" value="2" description="" />
   <keyValuePair key="programfpgadialog_specify_bitstream_file" value="2" description="" />
   <keyValuePair key="progressdialog_cancel" value="2" description="" />
   <keyValuePair key="projectnamechooser_choose_project_location" value="1" description="" />
   <keyValuePair key="projectnamechooser_project_name" value="1" description="" />
   <keyValuePair key="projectsummarypowerpanel_tabbed_pane" value="3" description="" />
   <keyValuePair key="projectsummarytimingpanel_open_timing_summary_report" value="1" description="" />
   <keyValuePair key="projecttab_close_design" value="16" description="" />
   <keyValuePair key="projecttab_reload" value="1" description="" />
   <keyValuePair key="rdicommands_copy" value="1" description="" />
   <keyValuePair key="rdicommands_custom_commands" value="6" description="" />
   <keyValuePair key="rdicommands_delete" value="15" description="" />
   <keyValuePair key="rdicommands_properties" value="12" description="" />
   <keyValuePair key="rdicommands_redo" value="15" description="" />
   <keyValuePair key="rdicommands_save_file" value="1" description="" />
   <keyValuePair key="rdicommands_settings" value="5" description="" />
   <keyValuePair key="rdiviews_waveform_viewer" value="202" description="" />
   <keyValuePair key="removesourcesdialog_also_delete" value="1" description="" />
   <keyValuePair key="reportclocknetworksdialog_result_name" value="1" description="" />
   <keyValuePair key="reporttimingdialog_tabbed_pane" value="7" description="" />
   <keyValuePair key="reporttimingsummarydialog_report_timing_summary_dialog_tabbed" value="13" description="" />
   <keyValuePair key="rsbapplyautomationbar_run_block_automation" value="6" description="" />
   <keyValuePair key="rsbapplyautomationbar_run_connection_automation" value="31" description="" />
   <keyValuePair key="rsbexternalinterfaceproppanels_name" value="5" description="" />
   <keyValuePair key="rsbexternalportproppanels_name" value="12" description="" />
   <keyValuePair key="rungadget_show_error" value="1" description="" />
   <keyValuePair key="rungadget_show_error_and_critical_warning_messages" value="3" description="" />
   <keyValuePair key="rungadget_show_warning_and_error_messages_in_messages" value="2" description="" />
   <keyValuePair key="saveprojectutils_cancel" value="2" description="" />
   <keyValuePair key="saveprojectutils_dont_save" value="1" description="" />
   <keyValuePair key="saveprojectutils_reload" value="1" description="" />
   <keyValuePair key="saveprojectutils_save" value="9" description="" />
   <keyValuePair key="selectablelistpanel_selectable_list" value="22" description="" />
   <keyValuePair key="selectareadialog_select_all" value="2" description="" />
   <keyValuePair key="selectmenu_highlight" value="32" description="" />
   <keyValuePair key="selectmenu_mark" value="11" description="" />
   <keyValuePair key="settingsdialog_options_tree" value="1" description="" />
   <keyValuePair key="settingsdialog_project_tree" value="15" description="" />
   <keyValuePair key="settingsprojectgeneralpage_choose_device_for_your_project" value="1" description="" />
   <keyValuePair key="settingsprojectiprepositorypage_add_repository" value="2" description="" />
   <keyValuePair key="settingsprojectiprepositorypage_refresh_all" value="2" description="" />
   <keyValuePair key="settingsprojectiprepositorypage_repository_chooser" value="5" description="" />
   <keyValuePair key="signaltreepanel_signal_tree_table" value="40" description="" />
   <keyValuePair key="simpleoutputproductdialog_close_dialog_unsaved_changes_will" value="2" description="" />
   <keyValuePair key="simpleoutputproductdialog_generate_output_products_immediately" value="58" description="" />
   <keyValuePair key="simpleoutputproductdialog_output_product_tree" value="2" description="" />
   <keyValuePair key="simpleoutputproductdialog_reset_output_products" value="7" description="" />
   <keyValuePair key="simpleoutputproductdialog_synthesize_design_globally" value="2" description="" />
   <keyValuePair key="smartconnect_show_advanced_properties" value="1" description="" />
   <keyValuePair key="srcchooserpanel_add_hdl_and_netlist_files_to_your_project" value="1" description="" />
   <keyValuePair key="srcchooserpanel_create_file" value="1" description="" />
   <keyValuePair key="srcchooserpanel_make_local_copy_of_these_files_into" value="1" description="" />
   <keyValuePair key="srcmenu_ip_hierarchy" value="10" description="" />
   <keyValuePair key="stalerundialog_no" value="2" description="" />
   <keyValuePair key="stalerundialog_run_synthesis" value="3" description="" />
   <keyValuePair key="statemonitor_reset_run" value="1" description="" />
   <keyValuePair key="syntheticagettingstartedview_recent_projects" value="60" description="" />
   <keyValuePair key="syntheticastatemonitor_cancel" value="5" description="" />
   <keyValuePair key="systembuildermenu_assign_address" value="3" description="" />
   <keyValuePair key="systembuildermenu_create_comment" value="1" description="" />
   <keyValuePair key="systembuildermenu_create_interface_port" value="2" description="" />
   <keyValuePair key="systembuildermenu_create_port" value="19" description="" />
   <keyValuePair key="systembuildermenu_end_connection_mode" value="1" description="" />
   <keyValuePair key="systembuildermenu_ip_documentation" value="18" description="" />
   <keyValuePair key="systembuilderview_add_ip" value="7" description="" />
   <keyValuePair key="systembuilderview_expand_collapse" value="86" description="" />
   <keyValuePair key="systembuilderview_orientation" value="25" description="" />
   <keyValuePair key="systembuilderview_pin_blocks_and_ports_to_location" value="1" description="" />
   <keyValuePair key="systembuilderview_pinning" value="118" description="" />
   <keyValuePair key="systembuilderview_search" value="3" description="" />
   <keyValuePair key="systemtab_blocks" value="1" description="" />
   <keyValuePair key="systemtab_report_ip_status" value="5" description="" />
   <keyValuePair key="systemtab_show_ip_status" value="3" description="" />
   <keyValuePair key="systemtab_upgrade_later" value="2" description="" />
   <keyValuePair key="systemtreeview_system_tree" value="38" description="" />
   <keyValuePair key="targetchooserpanel_add_xilinx_virtual_cable_as_hardware" value="1" description="" />
   <keyValuePair key="taskbanner_close" value="32" description="" />
   <keyValuePair key="tclconsoleview_clear_all_output" value="1" description="" />
   <keyValuePair key="tclconsoleview_copy" value="1" description="" />
   <keyValuePair key="tclconsoleview_tcl_console_code_editor" value="95" description="" />
   <keyValuePair key="tclfinddialog_specify_of_objects_option" value="1" description="" />
   <keyValuePair key="tclobjecttreetable_treetable" value="18" description="" />
   <keyValuePair key="timinggettingstartedpanel_report_timing" value="3" description="" />
   <keyValuePair key="timinggettingstartedpanel_report_timing_summary" value="2" description="" />
   <keyValuePair key="timingitemflattablepanel_floorplanning" value="1" description="" />
   <keyValuePair key="timingitemflattablepanel_table" value="59" description="" />
   <keyValuePair key="timingitemflattablepanel_view_path_report" value="2" description="" />
   <keyValuePair key="timingitemtreetablepanel_timing_item_tree_table" value="18" description="" />
   <keyValuePair key="touchpointsurveydialog_no" value="2" description="" />
   <keyValuePair key="triggersetuppanel_table" value="73" description="" />
   <keyValuePair key="triggerstatuspanel_run_trigger_for_this_ila_core" value="1" description="" />
   <keyValuePair key="viotreetablepanel_vio_tree_table" value="3" description="" />
   <keyValuePair key="waveformnametree_waveform_name_tree" value="66" description="" />
   <keyValuePair key="waveformview_add" value="2" description="" />
   <keyValuePair key="xdccategorytree_xdc_category_tree" value="38" description="" />
   <keyValuePair key="xdcviewertreetablepanel_xdc_viewer_tree_table" value="24" description="" />
   <keyValuePair key="xpg_combobox_value_of_specified_parameter" value="1" description="" />
   <keyValuePair key="xpg_combobox_value_of_specified_parameter_manual" value="1" description="" />
   <keyValuePair key="xpg_ipsymbol_show_disabled_ports" value="2" description="" />
  </section>
  <section name="java_command_handlers" level="2" order="2" description="">
   <keyValuePair key="addprobestowaveform" value="2" description="" />
   <keyValuePair key="addsources" value="3" description="" />
   <keyValuePair key="autoassignaddress" value="7" description="" />
   <keyValuePair key="autoconnectport" value="18" description="" />
   <keyValuePair key="autoconnecttarget" value="63" description="" />
   <keyValuePair key="closeserver" value="11" description="" />
   <keyValuePair key="closetarget" value="4" description="" />
   <keyValuePair key="coreview" value="50" description="" />
   <keyValuePair key="createblockdesign" value="1" description="" />
   <keyValuePair key="createtophdl" value="6" description="" />
   <keyValuePair key="customizecore" value="43" description="" />
   <keyValuePair key="customizersbblock" value="233" description="" />
   <keyValuePair key="debugwizardcmdhandler" value="4" description="" />
   <keyValuePair key="disableautoretrigger" value="1" description="" />
   <keyValuePair key="editconstraintsets" value="1" description="" />
   <keyValuePair key="editcopy" value="4" description="" />
   <keyValuePair key="editdelete" value="112" description="" />
   <keyValuePair key="editpaste" value="6" description="" />
   <keyValuePair key="editproperties" value="12" description="" />
   <keyValuePair key="editredo" value="14" description="" />
   <keyValuePair key="editundo" value="49" description="" />
   <keyValuePair key="enableautoretrigger" value="1" description="" />
   <keyValuePair key="exportiladata" value="1" description="" />
   <keyValuePair key="fileexit" value="4" description="" />
   <keyValuePair key="generateoutputforbdfile" value="1" description="" />
   <keyValuePair key="launchopentarget" value="2" description="" />
   <keyValuePair key="launchprogramfpga" value="3" description="" />
   <keyValuePair key="makersbconnection" value="1" description="" />
   <keyValuePair key="managecompositetargets" value="38" description="" />
   <keyValuePair key="newexporthardware" value="58" description="" />
   <keyValuePair key="newlaunchhardware" value="99" description="" />
   <keyValuePair key="newproject" value="2" description="" />
   <keyValuePair key="openaddresseditor" value="5" description="" />
   <keyValuePair key="openblockdesign" value="1" description="" />
   <keyValuePair key="openhardwaredashboard" value="1" description="" />
   <keyValuePair key="openhardwaremanager" value="30" description="" />
   <keyValuePair key="openproject" value="3" description="" />
   <keyValuePair key="openrecenttarget" value="2" description="" />
   <keyValuePair key="opentarget" value="3" description="" />
   <keyValuePair key="recustomizecore" value="14" description="" />
   <keyValuePair key="refreshdevice" value="15" description="" />
   <keyValuePair key="refreshserver" value="1" description="" />
   <keyValuePair key="regeneratersblayout" value="11" description="" />
   <keyValuePair key="reloaddesign" value="1" description="" />
   <keyValuePair key="reportclockinteraction" value="1" description="" />
   <keyValuePair key="reportclocknetworks" value="1" description="" />
   <keyValuePair key="reportipstatus" value="6" description="" />
   <keyValuePair key="reportmethodology" value="1" description="" />
   <keyValuePair key="reporttimingsummary" value="10" description="" />
   <keyValuePair key="runbitgen" value="70" description="" />
   <keyValuePair key="runimplementation" value="47" description="" />
   <keyValuePair key="runschematic" value="9" description="" />
   <keyValuePair key="runsynthesis" value="41" description="" />
   <keyValuePair key="runtrigger" value="31" description="" />
   <keyValuePair key="runtriggerimmediate" value="13" description="" />
   <keyValuePair key="savedesign" value="10" description="" />
   <keyValuePair key="savefileproxyhandler" value="1" description="" />
   <keyValuePair key="saversbdesign" value="90" description="" />
   <keyValuePair key="showsource" value="3" description="" />
   <keyValuePair key="showview" value="64" description="" />
   <keyValuePair key="simulationrun" value="1" description="" />
   <keyValuePair key="stoptrigger" value="17" description="" />
   <keyValuePair key="timingconstraintswizard" value="5" description="" />
   <keyValuePair key="toggleautofitselection" value="4" description="" />
   <keyValuePair key="toggleselectareamode" value="7" description="" />
   <keyValuePair key="toolssettings" value="8" description="" />
   <keyValuePair key="toolstemplates" value="7" description="" />
   <keyValuePair key="unmapaddresssegment" value="4" description="" />
   <keyValuePair key="upgradeip" value="13" description="" />
   <keyValuePair key="validatersbdesign" value="83" description="" />
   <keyValuePair key="viewlayoutcmd" value="3" description="" />
   <keyValuePair key="viewtaskimplementation" value="11" description="" />
   <keyValuePair key="viewtaskrtlanalysis" value="2" description="" />
   <keyValuePair key="viewtasksynthesis" value="3" description="" />
   <keyValuePair key="xdcsetinputdelay" value="5" description="" />
   <keyValuePair key="zoomfit" value="1" description="" />
   <keyValuePair key="zoomin" value="1" description="" />
   <keyValuePair key="zoomout" value="1" description="" />
  </section>
  <section name="other_data" level="2" order="3" description="">
   <keyValuePair key="guimode" value="97" description="" />
  </section>
  <section name="project_data" level="2" order="4" description="">
   <keyValuePair key="constraintsetcount" value="2" description="" />
   <keyValuePair key="core_container" value="false" description="" />
   <keyValuePair key="currentimplrun" value="impl_1" description="" />
   <keyValuePair key="currentsynthesisrun" value="synth_1" description="" />
   <keyValuePair key="default_library" value="xil_defaultlib" description="" />
   <keyValuePair key="designmode" value="RTL" description="" />
   <keyValuePair key="export_simulation_activehdl" value="53" description="" />
   <keyValuePair key="export_simulation_ies" value="53" description="" />
   <keyValuePair key="export_simulation_modelsim" value="53" description="" />
   <keyValuePair key="export_simulation_questa" value="53" description="" />
   <keyValuePair key="export_simulation_riviera" value="53" description="" />
   <keyValuePair key="export_simulation_vcs" value="53" description="" />
   <keyValuePair key="export_simulation_xsim" value="53" description="" />
   <keyValuePair key="implstrategy" value="Vivado Implementation Defaults" description="" />
   <keyValuePair key="launch_simulation_activehdl" value="0" description="" />
   <keyValuePair key="launch_simulation_ies" value="0" description="" />
   <keyValuePair key="launch_simulation_modelsim" value="0" description="" />
   <keyValuePair key="launch_simulation_questa" value="0" description="" />
   <keyValuePair key="launch_simulation_riviera" value="0" description="" />
   <keyValuePair key="launch_simulation_vcs" value="0" description="" />
   <keyValuePair key="launch_simulation_xsim" value="1" description="" />
   <keyValuePair key="simulator_language" value="Mixed" description="" />
   <keyValuePair key="srcsetcount" value="3" description="" />
   <keyValuePair key="synthesisstrategy" value="Vivado Synthesis Defaults" description="" />
   <keyValuePair key="target_language" value="Verilog" description="" />
   <keyValuePair key="target_simulator" value="XSim" description="" />
   <keyValuePair key="totalimplruns" value="3" description="" />
   <keyValuePair key="totalsynthesisruns" value="3" description="" />
  </section>
 </section>
 <section name="xsim" level="1" order="10" description="">
  <section name="command_line_options" level="2" order="1" description="">
   <keyValuePair key="-sim_mode" value="default::behavioral" description="" />
   <keyValuePair key="-sim_type" value="default::" description="" />
  </section>
 </section>
</section>
</webTalkData>
