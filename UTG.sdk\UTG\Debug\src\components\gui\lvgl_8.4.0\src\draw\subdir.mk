################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_arc.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_img.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_label.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_layer.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_line.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_mask.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_rect.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_transform.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_draw_triangle.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_img_buf.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_img_cache.c \
../src/components/gui/lvgl_8.4.0/src/draw/lv_img_decoder.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_arc.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_img.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_label.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_layer.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_line.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_mask.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_rect.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_transform.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_triangle.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_img_buf.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_img_cache.o \
./src/components/gui/lvgl_8.4.0/src/draw/lv_img_decoder.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_arc.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_img.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_label.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_layer.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_line.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_mask.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_rect.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_transform.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_draw_triangle.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_img_buf.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_img_cache.d \
./src/components/gui/lvgl_8.4.0/src/draw/lv_img_decoder.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/src/draw/%.o: ../src/components/gui/lvgl_8.4.0/src/draw/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


