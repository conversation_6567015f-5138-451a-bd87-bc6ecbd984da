<?xml version="1.0" encoding="UTF-8"?>
<spirit:component xmlns:xilinx="http://www.xilinx.com" xmlns:spirit="http://www.spiritconsortium.org/XMLSchema/SPIRIT/1685-2009" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <spirit:vendor>lzcx</spirit:vendor>
  <spirit:library>user</spirit:library>
  <spirit:name>AXI_ADC</spirit:name>
  <spirit:version>1.0</spirit:version>
  <spirit:busInterfaces>
    <spirit:busInterface>
      <spirit:name>S00_AXI</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm_rtl" spirit:version="1.0"/>
      <spirit:slave>
        <spirit:memoryMapRef spirit:memoryMapRef="S00_AXI"/>
      </spirit:slave>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_awaddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWPROT</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_awprot</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_awvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_awready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_wdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WSTRB</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_wstrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_wvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_wready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_bresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_bvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_bready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_araddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARPROT</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_arprot</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_arvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_arready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_rdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_rresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_rvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_rready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>WIZ_DATA_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:id="BUSIFPARAM_VALUE.S00_AXI.WIZ_DATA_WIDTH" spirit:choiceRef="choice_list_6fc15197">32</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>WIZ_NUM_REG</spirit:name>
          <spirit:value spirit:format="long" spirit:id="BUSIFPARAM_VALUE.S00_AXI.WIZ_NUM_REG" spirit:minimum="4" spirit:maximum="512" spirit:rangeType="long">8</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>SUPPORTS_NARROW_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:id="BUSIFPARAM_VALUE.S00_AXI.SUPPORTS_NARROW_BURST" spirit:choiceRef="choice_pairs_ce1226b1">0</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M00_AXI</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="interface" spirit:name="aximm_rtl" spirit:version="1.0"/>
      <spirit:master>
        <spirit:addressSpaceRef spirit:addressSpaceRef="M00_AXI"/>
      </spirit:master>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awaddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWLEN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awlen</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWSIZE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awsize</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWBURST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awburst</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWLOCK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awlock</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWCACHE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awcache</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWPROT</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awprot</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWQOS</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awqos</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWUSER</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awuser</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>AWREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_awready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_wdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WSTRB</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_wstrb</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_wlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WUSER</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_wuser</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_wvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>WREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_wready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_bid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_bresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BUSER</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_buser</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_bvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>BREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_bready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARADDR</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_araddr</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARLEN</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arlen</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARSIZE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arsize</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARBURST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arburst</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARLOCK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arlock</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARCACHE</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arcache</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARPROT</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arprot</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARQOS</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arqos</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARUSER</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_aruser</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>ARREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_arready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_rid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RDATA</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_rdata</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RRESP</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_rresp</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RLAST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_rlast</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RUSER</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_ruser</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RVALID</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_rvalid</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RREADY</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_rready</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>WIZ_DATA_WIDTH</spirit:name>
          <spirit:value spirit:format="long" spirit:id="BUSIFPARAM_VALUE.M00_AXI.WIZ_DATA_WIDTH" spirit:choiceRef="choice_list_6fc15197">32</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>SUPPORTS_NARROW_BURST</spirit:name>
          <spirit:value spirit:format="long" spirit:id="BUSIFPARAM_VALUE.M00_AXI.SUPPORTS_NARROW_BURST" spirit:choiceRef="choice_pairs_ce1226b1">0</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S00_AXI_RST</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S00_AXI_RST.POLARITY" spirit:choiceRef="choice_list_9d8b0d81">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>S00_AXI_CLK</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>s00_axi_aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S00_AXI_CLK.ASSOCIATED_BUSIF">S00_AXI</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.S00_AXI_CLK.ASSOCIATED_RESET">s00_axi_aresetn</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M00_AXI_RST</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="reset_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>RST</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_aresetn</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>POLARITY</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.M00_AXI_RST.POLARITY" spirit:choiceRef="choice_list_9d8b0d81">ACTIVE_LOW</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
    <spirit:busInterface>
      <spirit:name>M00_AXI_CLK</spirit:name>
      <spirit:busType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock" spirit:version="1.0"/>
      <spirit:abstractionType spirit:vendor="xilinx.com" spirit:library="signal" spirit:name="clock_rtl" spirit:version="1.0"/>
      <spirit:slave/>
      <spirit:portMaps>
        <spirit:portMap>
          <spirit:logicalPort>
            <spirit:name>CLK</spirit:name>
          </spirit:logicalPort>
          <spirit:physicalPort>
            <spirit:name>m00_axi_aclk</spirit:name>
          </spirit:physicalPort>
        </spirit:portMap>
      </spirit:portMaps>
      <spirit:parameters>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_BUSIF</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.M00_AXI_CLK.ASSOCIATED_BUSIF">M00_AXI</spirit:value>
        </spirit:parameter>
        <spirit:parameter>
          <spirit:name>ASSOCIATED_RESET</spirit:name>
          <spirit:value spirit:id="BUSIFPARAM_VALUE.M00_AXI_CLK.ASSOCIATED_RESET">m00_axi_aresetn</spirit:value>
        </spirit:parameter>
      </spirit:parameters>
    </spirit:busInterface>
  </spirit:busInterfaces>
  <spirit:addressSpaces>
    <spirit:addressSpace>
      <spirit:name>M00_AXI</spirit:name>
      <spirit:range spirit:format="long" spirit:resolve="dependent" spirit:dependency="pow(2,(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ADDR_WIDTH&apos;)) - 1) + 1)" spirit:minimum="0" spirit:maximum="4294967296" spirit:rangeType="long">4294967296</spirit:range>
      <spirit:width spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_DATA_WIDTH&apos;)) - 1) + 1">32</spirit:width>
    </spirit:addressSpace>
  </spirit:addressSpaces>
  <spirit:memoryMaps>
    <spirit:memoryMap>
      <spirit:name>S00_AXI</spirit:name>
      <spirit:addressBlock>
        <spirit:name>S00_AXI_reg</spirit:name>
        <spirit:baseAddress spirit:format="long" spirit:resolve="user">0</spirit:baseAddress>
        <spirit:range spirit:format="long">4096</spirit:range>
        <spirit:width spirit:format="long">32</spirit:width>
        <spirit:usage>register</spirit:usage>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>OFFSET_BASE_PARAM</spirit:name>
            <spirit:value spirit:id="ADDRBLOCKPARAM_VALUE.S00_AXI.S00_AXI_REG.OFFSET_BASE_PARAM">C_S00_AXI_BASEADDR</spirit:value>
          </spirit:parameter>
          <spirit:parameter>
            <spirit:name>OFFSET_HIGH_PARAM</spirit:name>
            <spirit:value spirit:id="ADDRBLOCKPARAM_VALUE.S00_AXI.S00_AXI_REG.OFFSET_HIGH_PARAM">C_S00_AXI_HIGHADDR</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:addressBlock>
    </spirit:memoryMap>
  </spirit:memoryMaps>
  <spirit:model>
    <spirit:views>
      <spirit:view>
        <spirit:name>xilinx_verilogsynthesis</spirit:name>
        <spirit:displayName>Verilog Synthesis</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:synthesis</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>AXI_ADC_v1_0</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogsynthesis_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>viewChecksum</spirit:name>
            <spirit:value>355dbd86</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_verilogbehavioralsimulation</spirit:name>
        <spirit:displayName>Verilog Simulation</spirit:displayName>
        <spirit:envIdentifier>verilogSource:vivado.xilinx.com:simulation</spirit:envIdentifier>
        <spirit:language>verilog</spirit:language>
        <spirit:modelName>AXI_ADC_v1_0</spirit:modelName>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_verilogbehavioralsimulation_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>viewChecksum</spirit:name>
            <spirit:value>355dbd86</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_softwaredriver</spirit:name>
        <spirit:displayName>Software Driver</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:sw.driver</spirit:envIdentifier>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_softwaredriver_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>viewChecksum</spirit:name>
            <spirit:value>4af4573c</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>xilinx_xpgui</spirit:name>
        <spirit:displayName>UI Layout</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:xgui.ui</spirit:envIdentifier>
        <spirit:fileSetRef>
          <spirit:localName>xilinx_xpgui_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>viewChecksum</spirit:name>
            <spirit:value>484b0775</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
      <spirit:view>
        <spirit:name>bd_tcl</spirit:name>
        <spirit:displayName>Block Diagram</spirit:displayName>
        <spirit:envIdentifier>:vivado.xilinx.com:block.diagram</spirit:envIdentifier>
        <spirit:fileSetRef>
          <spirit:localName>bd_tcl_view_fileset</spirit:localName>
        </spirit:fileSetRef>
        <spirit:parameters>
          <spirit:parameter>
            <spirit:name>viewChecksum</spirit:name>
            <spirit:value>16328387</spirit:value>
          </spirit:parameter>
        </spirit:parameters>
      </spirit:view>
    </spirit:views>
    <spirit:ports>
      <spirit:port>
        <spirit:name>ADC_OR</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>ADC_DCLK</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>ADC_DATA</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">11</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>std_logic_vector</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>ADC_PDWN</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>ADC_SP_CLK</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>ADC_START</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>ULTRASOUND_PLUS</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_awaddr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">5</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_awprot</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_awvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_awready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_wdata</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_wstrb</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_wvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_wready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_bresp</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_bvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_bready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_araddr</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">5</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_arprot</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_arvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_arready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_rdata</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_rresp</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_rvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_rready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>s00_axi_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_awid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awaddr</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ADDR_WIDTH&apos;)) - 1)">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awlen</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awsize</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awburst</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awlock</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awcache</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awprot</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awqos</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awuser</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_AWUSER_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_awuser" xilinx:dependency="spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_AWUSER_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_awready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_wdata</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_DATA_WIDTH&apos;)) - 1)">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_wstrb</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="((spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_DATA_WIDTH&apos;)) / 8) - 1)">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_wlast</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_wuser</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_WUSER_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_wuser" xilinx:dependency="spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_WUSER_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_wvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_wready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_bid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_bid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_bresp</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_buser</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_BUSER_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_buser" xilinx:dependency="spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_BUSER_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_bvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_bready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_arid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_araddr</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ADDR_WIDTH&apos;)) - 1)">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arlen</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">7</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arsize</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arburst</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arlock</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arcache</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arprot</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">2</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arqos</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">3</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_aruser</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ARUSER_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_aruser" xilinx:dependency="spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_ARUSER_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_arready</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_rid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_rid" xilinx:dependency="spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_rdata</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_DATA_WIDTH&apos;)) - 1)">31</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_rresp</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long">1</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_rlast</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_ruser</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:vector>
            <spirit:left spirit:format="long" spirit:resolve="dependent" spirit:dependency="(spirit:decode(id(&apos;MODELPARAM_VALUE.C_M00_AXI_RUSER_WIDTH&apos;)) - 1)">0</spirit:left>
            <spirit:right spirit:format="long">0</spirit:right>
          </spirit:vector>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
          <spirit:driver>
            <spirit:defaultValue spirit:format="long">0</spirit:defaultValue>
          </spirit:driver>
        </spirit:wire>
        <spirit:vendorExtensions>
          <xilinx:portInfo>
            <xilinx:enablement>
              <xilinx:isEnabled xilinx:resolve="dependent" xilinx:id="PORT_ENABLEMENT.m00_axi_ruser" xilinx:dependency="spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_RUSER_WIDTH&apos;)) >0">true</xilinx:isEnabled>
            </xilinx:enablement>
          </xilinx:portInfo>
        </spirit:vendorExtensions>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_rvalid</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_rready</spirit:name>
        <spirit:wire>
          <spirit:direction>out</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_aclk</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
      <spirit:port>
        <spirit:name>m00_axi_aresetn</spirit:name>
        <spirit:wire>
          <spirit:direction>in</spirit:direction>
          <spirit:wireTypeDefs>
            <spirit:wireTypeDef>
              <spirit:typeName>wire</spirit:typeName>
              <spirit:viewNameRef>xilinx_verilogsynthesis</spirit:viewNameRef>
              <spirit:viewNameRef>xilinx_verilogbehavioralsimulation</spirit:viewNameRef>
            </spirit:wireTypeDef>
          </spirit:wireTypeDefs>
        </spirit:wire>
      </spirit:port>
    </spirit:ports>
    <spirit:modelParameters>
      <spirit:modelParameter xsi:type="spirit:nameValueTypeType" spirit:dataType="integer">
        <spirit:name>C_M00_AXI_TARGET_SLAVE_BASE_ADDR</spirit:name>
        <spirit:displayName>C M00 AXI TARGET SLAVE BASE ADDR</spirit:displayName>
        <spirit:description>Base address of targeted slave</spirit:description>
        <spirit:value spirit:format="bitString" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_TARGET_SLAVE_BASE_ADDR" spirit:order="7" spirit:bitStringLength="32">0x40000000</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_BURST_LEN</spirit:name>
        <spirit:displayName>C M00 AXI BURST LEN</spirit:displayName>
        <spirit:description>Burst Length. Supports 1, 2, 4, 8, 16, 32, 64, 128, 256 burst lengths</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_BURST_LEN" spirit:choiceRef="choice_list_85010fde" spirit:order="8">16</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_ID_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI ID WIDTH</spirit:displayName>
        <spirit:description>Thread ID Width</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_ID_WIDTH" spirit:dependency="((spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;)) &lt;= 0 ) + (spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_ID_WIDTH&apos;))))" spirit:order="9" spirit:minimum="0" spirit:maximum="32" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_ADDR_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI ADDR WIDTH</spirit:displayName>
        <spirit:description>Width of Address Bus</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_ADDR_WIDTH" spirit:order="10" spirit:rangeType="long">32</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_DATA_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI DATA WIDTH</spirit:displayName>
        <spirit:description>Width of Data Bus</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_DATA_WIDTH" spirit:order="11" spirit:rangeType="long">32</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_AWUSER_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI AWUSER WIDTH</spirit:displayName>
        <spirit:description>Width of User Write Address Bus</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_AWUSER_WIDTH" spirit:dependency="((spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_AWUSER_WIDTH&apos;)) &lt;= 0 ) + (spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_AWUSER_WIDTH&apos;))))" spirit:order="12" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_ARUSER_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI ARUSER WIDTH</spirit:displayName>
        <spirit:description>Width of User Read Address Bus</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_ARUSER_WIDTH" spirit:dependency="((spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_ARUSER_WIDTH&apos;)) &lt;= 0 ) + (spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_ARUSER_WIDTH&apos;))))" spirit:order="13" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_WUSER_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI WUSER WIDTH</spirit:displayName>
        <spirit:description>Width of User Write Data Bus</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_WUSER_WIDTH" spirit:dependency="((spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_WUSER_WIDTH&apos;)) &lt;= 0 ) + (spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_WUSER_WIDTH&apos;))))" spirit:order="14" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_RUSER_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI RUSER WIDTH</spirit:displayName>
        <spirit:description>Width of User Read Data Bus</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_RUSER_WIDTH" spirit:dependency="((spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_RUSER_WIDTH&apos;)) &lt;= 0 ) + (spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_RUSER_WIDTH&apos;))))" spirit:order="15" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
      <spirit:modelParameter spirit:dataType="integer">
        <spirit:name>C_M00_AXI_BUSER_WIDTH</spirit:name>
        <spirit:displayName>C M00 AXI BUSER WIDTH</spirit:displayName>
        <spirit:description>Width of User Response Bus</spirit:description>
        <spirit:value spirit:format="long" spirit:resolve="generated" spirit:id="MODELPARAM_VALUE.C_M00_AXI_BUSER_WIDTH" spirit:dependency="((spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_BUSER_WIDTH&apos;)) &lt;= 0 ) + (spirit:decode(id(&apos;PARAM_VALUE.C_M00_AXI_BUSER_WIDTH&apos;))))" spirit:order="16" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
      </spirit:modelParameter>
    </spirit:modelParameters>
  </spirit:model>
  <spirit:choices>
    <spirit:choice>
      <spirit:name>choice_list_6fc15197</spirit:name>
      <spirit:enumeration>32</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_85010fde</spirit:name>
      <spirit:enumeration>1</spirit:enumeration>
      <spirit:enumeration>2</spirit:enumeration>
      <spirit:enumeration>4</spirit:enumeration>
      <spirit:enumeration>8</spirit:enumeration>
      <spirit:enumeration>16</spirit:enumeration>
      <spirit:enumeration>32</spirit:enumeration>
      <spirit:enumeration>64</spirit:enumeration>
      <spirit:enumeration>128</spirit:enumeration>
      <spirit:enumeration>256</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_list_9d8b0d81</spirit:name>
      <spirit:enumeration>ACTIVE_HIGH</spirit:enumeration>
      <spirit:enumeration>ACTIVE_LOW</spirit:enumeration>
    </spirit:choice>
    <spirit:choice>
      <spirit:name>choice_pairs_ce1226b1</spirit:name>
      <spirit:enumeration spirit:text="true">1</spirit:enumeration>
      <spirit:enumeration spirit:text="false">0</spirit:enumeration>
    </spirit:choice>
  </spirit:choices>
  <spirit:fileSets>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogsynthesis_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>src/adc_top.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/async_fifo.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/m_axis_s2mm.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/s_axi.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/s_axi_adc_ctrl.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/AXI_ADC_v1_0.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
        <spirit:userFileType>CHECKSUM_6d037f93</spirit:userFileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_verilogbehavioralsimulation_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>src/adc_top.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/async_fifo.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/m_axis_s2mm.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/s_axi.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/s_axi_adc_ctrl.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>src/AXI_ADC_v1_0.v</spirit:name>
        <spirit:fileType>verilogSource</spirit:fileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_softwaredriver_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>drivers/AXI_ADC_v1_0/data/AXI_ADC.mdd</spirit:name>
        <spirit:userFileType>mdd</spirit:userFileType>
        <spirit:userFileType>driver_mdd</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>drivers/AXI_ADC_v1_0/data/AXI_ADC.tcl</spirit:name>
        <spirit:fileType>tclSource</spirit:fileType>
        <spirit:userFileType>driver_tcl</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>drivers/AXI_ADC_v1_0/src/Makefile</spirit:name>
        <spirit:userFileType>driver_src</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>drivers/AXI_ADC_v1_0/src/AXI_ADC.h</spirit:name>
        <spirit:fileType>cSource</spirit:fileType>
        <spirit:userFileType>driver_src</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>drivers/AXI_ADC_v1_0/src/AXI_ADC.c</spirit:name>
        <spirit:fileType>cSource</spirit:fileType>
        <spirit:userFileType>driver_src</spirit:userFileType>
      </spirit:file>
      <spirit:file>
        <spirit:name>drivers/AXI_ADC_v1_0/src/AXI_ADC_selftest.c</spirit:name>
        <spirit:fileType>cSource</spirit:fileType>
        <spirit:userFileType>driver_src</spirit:userFileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>xilinx_xpgui_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>xgui/AXI_ADC_v1_0.tcl</spirit:name>
        <spirit:fileType>tclSource</spirit:fileType>
        <spirit:userFileType>CHECKSUM_484b0775</spirit:userFileType>
        <spirit:userFileType>XGUI_VERSION_2</spirit:userFileType>
      </spirit:file>
    </spirit:fileSet>
    <spirit:fileSet>
      <spirit:name>bd_tcl_view_fileset</spirit:name>
      <spirit:file>
        <spirit:name>bd/bd.tcl</spirit:name>
        <spirit:fileType>tclSource</spirit:fileType>
      </spirit:file>
    </spirit:fileSet>
  </spirit:fileSets>
  <spirit:description>adc capture to ddram</spirit:description>
  <spirit:parameters>
    <spirit:parameter>
      <spirit:name>C_S00_AXI_BASEADDR</spirit:name>
      <spirit:displayName>C S00 AXI BASEADDR</spirit:displayName>
      <spirit:value spirit:format="bitString" spirit:resolve="user" spirit:id="PARAM_VALUE.C_S00_AXI_BASEADDR" spirit:order="5" spirit:bitStringLength="32">0xFFFFFFFF</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:id="PARAM_ENABLEMENT.C_S00_AXI_BASEADDR">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_S00_AXI_HIGHADDR</spirit:name>
      <spirit:displayName>C S00 AXI HIGHADDR</spirit:displayName>
      <spirit:value spirit:format="bitString" spirit:resolve="user" spirit:id="PARAM_VALUE.C_S00_AXI_HIGHADDR" spirit:order="6" spirit:bitStringLength="32">0x00000000</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:id="PARAM_ENABLEMENT.C_S00_AXI_HIGHADDR">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_TARGET_SLAVE_BASE_ADDR</spirit:name>
      <spirit:displayName>C M00 AXI TARGET SLAVE BASE ADDR</spirit:displayName>
      <spirit:description>Base address of targeted slave</spirit:description>
      <spirit:value spirit:format="bitString" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_TARGET_SLAVE_BASE_ADDR" spirit:order="7" spirit:bitStringLength="32">0x40000000</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_BURST_LEN</spirit:name>
      <spirit:displayName>C M00 AXI BURST LEN</spirit:displayName>
      <spirit:description>Burst Length. Supports 1, 2, 4, 8, 16, 32, 64, 128, 256 burst lengths</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_BURST_LEN" spirit:choiceRef="choice_list_85010fde" spirit:order="8">16</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_ID_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI ID WIDTH</spirit:displayName>
      <spirit:description>Thread ID Width</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_ID_WIDTH" spirit:order="9" spirit:minimum="0" spirit:maximum="32" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_ADDR_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI ADDR WIDTH</spirit:displayName>
      <spirit:description>Width of Address Bus</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_ADDR_WIDTH" spirit:order="10" spirit:rangeType="long">32</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:id="PARAM_ENABLEMENT.C_M00_AXI_ADDR_WIDTH">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_DATA_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI DATA WIDTH</spirit:displayName>
      <spirit:description>Width of Data Bus</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_DATA_WIDTH" spirit:choiceRef="choice_list_6fc15197" spirit:order="11">32</spirit:value>
      <spirit:vendorExtensions>
        <xilinx:parameterInfo>
          <xilinx:enablement>
            <xilinx:isEnabled xilinx:id="PARAM_ENABLEMENT.C_M00_AXI_DATA_WIDTH">false</xilinx:isEnabled>
          </xilinx:enablement>
        </xilinx:parameterInfo>
      </spirit:vendorExtensions>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_AWUSER_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI AWUSER WIDTH</spirit:displayName>
      <spirit:description>Width of User Write Address Bus</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_AWUSER_WIDTH" spirit:order="12" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_ARUSER_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI ARUSER WIDTH</spirit:displayName>
      <spirit:description>Width of User Read Address Bus</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_ARUSER_WIDTH" spirit:order="13" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_WUSER_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI WUSER WIDTH</spirit:displayName>
      <spirit:description>Width of User Write Data Bus</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_WUSER_WIDTH" spirit:order="14" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_RUSER_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI RUSER WIDTH</spirit:displayName>
      <spirit:description>Width of User Read Data Bus</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_RUSER_WIDTH" spirit:order="15" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>C_M00_AXI_BUSER_WIDTH</spirit:name>
      <spirit:displayName>C M00 AXI BUSER WIDTH</spirit:displayName>
      <spirit:description>Width of User Response Bus</spirit:description>
      <spirit:value spirit:format="long" spirit:resolve="user" spirit:id="PARAM_VALUE.C_M00_AXI_BUSER_WIDTH" spirit:order="16" spirit:minimum="0" spirit:maximum="1024" spirit:rangeType="long">1</spirit:value>
    </spirit:parameter>
    <spirit:parameter>
      <spirit:name>Component_Name</spirit:name>
      <spirit:value spirit:resolve="user" spirit:id="PARAM_VALUE.Component_Name" spirit:order="1">AXI_ADC_v1_0</spirit:value>
    </spirit:parameter>
  </spirit:parameters>
  <spirit:vendorExtensions>
    <xilinx:coreExtensions>
      <xilinx:supportedFamilies>
        <xilinx:family xilinx:lifeCycle="Pre-Production">zynq</xilinx:family>
      </xilinx:supportedFamilies>
      <xilinx:taxonomies>
        <xilinx:taxonomy>AXI_Peripheral</xilinx:taxonomy>
      </xilinx:taxonomies>
      <xilinx:displayName>AXI_ADC_v1.0</xilinx:displayName>
      <xilinx:coreRevision>1</xilinx:coreRevision>
      <xilinx:upgrades>
        <xilinx:canUpgradeFrom>xilinx.com:user:AXI_ADC:1.0</xilinx:canUpgradeFrom>
      </xilinx:upgrades>
      <xilinx:coreCreationDateTime>2025-08-01T08:25:01Z</xilinx:coreCreationDateTime>
      <xilinx:tags>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@d700968_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7e488a4c_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3e2455e0_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@18bfdcf4_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@73f158f_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@69d28e5b_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@69ec7e2_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1eeee453_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@60d48ee1_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4373c2b5_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3d5ef93d_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2cc925a6_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1bea2dd7_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3d04e23f_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5492854a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4a604a3c_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4b05ae9b_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@54b955fa_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@73dc7e90_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@fc41372_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5e44be30_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@c506db2_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@650c23b_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@23c9e96_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@cb7dd95_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2b93032b_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2600ff29_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3b1a2c8_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5ef7ecab_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7d4ec4bb_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@29719435_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2290118f_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3db0ac09_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@46793436_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@ef59869_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@136400e5_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2bf70a89_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@14c6ec4a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1c060cb7_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6b7397b2_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5e745018_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6e5b07dd_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7070420e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5332f72_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@56241e2a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@734f5a08_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1c6140ad_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@68cf1de_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@234bc680_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1bd28176_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3f450787_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5da6c199_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@9a65735_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4d551811_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@128dd9e6_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@996606d_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@46606ed7_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5d164000_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7e371ca1_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3c83f0b8_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3488cf27_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@325cc94e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3f24eca7_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1f84ba98_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@12dd2676_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@17cc47cd_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1902f68d_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@611de16_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6b805db3_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@26ba28c5_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@623e6c29_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7333eb5c_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6de7be11_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4c7a189a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@725c03b7_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4396c41d_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@70291e1b_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@31666a0a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@d9af815_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@27b2f97c_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5c3c8ee8_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6421f17e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@b7d5623_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@24510606_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@239ab6e6_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1e137119_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@44d44152_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5d63fc0b_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1e15666a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5014c04_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5f93326a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@77a5785d_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@544df9e6_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2aceb2d3_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@46567fd5_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1c01d540_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6592ae62_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5c2b89c4_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@8b6aef_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6e5df763_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@764db3ef_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@16e33444_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5c2f86d_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@a24cce7_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@364325f1_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3942bb0a_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@36cd3a84_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1af433c9_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@610e93d3_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@626ffb99_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@42385003_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@75ab5d48_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@32f2164c_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@31838501_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@574f2005_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4836fd86_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3663a66e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1a1b5600_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7d7d573e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6b03f3e5_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7f9e1d20_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@421e7274_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@20241b91_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@39f9f65e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4d5cfcf0_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@4598cd06_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@892a01_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6cb3c7db_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@56e3603f_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6d2d16bb_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@6a44f7d5_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2fdd5210_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@9654d36_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@1179ca26_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@74893f0_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3f5bbc90_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@5438a009_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@7967a01e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3c901b20_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@183ae61e_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@3f8b39cf_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@cfe4db8_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
        <xilinx:tag xilinx:name="ui.data.coregen.dd@2dbbbb95_ARCHIVE_LOCATION">f:/UTG/IP/ip_repo/AXI_ADC_1.0</xilinx:tag>
      </xilinx:tags>
    </xilinx:coreExtensions>
    <xilinx:packagingInfo>
      <xilinx:xilinxVersion>2018.3</xilinx:xilinxVersion>
      <xilinx:checksum xilinx:scope="busInterfaces" xilinx:value="ee6b75b4"/>
      <xilinx:checksum xilinx:scope="addressSpaces" xilinx:value="55bfeea0"/>
      <xilinx:checksum xilinx:scope="memoryMaps" xilinx:value="ed1368d5"/>
      <xilinx:checksum xilinx:scope="fileGroups" xilinx:value="f2d38fd5"/>
      <xilinx:checksum xilinx:scope="ports" xilinx:value="0fc566e3"/>
      <xilinx:checksum xilinx:scope="hdlParameters" xilinx:value="e29cbb96"/>
      <xilinx:checksum xilinx:scope="parameters" xilinx:value="1b2744e1"/>
    </xilinx:packagingInfo>
  </spirit:vendorExtensions>
</spirit:component>
