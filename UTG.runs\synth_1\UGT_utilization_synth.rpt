Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
-------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Aug  4 14:37:05 2025
| Host         : wantong running 64-bit major release  (build 9200)
| Command      : report_utilization -file UGT_utilization_synth.rpt -pb UGT_utilization_synth.pb
| Design       : UGT
| Device       : 7z020clg400-2
| Design State : Synthesized
-------------------------------------------------------------------------------------------------

Utilization Design Information

Table of Contents
-----------------
1. Slice Logic
1.1 Summary of Registers by Type
2. Memory
3. DSP
4. IO and GT Specific
5. Clocking
6. Specific Feature
7. Primitives
8. Black Boxes
9. Instantiated Netlists

1. Slice Logic
--------------

+----------------------------+-------+-------+-----------+-------+
|          Site Type         |  Used | Fixed | Available | Util% |
+----------------------------+-------+-------+-----------+-------+
| Slice LUTs*                |  7611 |     0 |     53200 | 14.31 |
|   LUT as Logic             |  6717 |     0 |     53200 | 12.63 |
|   LUT as Memory            |   894 |     0 |     17400 |  5.14 |
|     LUT as Distributed RAM |   536 |     0 |           |       |
|     LUT as Shift Register  |   358 |     0 |           |       |
| Slice Registers            | 11722 |     0 |    106400 | 11.02 |
|   Register as Flip Flop    | 11722 |     0 |    106400 | 11.02 |
|   Register as Latch        |     0 |     0 |    106400 |  0.00 |
| F7 Muxes                   |   236 |     0 |     26600 |  0.89 |
| F8 Muxes                   |    11 |     0 |     13300 |  0.08 |
+----------------------------+-------+-------+-----------+-------+
* Warning! The Final LUT count, after physical optimizations and full implementation, is typically lower. Run opt_design after synthesis, if not already completed, for a more realistic count.


1.1 Summary of Registers by Type
--------------------------------

+-------+--------------+-------------+--------------+
| Total | Clock Enable | Synchronous | Asynchronous |
+-------+--------------+-------------+--------------+
| 0     |            _ |           - |            - |
| 0     |            _ |           - |          Set |
| 0     |            _ |           - |        Reset |
| 0     |            _ |         Set |            - |
| 0     |            _ |       Reset |            - |
| 0     |          Yes |           - |            - |
| 9     |          Yes |           - |          Set |
| 67    |          Yes |           - |        Reset |
| 433   |          Yes |         Set |            - |
| 11213 |          Yes |       Reset |            - |
+-------+--------------+-------------+--------------+


2. Memory
---------

+-------------------+------+-------+-----------+-------+
|     Site Type     | Used | Fixed | Available | Util% |
+-------------------+------+-------+-----------+-------+
| Block RAM Tile    |   10 |     0 |       140 |  7.14 |
|   RAMB36/FIFO*    |   10 |     0 |       140 |  7.14 |
|     RAMB36E1 only |   10 |       |           |       |
|   RAMB18          |    0 |     0 |       280 |  0.00 |
+-------------------+------+-------+-----------+-------+
* Note: Each Block RAM Tile only has one FIFO logic available and therefore can accommodate only one FIFO36E1 or one FIFO18E1. However, if a FIFO18E1 occupies a Block RAM Tile, that tile can still accommodate a RAMB18E1


3. DSP
------

+-----------+------+-------+-----------+-------+
| Site Type | Used | Fixed | Available | Util% |
+-----------+------+-------+-----------+-------+
| DSPs      |    0 |     0 |       220 |  0.00 |
+-----------+------+-------+-----------+-------+


4. IO and GT Specific
---------------------

+-----------------------------+------+-------+-----------+-------+
|          Site Type          | Used | Fixed | Available | Util% |
+-----------------------------+------+-------+-----------+-------+
| Bonded IOB                  |   53 |     0 |       125 | 42.40 |
| Bonded IPADs                |    0 |     0 |         2 |  0.00 |
| Bonded IOPADs               |    0 |     0 |       130 |  0.00 |
| PHY_CONTROL                 |    0 |     0 |         4 |  0.00 |
| PHASER_REF                  |    0 |     0 |         4 |  0.00 |
| OUT_FIFO                    |    0 |     0 |        16 |  0.00 |
| IN_FIFO                     |    0 |     0 |        16 |  0.00 |
| IDELAYCTRL                  |    0 |     0 |         4 |  0.00 |
| IBUFDS                      |    0 |     0 |       121 |  0.00 |
| PHASER_OUT/PHASER_OUT_PHY   |    0 |     0 |        16 |  0.00 |
| PHASER_IN/PHASER_IN_PHY     |    0 |     0 |        16 |  0.00 |
| IDELAYE2/IDELAYE2_FINEDELAY |    0 |     0 |       200 |  0.00 |
| ILOGIC                      |    0 |     0 |       125 |  0.00 |
| OLOGIC                      |    1 |     0 |       125 |  0.80 |
|   ODDR                      |    1 |       |           |       |
+-----------------------------+------+-------+-----------+-------+


5. Clocking
-----------

+------------+------+-------+-----------+-------+
|  Site Type | Used | Fixed | Available | Util% |
+------------+------+-------+-----------+-------+
| BUFGCTRL   |    2 |     0 |        32 |  6.25 |
| BUFIO      |    0 |     0 |        16 |  0.00 |
| MMCME2_ADV |    1 |     0 |         4 | 25.00 |
| PLLE2_ADV  |    0 |     0 |         4 |  0.00 |
| BUFMRCE    |    0 |     0 |         8 |  0.00 |
| BUFHCE     |    0 |     0 |        72 |  0.00 |
| BUFR       |    1 |     0 |        16 |  6.25 |
+------------+------+-------+-----------+-------+


6. Specific Feature
-------------------

+-------------+------+-------+-----------+-------+
|  Site Type  | Used | Fixed | Available | Util% |
+-------------+------+-------+-----------+-------+
| BSCANE2     |    0 |     0 |         4 |  0.00 |
| CAPTUREE2   |    0 |     0 |         1 |  0.00 |
| DNA_PORT    |    0 |     0 |         1 |  0.00 |
| EFUSE_USR   |    0 |     0 |         1 |  0.00 |
| FRAME_ECCE2 |    0 |     0 |         1 |  0.00 |
| ICAPE2      |    0 |     0 |         2 |  0.00 |
| STARTUPE2   |    0 |     0 |         1 |  0.00 |
| XADC        |    0 |     0 |         1 |  0.00 |
+-------------+------+-------+-----------+-------+


7. Primitives
-------------

+------------+-------+----------------------+
|  Ref Name  |  Used |  Functional Category |
+------------+-------+----------------------+
| FDRE       | 11213 |         Flop & Latch |
| LUT3       |  2529 |                  LUT |
| LUT6       |  2020 |                  LUT |
| LUT5       |  1188 |                  LUT |
| LUT4       |  1092 |                  LUT |
| RAMD32     |   804 |   Distributed Memory |
| LUT2       |   782 |                  LUT |
| LUT1       |   512 |                  LUT |
| FDSE       |   433 |         Flop & Latch |
| CARRY4     |   298 |           CarryLogic |
| RAMS32     |   268 |   Distributed Memory |
| SRL16E     |   241 |   Distributed Memory |
| MUXF7      |   236 |                MuxFx |
| BIBUF      |   130 |                   IO |
| SRLC32E    |   117 |   Distributed Memory |
| FDCE       |    67 |         Flop & Latch |
| OBUF       |    34 |                   IO |
| IBUF       |    19 |                   IO |
| MUXF8      |    11 |                MuxFx |
| RAMB36E1   |    10 |         Block Memory |
| FDPE       |     9 |         Flop & Latch |
| OBUFT      |     5 |                   IO |
| BUFG       |     2 |                Clock |
| PS7        |     1 | Specialized Resource |
| ODDR       |     1 |                   IO |
| MMCME2_ADV |     1 |                Clock |
| BUFR       |     1 |                Clock |
+------------+-------+----------------------+


8. Black Boxes
--------------

+-----------+------+
|  Ref Name | Used |
+-----------+------+
| clk_wiz_0 |    1 |
+-----------+------+


9. Instantiated Netlists
------------------------

+----------+------+
| Ref Name | Used |
+----------+------+


