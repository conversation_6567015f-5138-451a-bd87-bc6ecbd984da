<HTML><HEAD><TITLE>Device Usage Statistics Report</TITLE></HEAD>
<BODY TEXT='#000000' BGCOLOR='#FFFFFF' LINK='#0000EE' VLINK='#551A8B' ALINK='#FF0000'><H3>Device Usage Page (usage_statistics_webtalk.html)</H3>This HTML page displays the device usage statistics that will be sent to Xilinx.<BR>To see the actual file transmitted to Xilinx, please click <A HREF="./usage_statistics_webtalk.xml">here</A>.<BR><BR><HR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>software_version_and_target_device</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>beta</B></TD><TD>FALSE</TD>
  <TD BGCOLOR='#DBE5F1'><B>build_version</B></TD><TD>2405991</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>date_generated</B></TD><TD>Mon Aug 11 10:50:25 2025</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_platform</B></TD><TD>WIN64</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>product_version</B></TD><TD>Vivado v2018.3 (64-bit)</TD>
  <TD BGCOLOR='#DBE5F1'><B>project_id</B></TD><TD>c9d39f15029a4714b0cb9c76acd34d9a</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>project_iteration</B></TD><TD>60</TD>
  <TD BGCOLOR='#DBE5F1'><B>random_id</B></TD><TD>6f2e0274765f583a9fcfacd97777ebed</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>registration_id</B></TD><TD>135256_15690819_173552794_661</TD>
  <TD BGCOLOR='#DBE5F1'><B>route_design</B></TD><TD>TRUE</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_device</B></TD><TD>xc7z020</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_family</B></TD><TD>zynq</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>target_package</B></TD><TD>clg400</TD>
  <TD BGCOLOR='#DBE5F1'><B>target_speed</B></TD><TD>-2</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>tool_flow</B></TD><TD>Vivado</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>user_environment</B></TD></TR>
<TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>cpu_name</B></TD><TD>13th Gen Intel(R) Core(TM) i5-13500H</TD>
  <TD BGCOLOR='#DBE5F1'><B>cpu_speed</B></TD><TD>3187 MHz</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>os_name</B></TD><TD>Microsoft Windows 8 or later , 64-bit</TD>
  <TD BGCOLOR='#DBE5F1'><B>os_release</B></TD><TD>major release  (build 9200)</TD>
</TR><TR ALIGN='LEFT'>  <TD BGCOLOR='#DBE5F1'><B>system_ram</B></TD><TD>34.000 GB</TD>
  <TD BGCOLOR='#DBE5F1'><B>total_processors</B></TD><TD>1</TD>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='4'><B>vivado_usage</B></TD></TR>
<TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>gui_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>abstractcombinedpanel_remove_selected_elements=2</TD>
   <TD>abstractfileview_reload=11</TD>
   <TD>abstractsearchablepanel_show_search=2</TD>
   <TD>addilaprobespopup_ok=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>addrepositoryinfodialog_ok=3</TD>
   <TD>addrepositoryinfodialog_repository_tree=2</TD>
   <TD>addresstreetablepanel_address_tree_table=286</TD>
   <TD>addsrcwizard_specify_hdl_netlist_block_design=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>addsrcwizard_specify_or_create_constraint_files=2</TD>
   <TD>applyrsbmultiautomationdialog_checkbox_tree=25</TD>
   <TD>basedialog_cancel=262</TD>
   <TD>basedialog_close=5</TD>
</TR><TR ALIGN='LEFT'>   <TD>basedialog_ok=613</TD>
   <TD>basedialog_yes=49</TD>
   <TD>basedialogutils_open_in_new_tab=1</TD>
   <TD>basedialogutils_open_in_specified_layout=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>basereporttab_rerun=12</TD>
   <TD>checktimingresulttreetablepanel_check_timing_result_tree_table=10</TD>
   <TD>checktimingsectionpanel_check_timing_selection_table=2</TD>
   <TD>clkconfigmainpanel_tabbed_pane=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>clkconfigtreetablepanel_clk_config_tree_table=59</TD>
   <TD>clocknetworksreportview_clock_network_tree=20</TD>
   <TD>clocksummarytreetablepanel_clock_summary_tree_table=28</TD>
   <TD>closeplanner_cancel=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>closeplanner_yes=3</TD>
   <TD>cmdmsgdialog_copy_message=7</TD>
   <TD>cmdmsgdialog_messages=112</TD>
   <TD>cmdmsgdialog_ok=141</TD>
</TR><TR ALIGN='LEFT'>   <TD>cmdmsgdialog_open_messages_view=7</TD>
   <TD>confirmsavetexteditsdialog_no=2</TD>
   <TD>constraintschooserpanel_add_existing_or_create_new_constraints=3</TD>
   <TD>constraintschooserpanel_add_files_below_to_this_constraint_set=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>constraintschooserpanel_create_file=4</TD>
   <TD>constraintschooserpanel_file_table=18</TD>
   <TD>coreandinterfacesbasetreetablepanel_refresh_all_repositories=2</TD>
   <TD>coreandinterfacesbasetreetablepanel_refresh_repository=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>coretreetablepanel_add_ip_to_repository=1</TD>
   <TD>coretreetablepanel_core_tree_table=521</TD>
   <TD>coretreetablepanel_delete_ip=1</TD>
   <TD>createconstraintsfilepanel_file_name=5</TD>
</TR><TR ALIGN='LEFT'>   <TD>createnewdiagramdialog_design_name=5</TD>
   <TD>creatersbinterfacedialog_table=8</TD>
   <TD>creatersbportdialog_create_vector=7</TD>
   <TD>creatersbportdialog_direction=15</TD>
</TR><TR ALIGN='LEFT'>   <TD>creatersbportdialog_frequency=1</TD>
   <TD>creatersbportdialog_from=7</TD>
   <TD>creatersbportdialog_port_name=19</TD>
   <TD>creatersbportdialog_type=18</TD>
</TR><TR ALIGN='LEFT'>   <TD>customizecoredialog_documentation=1</TD>
   <TD>customizecoredialog_ip_location=2</TD>
   <TD>ddrconfigtreetablepanel_ddr_config_tree_table=15</TD>
   <TD>debugview_debug_cores_tree_table=12</TD>
</TR><TR ALIGN='LEFT'>   <TD>debugwizard_advanced_trigger=4</TD>
   <TD>debugwizard_capture_control=5</TD>
   <TD>debugwizard_chipscope_tree_table=7</TD>
   <TD>debugwizard_find_nets_to_add=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>debugwizard_netlist_view=1</TD>
   <TD>debugwizard_sample_of_data_depth=2</TD>
   <TD>designtimingsumsectionpanel_worst_hold_slack=1</TD>
   <TD>designtimingsumsectionpanel_worst_negative_slack=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>editcreateclocktablepanel_edit_create_clock_table=23</TD>
   <TD>editcreategeneratedclocktablepanel_edit_create_generated_clock_table=1</TD>
   <TD>editiodelaytablepanel_edit_io_delay_table=16</TD>
   <TD>existingconstraintslistpanel_copy_text_from_selected_constraints=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>expreporttreepanel_exp_report_tree_table=19</TD>
   <TD>expruntreepanel_exp_run_tree_table=44</TD>
   <TD>filesetpanel_file_set_panel_tree=798</TD>
   <TD>flownavigatortreepanel_flow_navigator_tree=365</TD>
</TR><TR ALIGN='LEFT'>   <TD>flownavigatortreepanel_open=2</TD>
   <TD>fpgachooser_category=2</TD>
   <TD>fpgachooser_family=2</TD>
   <TD>fpgachooser_fpga_table=7</TD>
</TR><TR ALIGN='LEFT'>   <TD>fpgachooser_package=1</TD>
   <TD>fpgachooser_speed=1</TD>
   <TD>fromtargetspecifierpanel_specify_start_points=3</TD>
   <TD>gensettingtreetablepanel_gen_setting_tree_table=81</TD>
</TR><TR ALIGN='LEFT'>   <TD>getobjectsdialog_find=12</TD>
   <TD>getobjectspanel_append=4</TD>
   <TD>getobjectspanel_set=2</TD>
   <TD>gettingstartedview_create_new_project=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>gettingstartedview_open_project=3</TD>
   <TD>gictreetablepanel_gic_tree_table=19</TD>
   <TD>graphicalview_zoom_fit=4</TD>
   <TD>graphicalview_zoom_out=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>hacgccheckbox_value_of_specified_parameter=1</TD>
   <TD>hacgccombobox_value_of_specified_parameter=16</TD>
   <TD>hacgccombobox_value_of_specified_parameter_manual=11</TD>
   <TD>hacgcipsymbol_show_disabled_ports=11</TD>
</TR><TR ALIGN='LEFT'>   <TD>hacgctabbedpane_tabbed_pane=1</TD>
   <TD>hardwaredashboardview_show_dashboard_options=1</TD>
   <TD>hardwareilawaveformview_run_trigger_for_this_ila_core=21</TD>
   <TD>hardwareilawaveformview_run_trigger_immediate_for_this_ila_core=9</TD>
</TR><TR ALIGN='LEFT'>   <TD>hardwareilawaveformview_stop_trigger_for_this_ila_core=5</TD>
   <TD>hardwareilawaveformview_toggle_auto_re_trigger_mode=12</TD>
   <TD>hardwaretreepanel_hardware_tree_table=51</TD>
   <TD>hardwareview_expand_next_level=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>hcodeeditor_blank_operations=1</TD>
   <TD>hcodeeditor_search_text_combo_box=14</TD>
   <TD>hduallist_find_results=21</TD>
   <TD>hduallist_move_selected_items_to_right=6</TD>
</TR><TR ALIGN='LEFT'>   <TD>hfolderchooserhelpers_up_one_level=4</TD>
   <TD>hinputhandler_toggle_block_comments=1</TD>
   <TD>hpopuptitle_close=8</TD>
   <TD>htable_set_eliding_for_table_cells=9</TD>
</TR><TR ALIGN='LEFT'>   <TD>ictelementsummarysectionpanel_hold=1</TD>
   <TD>ilaprobetablepanel_add_probe=4</TD>
   <TD>ilaprobetablepanel_add_probes=1</TD>
   <TD>ilaprobetablepanel_set_trigger_condition_to_global=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>instancemenu_floorplanning=2</TD>
   <TD>intraclockssectionpanel_intra_clocks_section_table=40</TD>
   <TD>iodelaycreationpanel_delay_value=5</TD>
   <TD>iodelaycreationpanel_delay_value_specifies=5</TD>
</TR><TR ALIGN='LEFT'>   <TD>iodelaycreationpanel_delay_value_specifies_rise_fall_delay=2</TD>
   <TD>iodelaycreationpanel_specify_clock_pin_or_port=3</TD>
   <TD>iodelaycreationpanel_specify_list_of_ports=3</TD>
   <TD>ipicomponentname_component_name=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>ipstatussectionpanel_upgrade_selected=13</TD>
   <TD>ipstatustablepanel_ip_status_table=31</TD>
   <TD>ipstatustablepanel_more_info=11</TD>
   <TD>labtoolsmenu_name=9</TD>
</TR><TR ALIGN='LEFT'>   <TD>languagetemplatesdialog_templates_tree=157</TD>
   <TD>logmonitor_monitor=5</TD>
   <TD>logpanel_log_navigator=4</TD>
   <TD>mainmenumgr_checkpoint=190</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_constraints=15</TD>
   <TD>mainmenumgr_edit=26</TD>
   <TD>mainmenumgr_export=263</TD>
   <TD>mainmenumgr_file=374</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_floorplanning=2</TD>
   <TD>mainmenumgr_flow=36</TD>
   <TD>mainmenumgr_help=2</TD>
   <TD>mainmenumgr_import=46</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_io=4</TD>
   <TD>mainmenumgr_io_planning=2</TD>
   <TD>mainmenumgr_ip=193</TD>
   <TD>mainmenumgr_open_block_design=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_project=191</TD>
   <TD>mainmenumgr_reports=42</TD>
   <TD>mainmenumgr_settings=3</TD>
   <TD>mainmenumgr_text_editor=195</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_timing=5</TD>
   <TD>mainmenumgr_tools=49</TD>
   <TD>mainmenumgr_unselect_type=1</TD>
   <TD>mainmenumgr_view=32</TD>
</TR><TR ALIGN='LEFT'>   <TD>mainmenumgr_window=60</TD>
   <TD>maintoolbarmgr_run=6</TD>
   <TD>mainwinmenumgr_layout=48</TD>
   <TD>mainwinmenumgr_load=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>messagewithoptiondialog_dont_show_this_dialog_again=1</TD>
   <TD>mioconfigtreetablepanel_mio_config_tree_table=89</TD>
   <TD>miotablepagepanel_mio_table=21</TD>
   <TD>msgtreepanel_discard_user_created_messages=10</TD>
</TR><TR ALIGN='LEFT'>   <TD>msgtreepanel_message_severity=24</TD>
   <TD>msgtreepanel_message_view_tree=477</TD>
   <TD>msgtreepanel_suppress_messages_with_this_id=1</TD>
   <TD>msgview_critical_warnings=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>navigabletimingreporttab_timing_report_navigation_tree=284</TD>
   <TD>netlisttreeview_netlist_tree=73</TD>
   <TD>newexporthardwaredialog_export_to=4</TD>
   <TD>newexporthardwaredialog_include_bitstream=34</TD>
</TR><TR ALIGN='LEFT'>   <TD>packagetreepanel_package_tree_panel=3</TD>
   <TD>pacommandnames_add_probes_to_waveform=2</TD>
   <TD>pacommandnames_add_sources=3</TD>
   <TD>pacommandnames_addresseditor_window=5</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_auto_assign_address=18</TD>
   <TD>pacommandnames_auto_connect_ports=18</TD>
   <TD>pacommandnames_auto_connect_target=64</TD>
   <TD>pacommandnames_auto_fit_selection=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_auto_update_hier=10</TD>
   <TD>pacommandnames_close_server=11</TD>
   <TD>pacommandnames_close_target=4</TD>
   <TD>pacommandnames_create_hardware_dashboards=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_create_top_hdl=7</TD>
   <TD>pacommandnames_disable_auto_retrigger=1</TD>
   <TD>pacommandnames_edit_constraint_sets=1</TD>
   <TD>pacommandnames_enable_auto_retrigger=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_exit=4</TD>
   <TD>pacommandnames_export_bd_tcl=1</TD>
   <TD>pacommandnames_export_hardware=57</TD>
   <TD>pacommandnames_export_ila_data=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_generate_composite_file=30</TD>
   <TD>pacommandnames_goto_implemented_design=2</TD>
   <TD>pacommandnames_ip_settings=2</TD>
   <TD>pacommandnames_language_templates=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_launch_hardware=101</TD>
   <TD>pacommandnames_log_window=1</TD>
   <TD>pacommandnames_make_connection=1</TD>
   <TD>pacommandnames_message_window=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_open_hardware_manager=2</TD>
   <TD>pacommandnames_open_target=3</TD>
   <TD>pacommandnames_open_target_wizard=2</TD>
   <TD>pacommandnames_package_pins_window=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_ports_window=2</TD>
   <TD>pacommandnames_program_fpga=2</TD>
   <TD>pacommandnames_refresh_server=1</TD>
   <TD>pacommandnames_regenerate_layout=11</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_reload_rtl_design=1</TD>
   <TD>pacommandnames_report_clock_networks=2</TD>
   <TD>pacommandnames_report_ip_status=1</TD>
   <TD>pacommandnames_reports_window=5</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_reset_composite_file=7</TD>
   <TD>pacommandnames_run_bitgen=27</TD>
   <TD>pacommandnames_run_implementation=3</TD>
   <TD>pacommandnames_run_synthesis=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_run_trigger=5</TD>
   <TD>pacommandnames_save_design=10</TD>
   <TD>pacommandnames_save_rsb_design=57</TD>
   <TD>pacommandnames_schematic=8</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_select_area=7</TD>
   <TD>pacommandnames_simulation_run=3</TD>
   <TD>pacommandnames_simulation_run_behavioral=1</TD>
   <TD>pacommandnames_stop_trigger=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_tcl_console_window=1</TD>
   <TD>pacommandnames_timing_results_window=2</TD>
   <TD>pacommandnames_trigger_immediate=4</TD>
   <TD>pacommandnames_unmap_segment=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>pacommandnames_validate_rsb_design=88</TD>
   <TD>pacommandnames_zoom_fit=1</TD>
   <TD>pacommandnames_zoom_in=1</TD>
   <TD>pacommandnames_zoom_out=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>pathmenu_set_false_path=3</TD>
   <TD>pathmenu_set_maximum_delay=2</TD>
   <TD>pathmenu_set_multicycle_path=2</TD>
   <TD>pathreporttableview_description=48</TD>
</TR><TR ALIGN='LEFT'>   <TD>pathreporttableview_floorplanning=5</TD>
   <TD>pathreporttableview_select=7</TD>
   <TD>paviews_address_editor=22</TD>
   <TD>paviews_code=61</TD>
</TR><TR ALIGN='LEFT'>   <TD>paviews_dashboard=4</TD>
   <TD>paviews_device=13</TD>
   <TD>paviews_ip_catalog=4</TD>
   <TD>paviews_path_table=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>paviews_project_summary=94</TD>
   <TD>paviews_schematic=8</TD>
   <TD>paviews_system=8</TD>
   <TD>paviews_timing_constraints=6</TD>
</TR><TR ALIGN='LEFT'>   <TD>planaheadtab_refresh_ip_catalog=15</TD>
   <TD>primaryclockspanel_recommended_constraints_table=9</TD>
   <TD>primitivesmenu_highlight_leaf_cells=2</TD>
   <TD>probesview_probes_tree=13</TD>
</TR><TR ALIGN='LEFT'>   <TD>programdebugtab_available_targets_on_server=1</TD>
   <TD>programdebugtab_open_recently_opened_target=1</TD>
   <TD>programdebugtab_open_target=30</TD>
   <TD>programdebugtab_program_device=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>programdebugtab_refresh_device=17</TD>
   <TD>programfpgadialog_program=2</TD>
   <TD>programfpgadialog_specify_bitstream_file=2</TD>
   <TD>progressdialog_cancel=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>projectnamechooser_choose_project_location=1</TD>
   <TD>projectnamechooser_project_name=1</TD>
   <TD>projectsummarypowerpanel_tabbed_pane=3</TD>
   <TD>projectsummarytimingpanel_open_timing_summary_report=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>projecttab_close_design=16</TD>
   <TD>projecttab_reload=1</TD>
   <TD>rdicommands_copy=1</TD>
   <TD>rdicommands_custom_commands=6</TD>
</TR><TR ALIGN='LEFT'>   <TD>rdicommands_delete=15</TD>
   <TD>rdicommands_properties=12</TD>
   <TD>rdicommands_redo=15</TD>
   <TD>rdicommands_save_file=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>rdicommands_settings=5</TD>
   <TD>rdiviews_waveform_viewer=202</TD>
   <TD>removesourcesdialog_also_delete=1</TD>
   <TD>reportclocknetworksdialog_result_name=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>reporttimingdialog_tabbed_pane=7</TD>
   <TD>reporttimingsummarydialog_report_timing_summary_dialog_tabbed=13</TD>
   <TD>rsbapplyautomationbar_run_block_automation=6</TD>
   <TD>rsbapplyautomationbar_run_connection_automation=31</TD>
</TR><TR ALIGN='LEFT'>   <TD>rsbexternalinterfaceproppanels_name=5</TD>
   <TD>rsbexternalportproppanels_name=12</TD>
   <TD>rungadget_show_error=1</TD>
   <TD>rungadget_show_error_and_critical_warning_messages=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>rungadget_show_warning_and_error_messages_in_messages=2</TD>
   <TD>saveprojectutils_cancel=2</TD>
   <TD>saveprojectutils_dont_save=1</TD>
   <TD>saveprojectutils_reload=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>saveprojectutils_save=9</TD>
   <TD>selectablelistpanel_selectable_list=22</TD>
   <TD>selectareadialog_select_all=2</TD>
   <TD>selectmenu_highlight=32</TD>
</TR><TR ALIGN='LEFT'>   <TD>selectmenu_mark=11</TD>
   <TD>settingsdialog_options_tree=1</TD>
   <TD>settingsdialog_project_tree=15</TD>
   <TD>settingsprojectgeneralpage_choose_device_for_your_project=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>settingsprojectiprepositorypage_add_repository=2</TD>
   <TD>settingsprojectiprepositorypage_refresh_all=2</TD>
   <TD>settingsprojectiprepositorypage_repository_chooser=5</TD>
   <TD>signaltreepanel_signal_tree_table=40</TD>
</TR><TR ALIGN='LEFT'>   <TD>simpleoutputproductdialog_close_dialog_unsaved_changes_will=2</TD>
   <TD>simpleoutputproductdialog_generate_output_products_immediately=58</TD>
   <TD>simpleoutputproductdialog_output_product_tree=2</TD>
   <TD>simpleoutputproductdialog_reset_output_products=7</TD>
</TR><TR ALIGN='LEFT'>   <TD>simpleoutputproductdialog_synthesize_design_globally=2</TD>
   <TD>smartconnect_show_advanced_properties=1</TD>
   <TD>srcchooserpanel_add_hdl_and_netlist_files_to_your_project=1</TD>
   <TD>srcchooserpanel_create_file=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>srcchooserpanel_make_local_copy_of_these_files_into=1</TD>
   <TD>srcmenu_ip_hierarchy=10</TD>
   <TD>stalerundialog_no=2</TD>
   <TD>stalerundialog_run_synthesis=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>statemonitor_reset_run=1</TD>
   <TD>syntheticagettingstartedview_recent_projects=60</TD>
   <TD>syntheticastatemonitor_cancel=5</TD>
   <TD>systembuildermenu_assign_address=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>systembuildermenu_create_comment=1</TD>
   <TD>systembuildermenu_create_interface_port=2</TD>
   <TD>systembuildermenu_create_port=19</TD>
   <TD>systembuildermenu_end_connection_mode=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>systembuildermenu_ip_documentation=18</TD>
   <TD>systembuilderview_add_ip=7</TD>
   <TD>systembuilderview_expand_collapse=86</TD>
   <TD>systembuilderview_orientation=25</TD>
</TR><TR ALIGN='LEFT'>   <TD>systembuilderview_pin_blocks_and_ports_to_location=1</TD>
   <TD>systembuilderview_pinning=118</TD>
   <TD>systembuilderview_search=3</TD>
   <TD>systemtab_blocks=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>systemtab_report_ip_status=5</TD>
   <TD>systemtab_show_ip_status=3</TD>
   <TD>systemtab_upgrade_later=2</TD>
   <TD>systemtreeview_system_tree=38</TD>
</TR><TR ALIGN='LEFT'>   <TD>targetchooserpanel_add_xilinx_virtual_cable_as_hardware=1</TD>
   <TD>taskbanner_close=32</TD>
   <TD>tclconsoleview_clear_all_output=1</TD>
   <TD>tclconsoleview_copy=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>tclconsoleview_tcl_console_code_editor=95</TD>
   <TD>tclfinddialog_specify_of_objects_option=1</TD>
   <TD>tclobjecttreetable_treetable=18</TD>
   <TD>timinggettingstartedpanel_report_timing=3</TD>
</TR><TR ALIGN='LEFT'>   <TD>timinggettingstartedpanel_report_timing_summary=2</TD>
   <TD>timingitemflattablepanel_floorplanning=1</TD>
   <TD>timingitemflattablepanel_table=59</TD>
   <TD>timingitemflattablepanel_view_path_report=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>timingitemtreetablepanel_timing_item_tree_table=18</TD>
   <TD>touchpointsurveydialog_no=2</TD>
   <TD>triggersetuppanel_table=73</TD>
   <TD>triggerstatuspanel_run_trigger_for_this_ila_core=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>viotreetablepanel_vio_tree_table=3</TD>
   <TD>waveformnametree_waveform_name_tree=66</TD>
   <TD>waveformview_add=2</TD>
   <TD>xdccategorytree_xdc_category_tree=38</TD>
</TR><TR ALIGN='LEFT'>   <TD>xdcviewertreetablepanel_xdc_viewer_tree_table=24</TD>
   <TD>xpg_combobox_value_of_specified_parameter=1</TD>
   <TD>xpg_combobox_value_of_specified_parameter_manual=1</TD>
   <TD>xpg_ipsymbol_show_disabled_ports=2</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>java_command_handlers</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>addprobestowaveform=2</TD>
   <TD>addsources=3</TD>
   <TD>autoassignaddress=7</TD>
   <TD>autoconnectport=18</TD>
</TR><TR ALIGN='LEFT'>   <TD>autoconnecttarget=63</TD>
   <TD>closeserver=11</TD>
   <TD>closetarget=4</TD>
   <TD>coreview=50</TD>
</TR><TR ALIGN='LEFT'>   <TD>createblockdesign=1</TD>
   <TD>createtophdl=6</TD>
   <TD>customizecore=43</TD>
   <TD>customizersbblock=233</TD>
</TR><TR ALIGN='LEFT'>   <TD>debugwizardcmdhandler=4</TD>
   <TD>disableautoretrigger=1</TD>
   <TD>editconstraintsets=1</TD>
   <TD>editcopy=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>editdelete=112</TD>
   <TD>editpaste=6</TD>
   <TD>editproperties=12</TD>
   <TD>editredo=14</TD>
</TR><TR ALIGN='LEFT'>   <TD>editundo=49</TD>
   <TD>enableautoretrigger=1</TD>
   <TD>exportiladata=1</TD>
   <TD>fileexit=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>generateoutputforbdfile=1</TD>
   <TD>launchopentarget=2</TD>
   <TD>launchprogramfpga=3</TD>
   <TD>makersbconnection=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>managecompositetargets=38</TD>
   <TD>newexporthardware=58</TD>
   <TD>newlaunchhardware=99</TD>
   <TD>newproject=2</TD>
</TR><TR ALIGN='LEFT'>   <TD>openaddresseditor=5</TD>
   <TD>openblockdesign=1</TD>
   <TD>openhardwaredashboard=1</TD>
   <TD>openhardwaremanager=30</TD>
</TR><TR ALIGN='LEFT'>   <TD>openproject=3</TD>
   <TD>openrecenttarget=2</TD>
   <TD>opentarget=3</TD>
   <TD>recustomizecore=14</TD>
</TR><TR ALIGN='LEFT'>   <TD>refreshdevice=15</TD>
   <TD>refreshserver=1</TD>
   <TD>regeneratersblayout=11</TD>
   <TD>reloaddesign=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>reportclockinteraction=1</TD>
   <TD>reportclocknetworks=1</TD>
   <TD>reportipstatus=6</TD>
   <TD>reportmethodology=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>reporttimingsummary=10</TD>
   <TD>runbitgen=70</TD>
   <TD>runimplementation=47</TD>
   <TD>runschematic=9</TD>
</TR><TR ALIGN='LEFT'>   <TD>runsynthesis=41</TD>
   <TD>runtrigger=31</TD>
   <TD>runtriggerimmediate=13</TD>
   <TD>savedesign=10</TD>
</TR><TR ALIGN='LEFT'>   <TD>savefileproxyhandler=1</TD>
   <TD>saversbdesign=90</TD>
   <TD>showsource=3</TD>
   <TD>showview=64</TD>
</TR><TR ALIGN='LEFT'>   <TD>simulationrun=1</TD>
   <TD>stoptrigger=17</TD>
   <TD>timingconstraintswizard=5</TD>
   <TD>toggleautofitselection=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>toggleselectareamode=7</TD>
   <TD>toolssettings=8</TD>
   <TD>toolstemplates=7</TD>
   <TD>unmapaddresssegment=4</TD>
</TR><TR ALIGN='LEFT'>   <TD>upgradeip=13</TD>
   <TD>validatersbdesign=83</TD>
   <TD>viewlayoutcmd=3</TD>
   <TD>viewtaskimplementation=11</TD>
</TR><TR ALIGN='LEFT'>   <TD>viewtaskrtlanalysis=2</TD>
   <TD>viewtasksynthesis=3</TD>
   <TD>xdcsetinputdelay=5</TD>
   <TD>zoomfit=1</TD>
</TR><TR ALIGN='LEFT'>   <TD>zoomin=1</TD>
   <TD>zoomout=1</TD>
</TR>  </TABLE>
</TR><TR ALIGN='LEFT'>  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>other_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>guimode=97</TD>
</TR>  </TABLE>
  <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
   <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>project_data</B></TD></TR>
<TR ALIGN='LEFT'>   <TD>constraintsetcount=2</TD>
   <TD>core_container=false</TD>
   <TD>currentimplrun=impl_1</TD>
   <TD>currentsynthesisrun=synth_1</TD>
</TR><TR ALIGN='LEFT'>   <TD>default_library=xil_defaultlib</TD>
   <TD>designmode=RTL</TD>
   <TD>export_simulation_activehdl=53</TD>
   <TD>export_simulation_ies=53</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_modelsim=53</TD>
   <TD>export_simulation_questa=53</TD>
   <TD>export_simulation_riviera=53</TD>
   <TD>export_simulation_vcs=53</TD>
</TR><TR ALIGN='LEFT'>   <TD>export_simulation_xsim=53</TD>
   <TD>implstrategy=Vivado Implementation Defaults</TD>
   <TD>launch_simulation_activehdl=0</TD>
   <TD>launch_simulation_ies=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_modelsim=0</TD>
   <TD>launch_simulation_questa=0</TD>
   <TD>launch_simulation_riviera=0</TD>
   <TD>launch_simulation_vcs=0</TD>
</TR><TR ALIGN='LEFT'>   <TD>launch_simulation_xsim=1</TD>
   <TD>simulator_language=Mixed</TD>
   <TD>srcsetcount=3</TD>
   <TD>synthesisstrategy=Vivado Synthesis Defaults</TD>
</TR><TR ALIGN='LEFT'>   <TD>target_language=Verilog</TD>
   <TD>target_simulator=XSim</TD>
   <TD>totalimplruns=3</TD>
   <TD>totalsynthesisruns=3</TD>
</TR>  </TABLE>
</TR> </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>unisim_transformation</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>post_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf=130</TD>
    <TD>bufg=5</TD>
    <TD>bufr=1</TD>
    <TD>carry4=298</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdce=67</TD>
    <TD>fdpe=9</TD>
    <TD>fdre=11213</TD>
    <TD>fdse=433</TD>
</TR><TR ALIGN='LEFT'>    <TD>gnd=300</TD>
    <TD>ibuf=20</TD>
    <TD>lut1=512</TD>
    <TD>lut2=782</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut3=2529</TD>
    <TD>lut4=1092</TD>
    <TD>lut5=1188</TD>
    <TD>lut6=2020</TD>
</TR><TR ALIGN='LEFT'>    <TD>mmcme2_adv=1</TD>
    <TD>muxf7=236</TD>
    <TD>muxf8=11</TD>
    <TD>obuf=34</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuft=5</TD>
    <TD>oddr=1</TD>
    <TD>plle2_adv=1</TD>
    <TD>ps7=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36e1=10</TD>
    <TD>ramd32=804</TD>
    <TD>rams32=268</TD>
    <TD>srl16e=241</TD>
</TR><TR ALIGN='LEFT'>    <TD>srlc32e=117</TD>
    <TD>vcc=292</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>pre_unisim_transformation</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf=130</TD>
    <TD>bufg=5</TD>
    <TD>bufr=1</TD>
    <TD>carry4=298</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdce=67</TD>
    <TD>fdpe=9</TD>
    <TD>fdre=11213</TD>
    <TD>fdse=433</TD>
</TR><TR ALIGN='LEFT'>    <TD>gnd=300</TD>
    <TD>ibuf=15</TD>
    <TD>iobuf=5</TD>
    <TD>lut1=512</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut2=782</TD>
    <TD>lut3=2529</TD>
    <TD>lut4=1092</TD>
    <TD>lut5=1188</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut6=2020</TD>
    <TD>mmcme2_adv=1</TD>
    <TD>muxf7=236</TD>
    <TD>muxf8=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf=34</TD>
    <TD>oddr=1</TD>
    <TD>plle2_adv=1</TD>
    <TD>ps7=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>ram32m=134</TD>
    <TD>ramb36e1=10</TD>
    <TD>srl16e=241</TD>
    <TD>srlc32e=117</TD>
</TR><TR ALIGN='LEFT'>    <TD>vcc=292</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>power_opt_design</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options_spo</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-cell_types=default::all</TD>
    <TD>-clocks=default::[not_specified]</TD>
    <TD>-exclude_cells=default::[not_specified]</TD>
    <TD>-include_cells=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bram_ports_augmented=3</TD>
    <TD>bram_ports_newly_gated=0</TD>
    <TD>bram_ports_total=20</TD>
    <TD>flow_state=default</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_augmented=0</TD>
    <TD>slice_registers_newly_gated=0</TD>
    <TD>slice_registers_total=9542</TD>
    <TD>srls_augmented=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>srls_newly_gated=0</TD>
    <TD>srls_total=260</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>ip_statistics</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>IP_Integrator/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bdsource=SBD</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>maxhierdepth=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>numblks=14</TD>
    <TD>numhdlrefblks=0</TD>
    <TD>numhierblks=4</TD>
    <TD>numhlsblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numnonxlnxblks=0</TD>
    <TD>numpkgbdblks=0</TD>
    <TD>numreposblks=10</TD>
    <TD>numsysgenblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>synth_mode=Global</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=BlockDiagram</TD>
    <TD>x_ipname=bd_64d3</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.00.a</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>IP_Integrator/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bdsource=SBD</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>maxhierdepth=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>numblks=17</TD>
    <TD>numhdlrefblks=0</TD>
    <TD>numhierblks=4</TD>
    <TD>numhlsblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numnonxlnxblks=0</TD>
    <TD>numpkgbdblks=0</TD>
    <TD>numreposblks=13</TD>
    <TD>numsysgenblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>synth_mode=Global</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=BlockDiagram</TD>
    <TD>x_ipname=bd_a552</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.00.a</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>IP_Integrator/3</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bdsource=USER</TD>
    <TD>core_container=NA</TD>
    <TD>da_axi4_cnt=22</TD>
    <TD>da_board_cnt=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>da_clkrst_cnt=25</TD>
    <TD>da_ps7_cnt=1</TD>
    <TD>iptotal=1</TD>
    <TD>maxhierdepth=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numblks=23</TD>
    <TD>numhdlrefblks=0</TD>
    <TD>numhierblks=7</TD>
    <TD>numhlsblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>numnonxlnxblks=3</TD>
    <TD>numpkgbdblks=0</TD>
    <TD>numreposblks=16</TD>
    <TD>numsysgenblks=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>synth_mode=Global</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=BlockDiagram</TD>
    <TD>x_ipname=utg</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.00.a</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_crossbar_v2_1_19_axi_crossbar/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=32</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=1</TD>
    <TD>c_axi_protocol=2</TD>
    <TD>c_axi_ruser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_connectivity_mode=0</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_addr_width=0x0000001000000010000000100000001000000010</TD>
    <TD>c_m_axi_base_addr=0x0000000043c300000000000043c200000000000043c100000000000043c000000000000043000000</TD>
    <TD>c_m_axi_read_connectivity=0x0000000100000001000000010000000100000001</TD>
    <TD>c_m_axi_read_issuing=0x0000000100000001000000010000000100000001</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_secure=0x0000000000000000000000000000000000000000</TD>
    <TD>c_m_axi_write_connectivity=0x0000000100000001000000010000000100000001</TD>
    <TD>c_m_axi_write_issuing=0x0000000100000001000000010000000100000001</TD>
    <TD>c_num_addr_ranges=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_master_slots=5</TD>
    <TD>c_num_slave_slots=1</TD>
    <TD>c_r_register=1</TD>
    <TD>c_s_axi_arb_priority=0x00000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_base_id=0x00000000</TD>
    <TD>c_s_axi_read_acceptance=0x00000001</TD>
    <TD>c_s_axi_single_thread=0x00000001</TD>
    <TD>c_s_axi_thread_id_width=0x00000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_write_acceptance=0x00000001</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=19</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_crossbar</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_protocol_converter_v2_1_18_axi_protocol_converter/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_axi_addr_width=32</TD>
    <TD>c_axi_aruser_width=1</TD>
    <TD>c_axi_awuser_width=1</TD>
    <TD>c_axi_buser_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_data_width=32</TD>
    <TD>c_axi_id_width=12</TD>
    <TD>c_axi_ruser_width=1</TD>
    <TD>c_axi_supports_read=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_axi_supports_user_signals=0</TD>
    <TD>c_axi_supports_write=1</TD>
    <TD>c_axi_wuser_width=1</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_ignore_id=0</TD>
    <TD>c_m_axi_protocol=2</TD>
    <TD>c_s_axi_protocol=1</TD>
    <TD>c_translation_mode=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=18</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_protocol_converter</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>axi_vdma/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_dlytmr_resolution=125</TD>
    <TD>c_dynamic_resolution=1</TD>
    <TD>c_enable_debug_all=0</TD>
    <TD>c_enable_debug_info_0=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_enable_debug_info_1=0</TD>
    <TD>c_enable_debug_info_10=0</TD>
    <TD>c_enable_debug_info_11=0</TD>
    <TD>c_enable_debug_info_12=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_enable_debug_info_13=0</TD>
    <TD>c_enable_debug_info_14=1</TD>
    <TD>c_enable_debug_info_15=1</TD>
    <TD>c_enable_debug_info_2=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_enable_debug_info_3=0</TD>
    <TD>c_enable_debug_info_4=0</TD>
    <TD>c_enable_debug_info_5=0</TD>
    <TD>c_enable_debug_info_6=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_enable_debug_info_7=1</TD>
    <TD>c_enable_debug_info_8=0</TD>
    <TD>c_enable_debug_info_9=0</TD>
    <TD>c_enable_vert_flip=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_enable_vidprmtr_reads=1</TD>
    <TD>c_family=zynq</TD>
    <TD>c_flush_on_fsync=1</TD>
    <TD>c_include_internal_genlock=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_include_mm2s=1</TD>
    <TD>c_include_mm2s_dre=0</TD>
    <TD>c_include_mm2s_sf=0</TD>
    <TD>c_include_s2mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_include_s2mm_dre=0</TD>
    <TD>c_include_s2mm_sf=1</TD>
    <TD>c_include_sg=0</TD>
    <TD>c_instance=axi_vdma</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_mm2s_addr_width=32</TD>
    <TD>c_m_axi_mm2s_data_width=64</TD>
    <TD>c_m_axi_s2mm_addr_width=32</TD>
    <TD>c_m_axi_s2mm_data_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_sg_addr_width=32</TD>
    <TD>c_m_axi_sg_data_width=32</TD>
    <TD>c_m_axis_mm2s_tdata_width=32</TD>
    <TD>c_m_axis_mm2s_tuser_bits=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_mm2s_genlock_mode=3</TD>
    <TD>c_mm2s_genlock_num_masters=1</TD>
    <TD>c_mm2s_genlock_repeat_en=0</TD>
    <TD>c_mm2s_linebuffer_depth=2048</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_mm2s_linebuffer_thresh=4</TD>
    <TD>c_mm2s_max_burst_length=64</TD>
    <TD>c_mm2s_sof_enable=1</TD>
    <TD>c_num_fstores=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_prmry_is_aclk_async=0</TD>
    <TD>c_s2mm_genlock_mode=0</TD>
    <TD>c_s2mm_genlock_num_masters=1</TD>
    <TD>c_s2mm_genlock_repeat_en=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s2mm_linebuffer_depth=512</TD>
    <TD>c_s2mm_linebuffer_thresh=4</TD>
    <TD>c_s2mm_max_burst_length=8</TD>
    <TD>c_s2mm_sof_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_lite_addr_width=9</TD>
    <TD>c_s_axi_lite_data_width=32</TD>
    <TD>c_s_axis_s2mm_tdata_width=32</TD>
    <TD>c_s_axis_s2mm_tuser_bits=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_select_xpm=0</TD>
    <TD>c_use_fsync=1</TD>
    <TD>c_use_mm2s_fsync=0</TD>
    <TD>c_use_s2mm_fsync=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=6</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=axi_vdma</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=6.3</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>bd_64d3/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>advanced_properties=0</TD>
    <TD>component_name=utg_axi_smc_2</TD>
    <TD>core_container=NA</TD>
    <TD>has_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>num_clks=1</TD>
    <TD>num_mi=1</TD>
    <TD>num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=smartconnect</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>bd_a552/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>advanced_properties=0</TD>
    <TD>component_name=utg_axi_smc_0</TD>
    <TD>core_container=NA</TD>
    <TD>has_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>num_clks=1</TD>
    <TD>num_mi=1</TD>
    <TD>num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=smartconnect</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clk_wiz_v6_0_2_0_0/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>clkin1_period=20.000</TD>
    <TD>clkin2_period=10.0</TD>
    <TD>clock_mgr_type=NA</TD>
    <TD>component_name=clk_wiz_0</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>enable_axi=0</TD>
    <TD>feedback_source=FDBK_AUTO</TD>
    <TD>feedback_type=SINGLE</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>manual_override=false</TD>
    <TD>num_out_clk=2</TD>
    <TD>primitive=PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_dyn_phase_shift=false</TD>
    <TD>use_dyn_reconfig=false</TD>
    <TD>use_inclk_stopped=false</TD>
    <TD>use_inclk_switchover=false</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_locked=true</TD>
    <TD>use_max_i_jitter=false</TD>
    <TD>use_min_o_jitter=false</TD>
    <TD>use_phase_alignment=true</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_power_down=false</TD>
    <TD>use_reset=true</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>proc_sys_reset/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aux_reset_high=0</TD>
    <TD>c_aux_rst_width=4</TD>
    <TD>c_ext_reset_high=0</TD>
    <TD>c_ext_rst_width=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=zynq</TD>
    <TD>c_num_bus_rst=1</TD>
    <TD>c_num_interconnect_aresetn=1</TD>
    <TD>c_num_perp_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_perp_rst=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=13</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=proc_sys_reset</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=5.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>proc_sys_reset/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aux_reset_high=0</TD>
    <TD>c_aux_rst_width=4</TD>
    <TD>c_ext_reset_high=0</TD>
    <TD>c_ext_rst_width=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_family=zynq</TD>
    <TD>c_num_bus_rst=1</TD>
    <TD>c_num_interconnect_aresetn=1</TD>
    <TD>c_num_perp_aresetn=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_perp_rst=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=13</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=proc_sys_reset</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=5.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>processing_system7_v5.5_user_configuration/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>pcw_apu_clk_ratio_enable=6:2:1</TD>
    <TD>pcw_apu_peripheral_freqmhz=666.666666</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_armpll_ctrl_fbdiv=40</TD>
    <TD>pcw_can0_grp_clk_enable=0</TD>
    <TD>pcw_can0_peripheral_clksrc=External</TD>
    <TD>pcw_can0_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_can0_peripheral_freqmhz=-1</TD>
    <TD>pcw_can1_grp_clk_enable=0</TD>
    <TD>pcw_can1_peripheral_clksrc=External</TD>
    <TD>pcw_can1_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_can1_peripheral_freqmhz=-1</TD>
    <TD>pcw_can_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_can_peripheral_freqmhz=100</TD>
    <TD>pcw_cpu_cpu_pll_freqmhz=1333.333</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_cpu_peripheral_clksrc=ARM PLL</TD>
    <TD>pcw_crystal_peripheral_freqmhz=33.333333</TD>
    <TD>pcw_dci_peripheral_clksrc=DDR PLL</TD>
    <TD>pcw_dci_peripheral_freqmhz=10.159</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_ddr_pll_freqmhz=1066.667</TD>
    <TD>pcw_ddr_hpr_to_critical_priority_level=15</TD>
    <TD>pcw_ddr_hprlpr_queue_partition=HPR(0)/LPR(32)</TD>
    <TD>pcw_ddr_lpr_to_critical_priority_level=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_peripheral_clksrc=DDR PLL</TD>
    <TD>pcw_ddr_port0_hpr_enable=0</TD>
    <TD>pcw_ddr_port1_hpr_enable=0</TD>
    <TD>pcw_ddr_port2_hpr_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ddr_port3_hpr_enable=0</TD>
    <TD>pcw_ddr_write_to_critical_priority_level=2</TD>
    <TD>pcw_ddrpll_ctrl_fbdiv=32</TD>
    <TD>pcw_enet0_grp_mdio_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet0_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_enet0_peripheral_enable=0</TD>
    <TD>pcw_enet0_peripheral_freqmhz=1000 Mbps</TD>
    <TD>pcw_enet0_reset_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet1_grp_mdio_enable=0</TD>
    <TD>pcw_enet1_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_enet1_peripheral_enable=0</TD>
    <TD>pcw_enet1_peripheral_freqmhz=1000 Mbps</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_enet1_reset_enable=0</TD>
    <TD>pcw_enet_reset_polarity=Active Low</TD>
    <TD>pcw_fclk0_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fclk1_peripheral_clksrc=IO PLL</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fclk2_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fclk3_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_fpga0_peripheral_freqmhz=100</TD>
    <TD>pcw_fpga1_peripheral_freqmhz=150</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fpga2_peripheral_freqmhz=50</TD>
    <TD>pcw_fpga3_peripheral_freqmhz=50</TD>
    <TD>pcw_fpga_fclk0_enable=1</TD>
    <TD>pcw_fpga_fclk1_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_fpga_fclk2_enable=0</TD>
    <TD>pcw_fpga_fclk3_enable=0</TD>
    <TD>pcw_gpio_emio_gpio_enable=1</TD>
    <TD>pcw_gpio_emio_gpio_io=3</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_gpio_mio_gpio_enable=0</TD>
    <TD>pcw_gpio_peripheral_enable=0</TD>
    <TD>pcw_i2c0_grp_int_enable=1</TD>
    <TD>pcw_i2c0_grp_int_io=EMIO</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_i2c0_i2c0_io=EMIO</TD>
    <TD>pcw_i2c0_peripheral_enable=1</TD>
    <TD>pcw_i2c0_reset_enable=0</TD>
    <TD>pcw_i2c1_grp_int_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_i2c1_peripheral_enable=0</TD>
    <TD>pcw_i2c1_reset_enable=0</TD>
    <TD>pcw_i2c_reset_polarity=Active Low</TD>
    <TD>pcw_io_io_pll_freqmhz=1800.000</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_iopll_ctrl_fbdiv=54</TD>
    <TD>pcw_irq_f2p_mode=DIRECT</TD>
    <TD>pcw_m_axi_gp0_freqmhz=100</TD>
    <TD>pcw_m_axi_gp1_freqmhz=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nand_cycles_t_ar=1</TD>
    <TD>pcw_nand_cycles_t_clr=1</TD>
    <TD>pcw_nand_cycles_t_rc=11</TD>
    <TD>pcw_nand_cycles_t_rea=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nand_cycles_t_rr=1</TD>
    <TD>pcw_nand_cycles_t_wc=11</TD>
    <TD>pcw_nand_cycles_t_wp=1</TD>
    <TD>pcw_nand_grp_d8_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nand_peripheral_enable=0</TD>
    <TD>pcw_nor_cs0_t_ceoe=1</TD>
    <TD>pcw_nor_cs0_t_pc=1</TD>
    <TD>pcw_nor_cs0_t_rc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs0_t_tr=1</TD>
    <TD>pcw_nor_cs0_t_wc=11</TD>
    <TD>pcw_nor_cs0_t_wp=1</TD>
    <TD>pcw_nor_cs0_we_time=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs1_t_ceoe=1</TD>
    <TD>pcw_nor_cs1_t_pc=1</TD>
    <TD>pcw_nor_cs1_t_rc=11</TD>
    <TD>pcw_nor_cs1_t_tr=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_cs1_t_wc=11</TD>
    <TD>pcw_nor_cs1_t_wp=1</TD>
    <TD>pcw_nor_cs1_we_time=0</TD>
    <TD>pcw_nor_grp_a25_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_grp_cs0_enable=0</TD>
    <TD>pcw_nor_grp_cs1_enable=0</TD>
    <TD>pcw_nor_grp_sram_cs0_enable=0</TD>
    <TD>pcw_nor_grp_sram_cs1_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_grp_sram_int_enable=0</TD>
    <TD>pcw_nor_peripheral_enable=0</TD>
    <TD>pcw_nor_sram_cs0_t_ceoe=1</TD>
    <TD>pcw_nor_sram_cs0_t_pc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs0_t_rc=11</TD>
    <TD>pcw_nor_sram_cs0_t_tr=1</TD>
    <TD>pcw_nor_sram_cs0_t_wc=11</TD>
    <TD>pcw_nor_sram_cs0_t_wp=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs0_we_time=0</TD>
    <TD>pcw_nor_sram_cs1_t_ceoe=1</TD>
    <TD>pcw_nor_sram_cs1_t_pc=1</TD>
    <TD>pcw_nor_sram_cs1_t_rc=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_nor_sram_cs1_t_tr=1</TD>
    <TD>pcw_nor_sram_cs1_t_wc=11</TD>
    <TD>pcw_nor_sram_cs1_t_wp=1</TD>
    <TD>pcw_nor_sram_cs1_we_time=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_override_basic_clock=0</TD>
    <TD>pcw_pcap_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_pcap_peripheral_freqmhz=200</TD>
    <TD>pcw_pjtag_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_preset_bank0_voltage=LVCMOS 3.3V</TD>
    <TD>pcw_preset_bank1_voltage=LVCMOS 1.8V</TD>
    <TD>pcw_qspi_grp_fbclk_enable=0</TD>
    <TD>pcw_qspi_grp_io1_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_grp_single_ss_enable=1</TD>
    <TD>pcw_qspi_grp_single_ss_io=MIO 1 .. 6</TD>
    <TD>pcw_qspi_grp_ss1_enable=0</TD>
    <TD>pcw_qspi_internal_highaddress=0xFCFFFFFF</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_qspi_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_qspi_peripheral_enable=1</TD>
    <TD>pcw_qspi_peripheral_freqmhz=200</TD>
    <TD>pcw_qspi_qspi_io=MIO 1 .. 6</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_acp_freqmhz=10</TD>
    <TD>pcw_s_axi_gp0_freqmhz=10</TD>
    <TD>pcw_s_axi_gp1_freqmhz=10</TD>
    <TD>pcw_s_axi_hp0_data_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_hp0_freqmhz=100</TD>
    <TD>pcw_s_axi_hp1_data_width=64</TD>
    <TD>pcw_s_axi_hp1_freqmhz=100</TD>
    <TD>pcw_s_axi_hp2_data_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_s_axi_hp2_freqmhz=10</TD>
    <TD>pcw_s_axi_hp3_data_width=64</TD>
    <TD>pcw_s_axi_hp3_freqmhz=10</TD>
    <TD>pcw_sd0_grp_cd_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sd0_grp_cd_io=MIO 10</TD>
    <TD>pcw_sd0_grp_pow_enable=0</TD>
    <TD>pcw_sd0_grp_wp_enable=0</TD>
    <TD>pcw_sd0_peripheral_enable=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sd0_sd0_io=MIO 40 .. 45</TD>
    <TD>pcw_sd1_grp_cd_enable=0</TD>
    <TD>pcw_sd1_grp_pow_enable=0</TD>
    <TD>pcw_sd1_grp_wp_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_sd1_peripheral_enable=0</TD>
    <TD>pcw_sdio_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_sdio_peripheral_freqmhz=100</TD>
    <TD>pcw_single_qspi_data_mode=x4</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_smc_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_smc_peripheral_freqmhz=100</TD>
    <TD>pcw_spi0_grp_ss0_enable=0</TD>
    <TD>pcw_spi0_grp_ss1_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi0_grp_ss2_enable=0</TD>
    <TD>pcw_spi0_peripheral_enable=0</TD>
    <TD>pcw_spi1_grp_ss0_enable=0</TD>
    <TD>pcw_spi1_grp_ss1_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_spi1_grp_ss2_enable=0</TD>
    <TD>pcw_spi1_peripheral_enable=0</TD>
    <TD>pcw_spi_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_spi_peripheral_freqmhz=166.666666</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_tpiu_peripheral_clksrc=External</TD>
    <TD>pcw_tpiu_peripheral_freqmhz=200</TD>
    <TD>pcw_trace_grp_16bit_enable=0</TD>
    <TD>pcw_trace_grp_2bit_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_trace_grp_32bit_enable=0</TD>
    <TD>pcw_trace_grp_4bit_enable=0</TD>
    <TD>pcw_trace_grp_8bit_enable=0</TD>
    <TD>pcw_trace_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc0_clk0_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc0_clk0_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc0_clk1_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc0_clk1_peripheral_freqmhz=133.333333</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc0_clk2_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc0_clk2_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc0_peripheral_enable=0</TD>
    <TD>pcw_ttc1_clk0_peripheral_clksrc=CPU_1X</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc1_clk0_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc1_clk1_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_ttc1_clk1_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc1_clk2_peripheral_clksrc=CPU_1X</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_ttc1_clk2_peripheral_freqmhz=133.333333</TD>
    <TD>pcw_ttc1_peripheral_enable=0</TD>
    <TD>pcw_ttc_peripheral_freqmhz=50</TD>
    <TD>pcw_uart0_baud_rate=115200</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uart0_grp_full_enable=0</TD>
    <TD>pcw_uart0_peripheral_enable=1</TD>
    <TD>pcw_uart0_uart0_io=MIO 14 .. 15</TD>
    <TD>pcw_uart1_baud_rate=115200</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uart1_grp_full_enable=0</TD>
    <TD>pcw_uart1_peripheral_enable=0</TD>
    <TD>pcw_uart_peripheral_clksrc=IO PLL</TD>
    <TD>pcw_uart_peripheral_freqmhz=100</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_adv_enable=0</TD>
    <TD>pcw_uiparam_ddr_al=0</TD>
    <TD>pcw_uiparam_ddr_bank_addr_count=3</TD>
    <TD>pcw_uiparam_ddr_bl=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_board_delay0=0.25</TD>
    <TD>pcw_uiparam_ddr_board_delay1=0.25</TD>
    <TD>pcw_uiparam_ddr_board_delay2=0.25</TD>
    <TD>pcw_uiparam_ddr_board_delay3=0.25</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_bus_width=32 Bit</TD>
    <TD>pcw_uiparam_ddr_cl=7</TD>
    <TD>pcw_uiparam_ddr_clock_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_0_package_length=54.563</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_0_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_1_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_1_package_length=54.563</TD>
    <TD>pcw_uiparam_ddr_clock_1_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_2_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_clock_2_package_length=54.563</TD>
    <TD>pcw_uiparam_ddr_clock_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_3_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_clock_3_package_length=54.563</TD>
    <TD>pcw_uiparam_ddr_clock_3_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_clock_stop_en=0</TD>
    <TD>pcw_uiparam_ddr_col_addr_count=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_cwl=6</TD>
    <TD>pcw_uiparam_ddr_device_capacity=4096 MBits</TD>
    <TD>pcw_uiparam_ddr_dq_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_0_package_length=104.5365</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_0_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dq_1_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_1_package_length=70.676</TD>
    <TD>pcw_uiparam_ddr_dq_1_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_2_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dq_2_package_length=59.1615</TD>
    <TD>pcw_uiparam_ddr_dq_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dq_3_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dq_3_package_length=81.319</TD>
    <TD>pcw_uiparam_ddr_dq_3_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_0_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_0_package_length=101.239</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_0_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_1_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_1_package_length=79.5025</TD>
    <TD>pcw_uiparam_ddr_dqs_1_propogation_delay=160</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_2_length_mm=0</TD>
    <TD>pcw_uiparam_ddr_dqs_2_package_length=60.536</TD>
    <TD>pcw_uiparam_ddr_dqs_2_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_3_length_mm=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_3_package_length=71.7715</TD>
    <TD>pcw_uiparam_ddr_dqs_3_propogation_delay=160</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_0=0.0</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_1=0.0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_2=0.0</TD>
    <TD>pcw_uiparam_ddr_dqs_to_clk_delay_3=0.0</TD>
    <TD>pcw_uiparam_ddr_dram_width=16 Bits</TD>
    <TD>pcw_uiparam_ddr_ecc=Disabled</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_enable=1</TD>
    <TD>pcw_uiparam_ddr_freq_mhz=533.333333</TD>
    <TD>pcw_uiparam_ddr_high_temp=Normal (0-85)</TD>
    <TD>pcw_uiparam_ddr_memory_type=DDR 3</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_partno=MT41J256M16 RE-125</TD>
    <TD>pcw_uiparam_ddr_row_addr_count=15</TD>
    <TD>pcw_uiparam_ddr_speed_bin=DDR3_1066F</TD>
    <TD>pcw_uiparam_ddr_t_faw=40.0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_t_ras_min=35.0</TD>
    <TD>pcw_uiparam_ddr_t_rc=48.91</TD>
    <TD>pcw_uiparam_ddr_t_rcd=7</TD>
    <TD>pcw_uiparam_ddr_t_rp=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_uiparam_ddr_train_data_eye=1</TD>
    <TD>pcw_uiparam_ddr_train_read_gate=1</TD>
    <TD>pcw_uiparam_ddr_train_write_level=1</TD>
    <TD>pcw_uiparam_ddr_use_internal_vref=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_usb0_peripheral_enable=0</TD>
    <TD>pcw_usb0_peripheral_freqmhz=60</TD>
    <TD>pcw_usb0_reset_enable=0</TD>
    <TD>pcw_usb1_peripheral_enable=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_usb1_peripheral_freqmhz=60</TD>
    <TD>pcw_usb1_reset_enable=0</TD>
    <TD>pcw_usb_reset_polarity=Active Low</TD>
    <TD>pcw_use_cross_trigger=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_use_m_axi_gp0=1</TD>
    <TD>pcw_use_m_axi_gp1=0</TD>
    <TD>pcw_use_s_axi_acp=0</TD>
    <TD>pcw_use_s_axi_gp0=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_use_s_axi_gp1=0</TD>
    <TD>pcw_use_s_axi_hp0=1</TD>
    <TD>pcw_use_s_axi_hp1=1</TD>
    <TD>pcw_use_s_axi_hp2=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>pcw_use_s_axi_hp3=0</TD>
    <TD>pcw_wdt_peripheral_clksrc=CPU_1X</TD>
    <TD>pcw_wdt_peripheral_enable=0</TD>
    <TD>pcw_wdt_peripheral_freqmhz=133.333333</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>processing_system7_v5_5_processing_system7/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_dm_width=4</TD>
    <TD>c_dq_width=32</TD>
    <TD>c_dqs_width=4</TD>
    <TD>c_emio_gpio_width=3</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_en_emio_enet0=0</TD>
    <TD>c_en_emio_enet1=0</TD>
    <TD>c_en_emio_pjtag=0</TD>
    <TD>c_en_emio_trace=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fclk_clk0_buf=TRUE</TD>
    <TD>c_fclk_clk1_buf=FALSE</TD>
    <TD>c_fclk_clk2_buf=FALSE</TD>
    <TD>c_fclk_clk3_buf=FALSE</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gp0_en_modifiable_txn=1</TD>
    <TD>c_gp1_en_modifiable_txn=1</TD>
    <TD>c_include_acp_trans_check=0</TD>
    <TD>c_include_trace_buffer=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_irq_f2p_mode=DIRECT</TD>
    <TD>c_m_axi_gp0_enable_static_remap=0</TD>
    <TD>c_m_axi_gp0_id_width=12</TD>
    <TD>c_m_axi_gp0_thread_id_width=12</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_axi_gp1_enable_static_remap=0</TD>
    <TD>c_m_axi_gp1_id_width=12</TD>
    <TD>c_m_axi_gp1_thread_id_width=12</TD>
    <TD>c_mio_primitive=54</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_f2p_intr_inputs=2</TD>
    <TD>c_package_name=clg400</TD>
    <TD>c_ps7_si_rev=PRODUCTION</TD>
    <TD>c_s_axi_acp_aruser_val=31</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_acp_awuser_val=31</TD>
    <TD>c_s_axi_acp_id_width=3</TD>
    <TD>c_s_axi_gp0_id_width=6</TD>
    <TD>c_s_axi_gp1_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_hp0_data_width=64</TD>
    <TD>c_s_axi_hp0_id_width=6</TD>
    <TD>c_s_axi_hp1_data_width=64</TD>
    <TD>c_s_axi_hp1_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axi_hp2_data_width=64</TD>
    <TD>c_s_axi_hp2_id_width=6</TD>
    <TD>c_s_axi_hp3_data_width=64</TD>
    <TD>c_s_axi_hp3_id_width=6</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_trace_buffer_clock_delay=12</TD>
    <TD>c_trace_buffer_fifo_size=128</TD>
    <TD>c_trace_internal_width=2</TD>
    <TD>c_trace_pipeline_width=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_axi_nonsecure=0</TD>
    <TD>c_use_default_acp_user_val=0</TD>
    <TD>c_use_m_axi_gp0=1</TD>
    <TD>c_use_m_axi_gp1=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_s_axi_acp=0</TD>
    <TD>c_use_s_axi_gp0=0</TD>
    <TD>c_use_s_axi_gp1=0</TD>
    <TD>c_use_s_axi_hp0=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_use_s_axi_hp1=1</TD>
    <TD>c_use_s_axi_hp2=0</TD>
    <TD>c_use_s_axi_hp3=0</TD>
    <TD>core_container=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>use_trace_data_edge_detector=0</TD>
    <TD>x_ipcorerevision=6</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=processing_system7</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=5.5</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_exit_v1_0_8_top/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=32</TD>
    <TD>c_enable_pipelining=0x1</TD>
    <TD>c_family=zynq</TD>
    <TD>c_has_lock=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_is_cascaded=0</TD>
    <TD>c_m_aruser_width=0</TD>
    <TD>c_m_awuser_width=0</TD>
    <TD>c_m_buser_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_id_width=0</TD>
    <TD>c_m_limit_read_length=16</TD>
    <TD>c_m_limit_write_length=16</TD>
    <TD>c_m_protocol=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_ruser_bits_per_byte=0</TD>
    <TD>c_m_ruser_width=0</TD>
    <TD>c_m_wuser_bits_per_byte=0</TD>
    <TD>c_m_wuser_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_ruser_bits_per_byte=0</TD>
    <TD>c_max_wuser_bits_per_byte=0</TD>
    <TD>c_mep_identifier_width=1</TD>
    <TD>c_num_msc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_read_outstanding=2</TD>
    <TD>c_num_write_outstanding=2</TD>
    <TD>c_rdata_width=64</TD>
    <TD>c_read_acceptance=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_id_width=1</TD>
    <TD>c_single_issuing=0</TD>
    <TD>c_ssc_route_array=0b10</TD>
    <TD>c_ssc_route_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_wdata_width=64</TD>
    <TD>c_write_acceptance=32</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=8</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_exit</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_exit_v1_0_8_top/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=32</TD>
    <TD>c_enable_pipelining=0x1</TD>
    <TD>c_family=zynq</TD>
    <TD>c_has_lock=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_is_cascaded=0</TD>
    <TD>c_m_aruser_width=0</TD>
    <TD>c_m_awuser_width=0</TD>
    <TD>c_m_buser_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_id_width=0</TD>
    <TD>c_m_limit_read_length=16</TD>
    <TD>c_m_limit_write_length=16</TD>
    <TD>c_m_protocol=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_ruser_bits_per_byte=0</TD>
    <TD>c_m_ruser_width=0</TD>
    <TD>c_m_wuser_bits_per_byte=0</TD>
    <TD>c_m_wuser_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_ruser_bits_per_byte=0</TD>
    <TD>c_max_wuser_bits_per_byte=0</TD>
    <TD>c_mep_identifier_width=1</TD>
    <TD>c_num_msc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_read_outstanding=2</TD>
    <TD>c_num_write_outstanding=16</TD>
    <TD>c_rdata_width=64</TD>
    <TD>c_read_acceptance=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_id_width=1</TD>
    <TD>c_single_issuing=0</TD>
    <TD>c_ssc_route_array=0b01</TD>
    <TD>c_ssc_route_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_wdata_width=64</TD>
    <TD>c_write_acceptance=32</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=8</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_exit</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_mmu_v1_0_7_top/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=32</TD>
    <TD>c_enable_pipelining=0x1</TD>
    <TD>c_family=zynq</TD>
    <TD>c_id_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_is_cascaded=0</TD>
    <TD>c_msc_route_array=0b1</TD>
    <TD>c_msc_route_width=1</TD>
    <TD>c_num_msc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_read_outstanding=2</TD>
    <TD>c_num_seg=1</TD>
    <TD>c_num_write_outstanding=2</TD>
    <TD>c_rdata_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_read_acceptance=32</TD>
    <TD>c_s_aruser_width=0</TD>
    <TD>c_s_awuser_width=0</TD>
    <TD>c_s_buser_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_protocol=0</TD>
    <TD>c_s_ruser_width=0</TD>
    <TD>c_s_wuser_width=0</TD>
    <TD>c_seg_base_addr_array=0x0000000000000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_seg_secure_read_array=0b0</TD>
    <TD>c_seg_secure_write_array=0b0</TD>
    <TD>c_seg_sep_route_array=0x0000000000000000</TD>
    <TD>c_seg_size_array=0x0000001c</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_seg_supports_read_array=0x1</TD>
    <TD>c_seg_supports_write_array=0x1</TD>
    <TD>c_single_issuing=0</TD>
    <TD>c_supports_narrow=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_supports_read_decerr=1</TD>
    <TD>c_supports_wrap=1</TD>
    <TD>c_supports_write_decerr=1</TD>
    <TD>c_wdata_width=64</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_write_acceptance=32</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_mmu</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_mmu_v1_0_7_top/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=32</TD>
    <TD>c_enable_pipelining=0x1</TD>
    <TD>c_family=zynq</TD>
    <TD>c_id_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_is_cascaded=0</TD>
    <TD>c_msc_route_array=0b1</TD>
    <TD>c_msc_route_width=1</TD>
    <TD>c_num_msc=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_read_outstanding=2</TD>
    <TD>c_num_seg=1</TD>
    <TD>c_num_write_outstanding=16</TD>
    <TD>c_rdata_width=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_read_acceptance=32</TD>
    <TD>c_s_aruser_width=0</TD>
    <TD>c_s_awuser_width=0</TD>
    <TD>c_s_buser_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_protocol=0</TD>
    <TD>c_s_ruser_width=0</TD>
    <TD>c_s_wuser_width=0</TD>
    <TD>c_seg_base_addr_array=0x0000000010000000</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_seg_secure_read_array=0b0</TD>
    <TD>c_seg_secure_write_array=0b0</TD>
    <TD>c_seg_sep_route_array=0x0000000000000000</TD>
    <TD>c_seg_size_array=0x0000001c</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_seg_supports_read_array=0x1</TD>
    <TD>c_seg_supports_write_array=0x1</TD>
    <TD>c_single_issuing=0</TD>
    <TD>c_supports_narrow=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_supports_read_decerr=1</TD>
    <TD>c_supports_wrap=1</TD>
    <TD>c_supports_write_decerr=1</TD>
    <TD>c_wdata_width=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_write_acceptance=32</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_mmu</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_node_v1_0_10_top/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aclk_relationship=1</TD>
    <TD>c_aclken_conversion=0</TD>
    <TD>c_addr_width=32</TD>
    <TD>c_arbiter_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_channel=2</TD>
    <TD>c_disable_ip=0</TD>
    <TD>c_enable_pipelining=0x01</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fifo_ip=0</TD>
    <TD>c_fifo_output_reg=1</TD>
    <TD>c_fifo_size=5</TD>
    <TD>c_fifo_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_id_width=1</TD>
    <TD>c_m_num_bytes_array=0x00000008</TD>
    <TD>c_m_pipeline=0</TD>
    <TD>c_m_send_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_payld_bytes=8</TD>
    <TD>c_num_mi=1</TD>
    <TD>c_num_outstanding=2</TD>
    <TD>c_num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_payld_width=138</TD>
    <TD>c_s_latency=0</TD>
    <TD>c_s_num_bytes_array=0x00000008</TD>
    <TD>c_s_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sc_route_width=1</TD>
    <TD>c_synchronization_stages=3</TD>
    <TD>c_user_bits_per_byte=0</TD>
    <TD>c_user_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_node</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_node_v1_0_10_top/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aclk_relationship=1</TD>
    <TD>c_aclken_conversion=0</TD>
    <TD>c_addr_width=32</TD>
    <TD>c_arbiter_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_channel=0</TD>
    <TD>c_disable_ip=0</TD>
    <TD>c_enable_pipelining=0x01</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fifo_ip=0</TD>
    <TD>c_fifo_output_reg=1</TD>
    <TD>c_fifo_size=5</TD>
    <TD>c_fifo_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_id_width=1</TD>
    <TD>c_m_num_bytes_array=0x00000008</TD>
    <TD>c_m_pipeline=0</TD>
    <TD>c_m_send_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_payld_bytes=8</TD>
    <TD>c_num_mi=1</TD>
    <TD>c_num_outstanding=2</TD>
    <TD>c_num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_payld_width=83</TD>
    <TD>c_s_latency=0</TD>
    <TD>c_s_num_bytes_array=0x00000008</TD>
    <TD>c_s_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sc_route_width=1</TD>
    <TD>c_synchronization_stages=3</TD>
    <TD>c_user_bits_per_byte=0</TD>
    <TD>c_user_width=512</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_node</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_node_v1_0_10_top/3</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aclk_relationship=1</TD>
    <TD>c_aclken_conversion=0</TD>
    <TD>c_addr_width=32</TD>
    <TD>c_arbiter_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_channel=2</TD>
    <TD>c_disable_ip=0</TD>
    <TD>c_enable_pipelining=0x01</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fifo_ip=0</TD>
    <TD>c_fifo_output_reg=1</TD>
    <TD>c_fifo_size=5</TD>
    <TD>c_fifo_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_id_width=1</TD>
    <TD>c_m_num_bytes_array=0x00000008</TD>
    <TD>c_m_pipeline=0</TD>
    <TD>c_m_send_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_payld_bytes=8</TD>
    <TD>c_num_mi=1</TD>
    <TD>c_num_outstanding=2</TD>
    <TD>c_num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_payld_width=138</TD>
    <TD>c_s_latency=0</TD>
    <TD>c_s_num_bytes_array=0x00000004</TD>
    <TD>c_s_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sc_route_width=1</TD>
    <TD>c_synchronization_stages=3</TD>
    <TD>c_user_bits_per_byte=0</TD>
    <TD>c_user_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_node</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_node_v1_0_10_top/4</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aclk_relationship=1</TD>
    <TD>c_aclken_conversion=0</TD>
    <TD>c_addr_width=32</TD>
    <TD>c_arbiter_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_channel=3</TD>
    <TD>c_disable_ip=0</TD>
    <TD>c_enable_pipelining=0x01</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fifo_ip=0</TD>
    <TD>c_fifo_output_reg=1</TD>
    <TD>c_fifo_size=5</TD>
    <TD>c_fifo_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_id_width=1</TD>
    <TD>c_m_num_bytes_array=0x00000008</TD>
    <TD>c_m_pipeline=0</TD>
    <TD>c_m_send_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_payld_bytes=8</TD>
    <TD>c_num_mi=1</TD>
    <TD>c_num_outstanding=16</TD>
    <TD>c_num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_payld_width=138</TD>
    <TD>c_s_latency=0</TD>
    <TD>c_s_num_bytes_array=0x00000004</TD>
    <TD>c_s_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sc_route_width=1</TD>
    <TD>c_synchronization_stages=3</TD>
    <TD>c_user_bits_per_byte=0</TD>
    <TD>c_user_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_node</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_node_v1_0_10_top/5</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aclk_relationship=1</TD>
    <TD>c_aclken_conversion=0</TD>
    <TD>c_addr_width=32</TD>
    <TD>c_arbiter_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_channel=4</TD>
    <TD>c_disable_ip=0</TD>
    <TD>c_enable_pipelining=0x01</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fifo_ip=0</TD>
    <TD>c_fifo_output_reg=1</TD>
    <TD>c_fifo_size=5</TD>
    <TD>c_fifo_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_id_width=1</TD>
    <TD>c_m_num_bytes_array=0x0000000400000004</TD>
    <TD>c_m_pipeline=0</TD>
    <TD>c_m_send_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_payld_bytes=8</TD>
    <TD>c_num_mi=1</TD>
    <TD>c_num_outstanding=16</TD>
    <TD>c_num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_payld_width=5</TD>
    <TD>c_s_latency=0</TD>
    <TD>c_s_num_bytes_array=0x00000008</TD>
    <TD>c_s_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sc_route_width=1</TD>
    <TD>c_synchronization_stages=3</TD>
    <TD>c_user_bits_per_byte=0</TD>
    <TD>c_user_width=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_node</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_node_v1_0_10_top/6</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aclk_relationship=1</TD>
    <TD>c_aclken_conversion=0</TD>
    <TD>c_addr_width=32</TD>
    <TD>c_arbiter_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_channel=0</TD>
    <TD>c_disable_ip=0</TD>
    <TD>c_enable_pipelining=0x01</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fifo_ip=0</TD>
    <TD>c_fifo_output_reg=1</TD>
    <TD>c_fifo_size=5</TD>
    <TD>c_fifo_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_id_width=1</TD>
    <TD>c_m_num_bytes_array=0x0000000400000004</TD>
    <TD>c_m_pipeline=0</TD>
    <TD>c_m_send_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_payld_bytes=8</TD>
    <TD>c_num_mi=1</TD>
    <TD>c_num_outstanding=2</TD>
    <TD>c_num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_payld_width=83</TD>
    <TD>c_s_latency=0</TD>
    <TD>c_s_num_bytes_array=0x00000008</TD>
    <TD>c_s_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sc_route_width=1</TD>
    <TD>c_synchronization_stages=3</TD>
    <TD>c_user_bits_per_byte=0</TD>
    <TD>c_user_width=512</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_node</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_node_v1_0_10_top/7</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_aclk_relationship=1</TD>
    <TD>c_aclken_conversion=0</TD>
    <TD>c_addr_width=32</TD>
    <TD>c_arbiter_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_channel=1</TD>
    <TD>c_disable_ip=0</TD>
    <TD>c_enable_pipelining=0x01</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fifo_ip=0</TD>
    <TD>c_fifo_output_reg=1</TD>
    <TD>c_fifo_size=5</TD>
    <TD>c_fifo_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_id_width=1</TD>
    <TD>c_m_num_bytes_array=0x00000008</TD>
    <TD>c_m_pipeline=0</TD>
    <TD>c_m_send_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_payld_bytes=8</TD>
    <TD>c_num_mi=1</TD>
    <TD>c_num_outstanding=16</TD>
    <TD>c_num_si=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_payld_width=88</TD>
    <TD>c_s_latency=0</TD>
    <TD>c_s_num_bytes_array=0x00000004</TD>
    <TD>c_s_pipeline=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sc_route_width=1</TD>
    <TD>c_synchronization_stages=3</TD>
    <TD>c_user_bits_per_byte=0</TD>
    <TD>c_user_width=512</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_node</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_si_converter_v1_0_7_top/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=32</TD>
    <TD>c_enable_pipelining=0x1</TD>
    <TD>c_has_burst=0</TD>
    <TD>c_id_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_is_cascaded=0</TD>
    <TD>c_limit_read_length=0</TD>
    <TD>c_limit_write_length=0</TD>
    <TD>c_max_ruser_bits_per_byte=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_wuser_bits_per_byte=0</TD>
    <TD>c_mep_identifier_width=1</TD>
    <TD>c_msc_rdata_width_array=0x00000040</TD>
    <TD>c_msc_wdata_width_array=0x00000040</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_msc=1</TD>
    <TD>c_num_read_outstanding=2</TD>
    <TD>c_num_read_threads=1</TD>
    <TD>c_num_seg=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_write_outstanding=2</TD>
    <TD>c_num_write_threads=1</TD>
    <TD>c_rdata_width=64</TD>
    <TD>c_read_acceptance=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_read_watermark=0</TD>
    <TD>c_s_ruser_bits_per_byte=0</TD>
    <TD>c_s_wuser_bits_per_byte=0</TD>
    <TD>c_sep_protocol_array=0x00000001</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sep_rdata_width_array=0x00000040</TD>
    <TD>c_sep_wdata_width_array=0x00000040</TD>
    <TD>c_single_issuing=0</TD>
    <TD>c_supports_narrow=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_wdata_width=64</TD>
    <TD>c_write_acceptance=32</TD>
    <TD>c_write_watermark=0</TD>
    <TD>core_container=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=7</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipname=sc_si_converter</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_si_converter_v1_0_7_top/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=32</TD>
    <TD>c_enable_pipelining=0x1</TD>
    <TD>c_has_burst=1</TD>
    <TD>c_id_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_is_cascaded=0</TD>
    <TD>c_limit_read_length=0</TD>
    <TD>c_limit_write_length=0</TD>
    <TD>c_max_ruser_bits_per_byte=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_max_wuser_bits_per_byte=0</TD>
    <TD>c_mep_identifier_width=1</TD>
    <TD>c_msc_rdata_width_array=0x00000040</TD>
    <TD>c_msc_wdata_width_array=0x00000040</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_msc=1</TD>
    <TD>c_num_read_outstanding=2</TD>
    <TD>c_num_read_threads=1</TD>
    <TD>c_num_seg=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_write_outstanding=16</TD>
    <TD>c_num_write_threads=1</TD>
    <TD>c_rdata_width=32</TD>
    <TD>c_read_acceptance=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_read_watermark=0</TD>
    <TD>c_s_ruser_bits_per_byte=0</TD>
    <TD>c_s_wuser_bits_per_byte=0</TD>
    <TD>c_sep_protocol_array=0x00000001</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sep_rdata_width_array=0x00000040</TD>
    <TD>c_sep_wdata_width_array=0x00000040</TD>
    <TD>c_single_issuing=0</TD>
    <TD>c_supports_narrow=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_wdata_width=32</TD>
    <TD>c_write_acceptance=32</TD>
    <TD>c_write_watermark=0</TD>
    <TD>core_container=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=7</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipname=sc_si_converter</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>sc_transaction_regulator_v1_0_8_top/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=32</TD>
    <TD>c_enable_pipelining=0x1</TD>
    <TD>c_family=zynq</TD>
    <TD>c_is_cascaded=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_m_id_width=1</TD>
    <TD>c_mep_identifier=0</TD>
    <TD>c_mep_identifier_width=1</TD>
    <TD>c_num_read_outstanding=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_num_read_threads=1</TD>
    <TD>c_num_write_outstanding=16</TD>
    <TD>c_num_write_threads=1</TD>
    <TD>c_rdata_width=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_read_acceptance=32</TD>
    <TD>c_s_id_width=1</TD>
    <TD>c_sep_route_width=1</TD>
    <TD>c_single_issuing=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_supports_read_deadlock=0</TD>
    <TD>c_supports_write_deadlock=0</TD>
    <TD>c_wdata_width=32</TD>
    <TD>c_write_acceptance=32</TD>
</TR><TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=8</TD>
    <TD>x_iplanguage=VERILOG</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=sc_transaction_regulator</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>v_axi4s_vid_out_v4_0_10/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_addr_width=10</TD>
    <TD>c_addr_width_pixel_remap_420=10</TD>
    <TD>c_components_per_pixel=4</TD>
    <TD>c_family=zynq</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_has_async_clk=1</TD>
    <TD>c_hysteresis_level=12</TD>
    <TD>c_include_pixel_remap_420=0</TD>
    <TD>c_include_pixel_repeat=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_native_component_width=8</TD>
    <TD>c_native_data_width=32</TD>
    <TD>c_pixels_per_clock=1</TD>
    <TD>c_s_axis_component_width=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_s_axis_tdata_width=32</TD>
    <TD>c_sync_lock_threshold=4</TD>
    <TD>c_vtg_master_slave=0</TD>
    <TD>core_container=false</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=10</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipname=v_axi4s_vid_out</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipversion=4.0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>v_tc/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>c_det_achroma_en=0</TD>
    <TD>c_det_avideo_en=1</TD>
    <TD>c_det_fieldid_en=0</TD>
    <TD>c_det_hblank_en=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_det_hsync_en=1</TD>
    <TD>c_det_vblank_en=1</TD>
    <TD>c_det_vsync_en=1</TD>
    <TD>c_detect_en=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_hstart0=0</TD>
    <TD>c_fsync_hstart1=0</TD>
    <TD>c_fsync_hstart10=0</TD>
    <TD>c_fsync_hstart11=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_hstart12=0</TD>
    <TD>c_fsync_hstart13=0</TD>
    <TD>c_fsync_hstart14=0</TD>
    <TD>c_fsync_hstart15=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_hstart2=0</TD>
    <TD>c_fsync_hstart3=0</TD>
    <TD>c_fsync_hstart4=0</TD>
    <TD>c_fsync_hstart5=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_hstart6=0</TD>
    <TD>c_fsync_hstart7=0</TD>
    <TD>c_fsync_hstart8=0</TD>
    <TD>c_fsync_hstart9=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_vstart0=0</TD>
    <TD>c_fsync_vstart1=0</TD>
    <TD>c_fsync_vstart10=0</TD>
    <TD>c_fsync_vstart11=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_vstart12=0</TD>
    <TD>c_fsync_vstart13=0</TD>
    <TD>c_fsync_vstart14=0</TD>
    <TD>c_fsync_vstart15=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_vstart2=0</TD>
    <TD>c_fsync_vstart3=0</TD>
    <TD>c_fsync_vstart4=0</TD>
    <TD>c_fsync_vstart5=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_fsync_vstart6=0</TD>
    <TD>c_fsync_vstart7=0</TD>
    <TD>c_fsync_vstart8=0</TD>
    <TD>c_fsync_vstart9=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_achroma_en=0</TD>
    <TD>c_gen_achroma_polarity=1</TD>
    <TD>c_gen_auto_switch=0</TD>
    <TD>c_gen_avideo_en=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_avideo_polarity=1</TD>
    <TD>c_gen_cparity=0</TD>
    <TD>c_gen_f0_vblank_hend=1280</TD>
    <TD>c_gen_f0_vblank_hstart=1280</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_f0_vframe_size=750</TD>
    <TD>c_gen_f0_vsync_hend=1280</TD>
    <TD>c_gen_f0_vsync_hstart=1280</TD>
    <TD>c_gen_f0_vsync_vend=729</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_f0_vsync_vstart=724</TD>
    <TD>c_gen_f1_vblank_hend=1280</TD>
    <TD>c_gen_f1_vblank_hstart=1280</TD>
    <TD>c_gen_f1_vframe_size=750</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_f1_vsync_hend=1280</TD>
    <TD>c_gen_f1_vsync_hstart=1280</TD>
    <TD>c_gen_f1_vsync_vend=729</TD>
    <TD>c_gen_f1_vsync_vstart=724</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_fieldid_en=0</TD>
    <TD>c_gen_fieldid_polarity=1</TD>
    <TD>c_gen_hactive_size=1280</TD>
    <TD>c_gen_hblank_en=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_hblank_polarity=1</TD>
    <TD>c_gen_hframe_size=1650</TD>
    <TD>c_gen_hsync_en=1</TD>
    <TD>c_gen_hsync_end=1430</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_hsync_polarity=1</TD>
    <TD>c_gen_hsync_start=1390</TD>
    <TD>c_gen_interlaced=0</TD>
    <TD>c_gen_vactive_size=720</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_vblank_en=1</TD>
    <TD>c_gen_vblank_polarity=1</TD>
    <TD>c_gen_video_format=2</TD>
    <TD>c_gen_vsync_en=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_gen_vsync_polarity=1</TD>
    <TD>c_generate_en=1</TD>
    <TD>c_has_axi4_lite=1</TD>
    <TD>c_has_intc_if=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_interlace_en=0</TD>
    <TD>c_max_lines=4096</TD>
    <TD>c_max_pixels=4096</TD>
    <TD>c_num_fsyncs=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>c_sync_en=0</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
    <TD>x_ipcorerevision=13</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=v_tc</TD>
    <TD>x_ipproduct=Vivado 2018.3</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=6.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xlconcat_v2_1_1_xlconcat/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>dout_width=2</TD>
    <TD>in0_width=1</TD>
    <TD>in10_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in11_width=1</TD>
    <TD>in12_width=1</TD>
    <TD>in13_width=1</TD>
    <TD>in14_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in15_width=1</TD>
    <TD>in16_width=1</TD>
    <TD>in17_width=1</TD>
    <TD>in18_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in19_width=1</TD>
    <TD>in1_width=1</TD>
    <TD>in20_width=1</TD>
    <TD>in21_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in22_width=1</TD>
    <TD>in23_width=1</TD>
    <TD>in24_width=1</TD>
    <TD>in25_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in26_width=1</TD>
    <TD>in27_width=1</TD>
    <TD>in28_width=1</TD>
    <TD>in29_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in2_width=1</TD>
    <TD>in30_width=1</TD>
    <TD>in31_width=1</TD>
    <TD>in3_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in4_width=1</TD>
    <TD>in5_width=1</TD>
    <TD>in6_width=1</TD>
    <TD>in7_width=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>in8_width=1</TD>
    <TD>in9_width=1</TD>
    <TD>iptotal=1</TD>
    <TD>num_ports=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=1</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=xlconcat</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=2.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xlconstant_v1_1_5_xlconstant/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>const_val=0x1</TD>
    <TD>const_width=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=5</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=xlconstant</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xlconstant_v1_1_5_xlconstant/2</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>const_val=0x1</TD>
    <TD>const_width=1</TD>
    <TD>core_container=NA</TD>
    <TD>iptotal=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipcorerevision=5</TD>
    <TD>x_iplanguage=VERILOG</TD>
    <TD>x_iplibrary=ip</TD>
    <TD>x_ipname=xlconstant</TD>
</TR><TR ALIGN='LEFT'>    <TD>x_ipproduct=Vivado 2018.3</TD>
    <TD>x_ipsimlanguage=MIXED</TD>
    <TD>x_ipvendor=xilinx.com</TD>
    <TD>x_ipversion=1.1</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xpm_cdc_gray/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>dest_sync_ff=4</TD>
    <TD>init_sync_ff=1</TD>
    <TD>iptotal=7</TD>
</TR><TR ALIGN='LEFT'>    <TD>reg_output=0</TD>
    <TD>sim_assert_chk=0</TD>
    <TD>sim_lossless_gray_chk=0</TD>
    <TD>version=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>width=10</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xpm_cdc_sync_rst/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>def_val=1&apos;b0</TD>
    <TD>dest_sync_ff=4</TD>
    <TD>init=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>init_sync_ff=1</TD>
    <TD>iptotal=4</TD>
    <TD>sim_assert_chk=0</TD>
    <TD>version=0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xpm_fifo_async/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>cdc_sync_stages=4</TD>
    <TD>core_container=NA</TD>
    <TD>dout_reset_value=0</TD>
    <TD>ecc_mode=no_ecc</TD>
</TR><TR ALIGN='LEFT'>    <TD>en_adv_feature_async=16&apos;b0000011100000111</TD>
    <TD>fifo_memory_type=auto</TD>
    <TD>fifo_read_latency=0</TD>
    <TD>fifo_write_depth=1024</TD>
</TR><TR ALIGN='LEFT'>    <TD>full_reset_value=1</TD>
    <TD>iptotal=2</TD>
    <TD>p_common_clock=0</TD>
    <TD>p_ecc_mode=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_fifo_memory_type=0</TD>
    <TD>p_read_mode=1</TD>
    <TD>p_wakeup_time=2</TD>
    <TD>prog_empty_thresh=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>prog_full_thresh=10</TD>
    <TD>rd_data_count_width=11</TD>
    <TD>read_data_width=35</TD>
    <TD>read_mode=fwft</TD>
</TR><TR ALIGN='LEFT'>    <TD>related_clocks=0</TD>
    <TD>use_adv_features=0707</TD>
    <TD>wakeup_time=0</TD>
    <TD>wr_data_count_width=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>write_data_width=35</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xpm_fifo_base/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>both_stages_valid=3</TD>
    <TD>cdc_dest_sync_ff=4</TD>
    <TD>common_clock=0</TD>
    <TD>core_container=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>dout_reset_value=0</TD>
    <TD>ecc_mode=0</TD>
    <TD>en_adv_feature=16&apos;b0000011100000111</TD>
    <TD>en_ae=1&apos;b0</TD>
</TR><TR ALIGN='LEFT'>    <TD>en_af=1&apos;b0</TD>
    <TD>en_dvld=1&apos;b0</TD>
    <TD>en_of=1&apos;b1</TD>
    <TD>en_pe=1&apos;b1</TD>
</TR><TR ALIGN='LEFT'>    <TD>en_pf=1&apos;b1</TD>
    <TD>en_rdc=1&apos;b1</TD>
    <TD>en_uf=1&apos;b1</TD>
    <TD>en_wack=1&apos;b0</TD>
</TR><TR ALIGN='LEFT'>    <TD>en_wdc=1&apos;b1</TD>
    <TD>enable_ecc=0</TD>
    <TD>fg_eq_asym_dout=1&apos;b0</TD>
    <TD>fifo_mem_type=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>fifo_memory_type=0</TD>
    <TD>fifo_read_depth=1024</TD>
    <TD>fifo_read_latency=0</TD>
    <TD>fifo_size=35840</TD>
</TR><TR ALIGN='LEFT'>    <TD>fifo_write_depth=1024</TD>
    <TD>full_reset_value=1</TD>
    <TD>full_rst_val=1&apos;b1</TD>
    <TD>invalid=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=4</TD>
    <TD>pe_thresh_adj=8</TD>
    <TD>pe_thresh_max=1019</TD>
    <TD>pe_thresh_min=5</TD>
</TR><TR ALIGN='LEFT'>    <TD>pf_thresh_adj=8</TD>
    <TD>pf_thresh_max=1019</TD>
    <TD>pf_thresh_min=9</TD>
    <TD>prog_empty_thresh=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>prog_full_thresh=10</TD>
    <TD>rd_data_count_width=11</TD>
    <TD>rd_dc_width_ext=11</TD>
    <TD>rd_latency=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>rd_mode=1</TD>
    <TD>rd_pntr_width=10</TD>
    <TD>read_data_width=35</TD>
    <TD>read_mode=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>related_clocks=0</TD>
    <TD>remove_wr_rd_prot_logic=0</TD>
    <TD>sim_assert_chk=0</TD>
    <TD>stage1_valid=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>stage2_valid=1</TD>
    <TD>use_adv_features=0707</TD>
    <TD>version=0</TD>
    <TD>wakeup_time=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>width_ratio=1</TD>
    <TD>wr_data_count_width=11</TD>
    <TD>wr_dc_width_ext=11</TD>
    <TD>wr_depth_log=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>wr_pntr_width=10</TD>
    <TD>wr_rd_ratio=0</TD>
    <TD>wr_width_log=6</TD>
    <TD>write_data_width=35</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xpm_fifo_sync/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>core_container=NA</TD>
    <TD>dout_reset_value=0</TD>
    <TD>ecc_mode=no_ecc</TD>
    <TD>en_adv_feature_sync=16&apos;b0001111100011111</TD>
</TR><TR ALIGN='LEFT'>    <TD>fifo_memory_type=block</TD>
    <TD>fifo_read_latency=0</TD>
    <TD>fifo_write_depth=512</TD>
    <TD>full_reset_value=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=2</TD>
    <TD>p_common_clock=1</TD>
    <TD>p_ecc_mode=0</TD>
    <TD>p_fifo_memory_type=2</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_read_mode=1</TD>
    <TD>p_wakeup_time=2</TD>
    <TD>prog_empty_thresh=10</TD>
    <TD>prog_full_thresh=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>rd_data_count_width=4</TD>
    <TD>read_data_width=67</TD>
    <TD>read_mode=fwft</TD>
    <TD>use_adv_features=1F1F</TD>
</TR><TR ALIGN='LEFT'>    <TD>wakeup_time=0</TD>
    <TD>wr_data_count_width=10</TD>
    <TD>write_data_width=67</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xpm_memory_base/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>write_data_width=67</TD>
    <TD>addr_width_a=10</TD>
    <TD>addr_width_b=10</TD>
    <TD>auto_sleep_time=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>byte_write_width_a=35</TD>
    <TD>byte_write_width_b=35</TD>
    <TD>clocking_mode=1</TD>
    <TD>core_container=NA</TD>
</TR><TR ALIGN='LEFT'>    <TD>ecc_mode=0</TD>
    <TD>iptotal=11</TD>
    <TD>max_num_char=0</TD>
    <TD>memory_optimization=true</TD>
</TR><TR ALIGN='LEFT'>    <TD>memory_primitive=0</TD>
    <TD>memory_size=35840</TD>
    <TD>memory_type=1</TD>
    <TD>message_control=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>num_char_loc=0</TD>
    <TD>p_ecc_mode=no_ecc</TD>
    <TD>p_enable_byte_write_a=0</TD>
    <TD>p_enable_byte_write_b=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_max_depth_data=1024</TD>
    <TD>p_memory_opt=yes</TD>
    <TD>p_memory_primitive=auto</TD>
    <TD>p_min_width_data=35</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_min_width_data_a=35</TD>
    <TD>p_min_width_data_b=35</TD>
    <TD>p_min_width_data_ecc=35</TD>
    <TD>p_min_width_data_ldw=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_min_width_data_shft=35</TD>
    <TD>p_num_cols_write_a=1</TD>
    <TD>p_num_cols_write_b=1</TD>
    <TD>p_num_rows_read_a=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_num_rows_read_b=1</TD>
    <TD>p_num_rows_write_a=1</TD>
    <TD>p_num_rows_write_b=1</TD>
    <TD>p_sdp_write_mode=yes</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_width_addr_lsb_read_a=0</TD>
    <TD>p_width_addr_lsb_read_b=0</TD>
    <TD>p_width_addr_lsb_write_a=0</TD>
    <TD>p_width_addr_lsb_write_b=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_width_addr_read_a=10</TD>
    <TD>p_width_addr_read_b=10</TD>
    <TD>p_width_addr_write_a=10</TD>
    <TD>p_width_addr_write_b=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_width_col_write_a=35</TD>
    <TD>p_width_col_write_b=35</TD>
    <TD>read_data_width_a=35</TD>
    <TD>read_data_width_b=35</TD>
</TR><TR ALIGN='LEFT'>    <TD>read_latency_a=2</TD>
    <TD>read_latency_b=2</TD>
    <TD>read_reset_value_a=0</TD>
    <TD>read_reset_value_b=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>rst_mode_a=SYNC</TD>
    <TD>rst_mode_b=SYNC</TD>
    <TD>use_embedded_constraint=0</TD>
    <TD>use_mem_init=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>version=0</TD>
    <TD>wakeup_time=0</TD>
    <TD>write_data_width_a=35</TD>
    <TD>write_data_width_b=35</TD>
</TR><TR ALIGN='LEFT'>    <TD>write_mode_a=2</TD>
    <TD>write_mode_b=2</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>xpm_memory_sdpram/1</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>write_mode_b=2</TD>
    <TD>addr_width_a=5</TD>
    <TD>addr_width_b=5</TD>
    <TD>auto_sleep_time=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>byte_write_width_a=106</TD>
    <TD>clocking_mode=0</TD>
    <TD>core_container=NA</TD>
    <TD>ecc_mode=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>iptotal=7</TD>
    <TD>memory_optimization=true</TD>
    <TD>memory_primitive=1</TD>
    <TD>memory_size=3392</TD>
</TR><TR ALIGN='LEFT'>    <TD>message_control=0</TD>
    <TD>p_clocking_mode=0</TD>
    <TD>p_ecc_mode=0</TD>
    <TD>p_memory_optimization=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>p_memory_primitive=1</TD>
    <TD>p_wakeup_time=0</TD>
    <TD>p_write_mode_b=1</TD>
    <TD>read_data_width_b=106</TD>
</TR><TR ALIGN='LEFT'>    <TD>read_latency_b=1</TD>
    <TD>read_reset_value_b=0</TD>
    <TD>rst_mode_a=SYNC</TD>
    <TD>rst_mode_b=SYNC</TD>
</TR><TR ALIGN='LEFT'>    <TD>use_embedded_constraint=0</TD>
    <TD>use_mem_init=0</TD>
    <TD>wakeup_time=0</TD>
    <TD>write_data_width_a=106</TD>
</TR><TR ALIGN='LEFT'>    <TD>write_mode_b=1</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_drc</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-append=default::[not_specified]</TD>
    <TD>-checks=default::[not_specified]</TD>
    <TD>-fail_on=default::[not_specified]</TD>
    <TD>-force=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-format=default::[not_specified]</TD>
    <TD>-internal=default::[not_specified]</TD>
    <TD>-internal_only=default::[not_specified]</TD>
    <TD>-messages=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-name=default::[not_specified]</TD>
    <TD>-no_waivers=default::[not_specified]</TD>
    <TD>-return_string=default::[not_specified]</TD>
    <TD>-ruledecks=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-upgrade_cw=default::[not_specified]</TD>
    <TD>-waived=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>results</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>reqp-181=1</TD>
    <TD>reqp-1839=12</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>report_utilization</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>clocking</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bufgctrl_available=32</TD>
    <TD>bufgctrl_fixed=0</TD>
    <TD>bufgctrl_used=4</TD>
    <TD>bufgctrl_util_percentage=12.50</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufhce_available=72</TD>
    <TD>bufhce_fixed=0</TD>
    <TD>bufhce_used=0</TD>
    <TD>bufhce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufio_available=16</TD>
    <TD>bufio_fixed=0</TD>
    <TD>bufio_used=0</TD>
    <TD>bufio_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufmrce_available=8</TD>
    <TD>bufmrce_fixed=0</TD>
    <TD>bufmrce_used=0</TD>
    <TD>bufmrce_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_available=16</TD>
    <TD>bufr_fixed=0</TD>
    <TD>bufr_used=1</TD>
    <TD>bufr_util_percentage=6.25</TD>
</TR><TR ALIGN='LEFT'>    <TD>mmcme2_adv_available=4</TD>
    <TD>mmcme2_adv_fixed=0</TD>
    <TD>mmcme2_adv_used=1</TD>
    <TD>mmcme2_adv_util_percentage=25.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>plle2_adv_available=4</TD>
    <TD>plle2_adv_fixed=0</TD>
    <TD>plle2_adv_used=1</TD>
    <TD>plle2_adv_util_percentage=25.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>dsp</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>dsps_available=220</TD>
    <TD>dsps_fixed=0</TD>
    <TD>dsps_used=0</TD>
    <TD>dsps_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>io_standard</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>blvds_25=0</TD>
    <TD>diff_hstl_i=0</TD>
    <TD>diff_hstl_i_18=0</TD>
    <TD>diff_hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_hstl_ii_18=0</TD>
    <TD>diff_hsul_12=0</TD>
    <TD>diff_mobile_ddr=0</TD>
    <TD>diff_sstl135=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl135_r=0</TD>
    <TD>diff_sstl15=0</TD>
    <TD>diff_sstl15_r=0</TD>
    <TD>diff_sstl18_i=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>diff_sstl18_ii=0</TD>
    <TD>hstl_i=0</TD>
    <TD>hstl_i_18=0</TD>
    <TD>hstl_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>hstl_ii_18=0</TD>
    <TD>hsul_12=0</TD>
    <TD>lvcmos12=0</TD>
    <TD>lvcmos15=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvcmos18=0</TD>
    <TD>lvcmos25=0</TD>
    <TD>lvcmos33=1</TD>
    <TD>lvds_25=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lvttl=0</TD>
    <TD>mini_lvds_25=0</TD>
    <TD>mobile_ddr=0</TD>
    <TD>pci33_3=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>ppds_25=0</TD>
    <TD>rsds_25=0</TD>
    <TD>sstl135=0</TD>
    <TD>sstl135_r=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>sstl15=0</TD>
    <TD>sstl15_r=0</TD>
    <TD>sstl18_i=0</TD>
    <TD>sstl18_ii=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>tmds_33=0</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>memory</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>block_ram_tile_available=140</TD>
    <TD>block_ram_tile_fixed=0</TD>
    <TD>block_ram_tile_used=10</TD>
    <TD>block_ram_tile_util_percentage=7.14</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb18_available=280</TD>
    <TD>ramb18_fixed=0</TD>
    <TD>ramb18_used=0</TD>
    <TD>ramb18_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36_fifo_available=140</TD>
    <TD>ramb36_fifo_fixed=0</TD>
    <TD>ramb36_fifo_used=10</TD>
    <TD>ramb36_fifo_util_percentage=7.14</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramb36e1_only_used=10</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>primitives</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bibuf_functional_category=IO</TD>
    <TD>bibuf_used=130</TD>
    <TD>bufg_functional_category=Clock</TD>
    <TD>bufg_used=4</TD>
</TR><TR ALIGN='LEFT'>    <TD>bufr_functional_category=Clock</TD>
    <TD>bufr_used=1</TD>
    <TD>carry4_functional_category=CarryLogic</TD>
    <TD>carry4_used=254</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdce_functional_category=Flop &amp; Latch</TD>
    <TD>fdce_used=67</TD>
    <TD>fdpe_functional_category=Flop &amp; Latch</TD>
    <TD>fdpe_used=9</TD>
</TR><TR ALIGN='LEFT'>    <TD>fdre_functional_category=Flop &amp; Latch</TD>
    <TD>fdre_used=9080</TD>
    <TD>fdse_functional_category=Flop &amp; Latch</TD>
    <TD>fdse_used=386</TD>
</TR><TR ALIGN='LEFT'>    <TD>ibuf_functional_category=IO</TD>
    <TD>ibuf_used=20</TD>
    <TD>lut1_functional_category=LUT</TD>
    <TD>lut1_used=211</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut2_functional_category=LUT</TD>
    <TD>lut2_used=736</TD>
    <TD>lut3_functional_category=LUT</TD>
    <TD>lut3_used=1827</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut4_functional_category=LUT</TD>
    <TD>lut4_used=908</TD>
    <TD>lut5_functional_category=LUT</TD>
    <TD>lut5_used=1146</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut6_functional_category=LUT</TD>
    <TD>lut6_used=1664</TD>
    <TD>mmcme2_adv_functional_category=Clock</TD>
    <TD>mmcme2_adv_used=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>muxf7_functional_category=MuxFx</TD>
    <TD>muxf7_used=220</TD>
    <TD>muxf8_functional_category=MuxFx</TD>
    <TD>muxf8_used=11</TD>
</TR><TR ALIGN='LEFT'>    <TD>obuf_functional_category=IO</TD>
    <TD>obuf_used=34</TD>
    <TD>obuft_functional_category=IO</TD>
    <TD>obuft_used=5</TD>
</TR><TR ALIGN='LEFT'>    <TD>oddr_functional_category=IO</TD>
    <TD>oddr_used=1</TD>
    <TD>plle2_adv_functional_category=Clock</TD>
    <TD>plle2_adv_used=1</TD>
</TR><TR ALIGN='LEFT'>    <TD>ps7_functional_category=Specialized Resource</TD>
    <TD>ps7_used=1</TD>
    <TD>ramb36e1_functional_category=Block Memory</TD>
    <TD>ramb36e1_used=10</TD>
</TR><TR ALIGN='LEFT'>    <TD>ramd32_functional_category=Distributed Memory</TD>
    <TD>ramd32_used=390</TD>
    <TD>rams32_functional_category=Distributed Memory</TD>
    <TD>rams32_used=130</TD>
</TR><TR ALIGN='LEFT'>    <TD>srl16e_functional_category=Distributed Memory</TD>
    <TD>srl16e_used=177</TD>
    <TD>srlc32e_functional_category=Distributed Memory</TD>
    <TD>srlc32e_used=83</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>slice_logic</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>f7_muxes_available=26600</TD>
    <TD>f7_muxes_fixed=0</TD>
    <TD>f7_muxes_used=220</TD>
    <TD>f7_muxes_util_percentage=0.83</TD>
</TR><TR ALIGN='LEFT'>    <TD>f8_muxes_available=13300</TD>
    <TD>f8_muxes_fixed=0</TD>
    <TD>f8_muxes_used=11</TD>
    <TD>f8_muxes_util_percentage=0.08</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=260</TD>
    <TD>lut_as_logic_available=53200</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=5360</TD>
    <TD>lut_as_logic_util_percentage=10.08</TD>
    <TD>lut_as_memory_available=17400</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=469</TD>
    <TD>lut_as_memory_util_percentage=2.70</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=209</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_flip_flop_available=106400</TD>
    <TD>register_as_flip_flop_fixed=0</TD>
    <TD>register_as_flip_flop_used=9542</TD>
    <TD>register_as_flip_flop_util_percentage=8.97</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_as_latch_available=106400</TD>
    <TD>register_as_latch_fixed=0</TD>
    <TD>register_as_latch_used=0</TD>
    <TD>register_as_latch_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_luts_available=53200</TD>
    <TD>slice_luts_fixed=0</TD>
    <TD>slice_luts_used=5829</TD>
    <TD>slice_luts_util_percentage=10.96</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_available=106400</TD>
    <TD>slice_registers_fixed=0</TD>
    <TD>slice_registers_used=9542</TD>
    <TD>slice_registers_util_percentage=8.97</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_distributed_ram_fixed=0</TD>
    <TD>lut_as_distributed_ram_used=260</TD>
    <TD>lut_as_logic_available=53200</TD>
    <TD>lut_as_logic_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_logic_used=5360</TD>
    <TD>lut_as_logic_util_percentage=10.08</TD>
    <TD>lut_as_memory_available=17400</TD>
    <TD>lut_as_memory_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_as_memory_used=469</TD>
    <TD>lut_as_memory_util_percentage=2.70</TD>
    <TD>lut_as_shift_register_fixed=0</TD>
    <TD>lut_as_shift_register_used=209</TD>
</TR><TR ALIGN='LEFT'>    <TD>lut_in_front_of_the_register_is_unused_fixed=209</TD>
    <TD>lut_in_front_of_the_register_is_unused_used=4023</TD>
    <TD>lut_in_front_of_the_register_is_used_fixed=4023</TD>
    <TD>lut_in_front_of_the_register_is_used_used=783</TD>
</TR><TR ALIGN='LEFT'>    <TD>register_driven_from_outside_the_slice_fixed=783</TD>
    <TD>register_driven_from_outside_the_slice_used=4806</TD>
    <TD>register_driven_from_within_the_slice_fixed=4806</TD>
    <TD>register_driven_from_within_the_slice_used=4736</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_available=13300</TD>
    <TD>slice_fixed=0</TD>
    <TD>slice_registers_available=106400</TD>
    <TD>slice_registers_fixed=0</TD>
</TR><TR ALIGN='LEFT'>    <TD>slice_registers_used=9542</TD>
    <TD>slice_registers_util_percentage=8.97</TD>
    <TD>slice_used=2994</TD>
    <TD>slice_util_percentage=22.51</TD>
</TR><TR ALIGN='LEFT'>    <TD>slicel_fixed=0</TD>
    <TD>slicel_used=2069</TD>
    <TD>slicem_fixed=0</TD>
    <TD>slicem_used=925</TD>
</TR><TR ALIGN='LEFT'>    <TD>unique_control_sets_available=13300</TD>
    <TD>unique_control_sets_fixed=13300</TD>
    <TD>unique_control_sets_used=428</TD>
    <TD>unique_control_sets_util_percentage=3.22</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o5_and_o6_fixed=3.22</TD>
    <TD>using_o5_and_o6_used=51</TD>
    <TD>using_o5_output_only_fixed=51</TD>
    <TD>using_o5_output_only_used=8</TD>
</TR><TR ALIGN='LEFT'>    <TD>using_o6_output_only_fixed=8</TD>
    <TD>using_o6_output_only_used=150</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>specific_feature</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>bscane2_available=4</TD>
    <TD>bscane2_fixed=0</TD>
    <TD>bscane2_used=0</TD>
    <TD>bscane2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>capturee2_available=1</TD>
    <TD>capturee2_fixed=0</TD>
    <TD>capturee2_used=0</TD>
    <TD>capturee2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>dna_port_available=1</TD>
    <TD>dna_port_fixed=0</TD>
    <TD>dna_port_used=0</TD>
    <TD>dna_port_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>efuse_usr_available=1</TD>
    <TD>efuse_usr_fixed=0</TD>
    <TD>efuse_usr_used=0</TD>
    <TD>efuse_usr_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>frame_ecce2_available=1</TD>
    <TD>frame_ecce2_fixed=0</TD>
    <TD>frame_ecce2_used=0</TD>
    <TD>frame_ecce2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>icape2_available=2</TD>
    <TD>icape2_fixed=0</TD>
    <TD>icape2_used=0</TD>
    <TD>icape2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>startupe2_available=1</TD>
    <TD>startupe2_fixed=0</TD>
    <TD>startupe2_used=0</TD>
    <TD>startupe2_util_percentage=0.00</TD>
</TR><TR ALIGN='LEFT'>    <TD>xadc_available=1</TD>
    <TD>xadc_fixed=0</TD>
    <TD>xadc_used=0</TD>
    <TD>xadc_util_percentage=0.00</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>synthesis</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-assert=default::[not_specified]</TD>
    <TD>-bufg=default::12</TD>
    <TD>-cascade_dsp=default::auto</TD>
    <TD>-constrset=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-control_set_opt_threshold=default::auto</TD>
    <TD>-directive=default::default</TD>
    <TD>-fanout_limit=default::10000</TD>
    <TD>-flatten_hierarchy=default::rebuilt</TD>
</TR><TR ALIGN='LEFT'>    <TD>-fsm_extraction=default::auto</TD>
    <TD>-gated_clock_conversion=default::off</TD>
    <TD>-generic=default::[not_specified]</TD>
    <TD>-include_dirs=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-keep_equivalent_registers=default::[not_specified]</TD>
    <TD>-max_bram=default::-1</TD>
    <TD>-max_bram_cascade_height=default::-1</TD>
    <TD>-max_dsp=default::-1</TD>
</TR><TR ALIGN='LEFT'>    <TD>-max_uram=default::-1</TD>
    <TD>-max_uram_cascade_height=default::-1</TD>
    <TD>-mode=default::default</TD>
    <TD>-name=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-no_lc=default::[not_specified]</TD>
    <TD>-no_srlextract=default::[not_specified]</TD>
    <TD>-no_timing_driven=default::[not_specified]</TD>
    <TD>-part=xc7z020clg400-2</TD>
</TR><TR ALIGN='LEFT'>    <TD>-resource_sharing=default::auto</TD>
    <TD>-retiming=default::[not_specified]</TD>
    <TD>-rtl=default::[not_specified]</TD>
    <TD>-rtl_skip_constraints=default::[not_specified]</TD>
</TR><TR ALIGN='LEFT'>    <TD>-rtl_skip_ip=default::[not_specified]</TD>
    <TD>-seu_protect=default::none</TD>
    <TD>-sfcu=default::[not_specified]</TD>
    <TD>-shreg_min_size=default::3</TD>
</TR><TR ALIGN='LEFT'>    <TD>-top=UGT</TD>
    <TD>-verilog_define=default::[not_specified]</TD>
</TR>   </TABLE>
   </TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>usage</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>elapsed=00:05:07s</TD>
    <TD>hls_ip=0</TD>
    <TD>memory_gain=1589.668MB</TD>
    <TD>memory_peak=1972.723MB</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
 <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
  <TR ALIGN='CENTER' BGCOLOR='#A7BFDE'><TD COLSPAN='1'><B>xsim</B></TD></TR>
   <TR><TD>
   <TABLE BORDER='1' CELLSPACING='0' WIDTH='100%'>
    <TR ALIGN='CENTER' BGCOLOR='#DBE5F1'><TD COLSPAN='4'><B>command_line_options</B></TD></TR>
<TR ALIGN='LEFT'>    <TD>-sim_mode=default::behavioral</TD>
    <TD>-sim_type=default::</TD>
</TR>   </TABLE>
   </TD></TR>
  </TABLE><BR>
</BODY>
</HTML>
