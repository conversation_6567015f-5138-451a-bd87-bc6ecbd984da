################################################################################
# Vivado (TM) v2018.3 (64-bit)
#
# README.txt: Please read the sections below to understand the steps required to
#             run the exported script and information about the source files.
#
# Generated by export_simulation on Fri Aug 01 16:38:38 +0800 2025
#
################################################################################

1. How to run the generated simulation script:-

From the shell prompt in the current directory, issue the following command:-

./utg.sh

This command will launch the 'compile', 'elaborate' and 'simulate' functions
implemented in the script file for the 3-step flow. These functions are called
from the main 'run' function in the script file.

The 'run' function first executes the 'setup' function, the purpose of which is to
create simulator specific setup files, create design library mappings and library
directories and copy 'glbl.v' from the Vivado software install location into the
current directory.

The 'setup' function is also used for removing the simulator generated data in
order to reset the current directory to the original state when export_simulation
was launched from Vivado. This generated data can be removed by specifying the
'-reset_run' switch to the './utg.sh' script.

./utg.sh -reset_run

To keep the generated data from the previous run but regenerate the setup files and
library directories, use the '-noclean_files' switch.

./utg.sh -noclean_files

For more information on the script, please type './utg.sh -help'.

2. Additional design information files:-

export_simulation generates following additional file that can be used for fetching
the design files information or for integrating with external custom scripts.

Name   : file_info.txt
Purpose: This file contains detail design file information based on the compile order
         when export_simulation was executed from Vivado. The file contains information
         about the file type, name, whether it is part of the IP, associated library
         and the file path information.
