#!/bin/bash -f
#*********************************************************************************************************
# Vivado (TM) v2018.3 (64-bit)
#
# Filename    : utg.sh
# Simulator   : Synopsys Verilog Compiler Simulator
# Description : Simulation script for compiling, elaborating and verifying the project source files.
#               The script will automatically create the design libraries sub-directories in the run
#               directory, add the library logical mappings in the simulator setup file, create default
#               'do/prj' file, execute compilation, elaboration and simulation steps.
#
# Generated by Vivado on Fri Aug 01 16:38:38 +0800 2025
# SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
#
# Copyright 1986-2018 Xilinx, Inc. All Rights Reserved. 
#
# usage: utg.sh [-help]
# usage: utg.sh [-lib_map_path]
# usage: utg.sh [-noclean_files]
# usage: utg.sh [-reset_run]
#
# Prerequisite:- To compile and run simulation, you must compile the Xilinx simulation libraries using the
# 'compile_simlib' TCL command. For more information about this command, run 'compile_simlib -help' in the
# Vivado Tcl Shell. Once the libraries have been compiled successfully, specify the -lib_map_path switch
# that points to these libraries and rerun export_simulation. For more information about this switch please
# type 'export_simulation -help' in the Tcl shell.
#
# You can also point to the simulation libraries by either replacing the <SPECIFY_COMPILED_LIB_PATH> in this
# script with the compiled library directory path or specify this path with the '-lib_map_path' switch when
# executing this script. Please type 'utg.sh -help' for more information.
#
# Additional references - 'Xilinx Vivado Design Suite User Guide:Logic simulation (UG900)'
#
#*********************************************************************************************************

# Directory path for design sources and include directories (if any) wrt this path
ref_dir="."

# Override directory with 'export_sim_ref_dir' env path value if set in the shell
if [[ (! -z "$export_sim_ref_dir") && ($export_sim_ref_dir != "") ]]; then
  ref_dir="$export_sim_ref_dir"
fi

# Command line options
vlogan_opts="-full64"
vhdlan_opts="-full64"
vcs_elab_opts="-full64 -debug_pp -t ps -licqueue -l elaborate.log"
vcs_sim_opts="-ucli -licqueue -l simulate.log"

# Design libraries
design_libs=(xilinx_vip xil_defaultlib xpm axi_infrastructure_v1_1_0 axi_vip_v1_1_4 processing_system7_vip_v1_0_6 lib_cdc_v1_0_2 lib_pkg_v1_0_2 fifo_generator_v13_2_3 lib_fifo_v1_0_12 blk_mem_gen_v8_4_2 lib_bmg_v1_0_11 lib_srl_fifo_v1_0_2 axi_datamover_v5_1_20 axi_vdma_v6_3_6 axi_lite_ipif_v3_0_4 v_tc_v6_1_13 v_vid_in_axi4s_v4_0_9 v_axi4s_vid_out_v4_0_10 generic_baseblocks_v2_1_0 axi_register_slice_v2_1_18 axi_data_fifo_v2_1_17 axi_crossbar_v2_1_19 proc_sys_reset_v5_0_13 xlconstant_v1_1_5 smartconnect_v1_0 xlconcat_v2_1_1 axi_protocol_converter_v2_1_18)

# Simulation root library directory
sim_lib_dir="vcs_lib"

# Script info
echo -e "utg.sh - Script generated by export_simulation (Vivado v2018.3 (64-bit)-id)\n"

# Main steps
run()
{
  check_args $# $1
  setup $1 $2
  compile
  elaborate
  simulate
}

# RUN_STEP: <compile>
compile()
{
  # Compile design files
  vlogan -work xilinx_vip $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/axi4stream_vip_axi4streampc.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/axi_vip_axi4pc.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/xil_common_vip_pkg.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/axi4stream_vip_pkg.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/axi_vip_pkg.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/axi4stream_vip_if.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/axi_vip_if.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/clk_vip_if.sv" \
    "C:/Xilinx/Vivado/2018.3/data/xilinx_vip/hdl/rst_vip_if.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/hdl/xpm_cdc.sv" \
    "C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/hdl/xpm_fifo.sv" \
    "C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/hdl/xpm_memory.sv" \
  2>&1 | tee -a vlogan.log

  vhdlan -work xpm $vhdlan_opts \
    "C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_VCOMP.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work axi_infrastructure_v1_1_0 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl/axi_infrastructure_v1_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work axi_vip_v1_1_4 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/98af/hdl/axi_vip_v1_1_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work processing_system7_vip_v1_0_6 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl/processing_system7_vip_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_processing_system7_0_0/sim/utg_processing_system7_0_0.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work lib_cdc_v1_0_2 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ef1e/hdl/lib_cdc_v1_0_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vhdlan -work lib_pkg_v1_0_2 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/0513/hdl/lib_pkg_v1_0_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work fifo_generator_v13_2_3 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/64f4/simulation/fifo_generator_vlog_beh.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work fifo_generator_v13_2_3 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/64f4/hdl/fifo_generator_v13_2_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work fifo_generator_v13_2_3 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/64f4/hdl/fifo_generator_v13_2_rfs.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work lib_fifo_v1_0_12 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/544a/hdl/lib_fifo_v1_0_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work blk_mem_gen_v8_4_2 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/37c2/simulation/blk_mem_gen_v8_4.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work lib_bmg_v1_0_11 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/556c/hdl/lib_bmg_v1_0_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vhdlan -work lib_srl_fifo_v1_0_2 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/51ce/hdl/lib_srl_fifo_v1_0_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vhdlan -work axi_datamover_v5_1_20 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/dfb3/hdl/axi_datamover_v5_1_vh_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work axi_vdma_v6_3_6 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl/axi_vdma_v6_3_rfs.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work axi_vdma_v6_3_6 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl/axi_vdma_v6_3_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vhdlan -work xil_defaultlib $vhdlan_opts \
    "$ref_dir/../../../bd/utg/ip/utg_axi_vdma_0_0/sim/utg_axi_vdma_0_0.vhd" \
  2>&1 | tee -a vhdlan.log

  vhdlan -work axi_lite_ipif_v3_0_4 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/66ea/hdl/axi_lite_ipif_v3_0_vh_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vhdlan -work v_tc_v6_1_13 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/a92c/hdl/v_tc_v6_1_vh_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work v_vid_in_axi4s_v4_0_9 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2aa/hdl/v_vid_in_axi4s_v4_0_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work v_axi4s_vid_out_v4_0_10 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/a87e/hdl/v_axi4s_vid_out_v4_0_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_v_axi4s_vid_out_0_2/sim/utg_v_axi4s_vid_out_0_2.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work xil_defaultlib $vhdlan_opts \
    "$ref_dir/../../../bd/utg/ip/utg_v_tc_0_1/sim/utg_v_tc_0_1.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/9097/src/mmcme2_drp.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work xil_defaultlib $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/9097/src/axi_dynclk_S00_AXI.vhd" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/9097/src/axi_dynclk.vhd" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_dynclk_0_1/sim/utg_axi_dynclk_0_1.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work generic_baseblocks_v2_1_0 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b752/hdl/generic_baseblocks_v2_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work axi_register_slice_v2_1_18 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/cc23/hdl/axi_register_slice_v2_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work axi_data_fifo_v2_1_17 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/c4fd/hdl/axi_data_fifo_v2_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work axi_crossbar_v2_1_19 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/6c9d/hdl/axi_crossbar_v2_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_xbar_1/sim/utg_xbar_1.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work proc_sys_reset_v5_0_13 $vhdlan_opts \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/8842/hdl/proc_sys_reset_v5_0_vh_rfs.vhd" \
  2>&1 | tee -a vhdlan.log

  vhdlan -work xil_defaultlib $vhdlan_opts \
    "$ref_dir/../../../bd/utg/ip/utg_rst_ps7_0_100M_1/sim/utg_rst_ps7_0_100M_1.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/sim/bd_64d3.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work xlconstant_v1_1_5 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/4649/hdl/xlconstant_v1_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_0/sim/bd_64d3_one_0.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work xil_defaultlib $vhdlan_opts \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_1/sim/bd_64d3_psr_aclk_0.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work smartconnect_v1_0 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/sc_util_v1_0_vl_rfs.sv" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/f85e/hdl/sc_mmu_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_2/sim/bd_64d3_s00mmu_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work smartconnect_v1_0 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ca72/hdl/sc_transaction_regulator_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_3/sim/bd_64d3_s00tr_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work smartconnect_v1_0 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/9ade/hdl/sc_si_converter_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_4/sim/bd_64d3_s00sic_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work smartconnect_v1_0 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b89e/hdl/sc_axi2sc_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_5/sim/bd_64d3_s00a2s_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work smartconnect_v1_0 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/sc_node_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_6/sim/bd_64d3_sarn_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_7/sim/bd_64d3_srn_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work smartconnect_v1_0 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/7005/hdl/sc_sc2axi_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_8/sim/bd_64d3_m00s2a_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work smartconnect_v1_0 $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b387/hdl/sc_exit_v1_0_vl_rfs.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_9/sim/bd_64d3_m00e_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_2/sim/utg_axi_smc_2.v" \
    "$ref_dir/../../../bd/utg/ipshared/9d7a/src/misc_ctrl.v" \
    "$ref_dir/../../../bd/utg/ipshared/9d7a/hdl/misc_v1_0_S00_AXI.v" \
    "$ref_dir/../../../bd/utg/ipshared/9d7a/hdl/misc_v1_0.v" \
    "$ref_dir/../../../bd/utg/ip/utg_misc_0_0/sim/utg_misc_0_0.v" \
    "$ref_dir/../../../bd/utg/ip/utg_xlconstant_0_0/sim/utg_xlconstant_0_0.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work xlconcat_v2_1_1 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/2f66/hdl/xlconcat_v2_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_xlconcat_0_0/sim/utg_xlconcat_0_0.v" \
    "$ref_dir/../../../bd/utg/ipshared/69b7/src/adc_top.v" \
    "$ref_dir/../../../bd/utg/ipshared/69b7/src/async_fifo.v" \
    "$ref_dir/../../../bd/utg/ipshared/69b7/src/m_axis_s2mm.v" \
    "$ref_dir/../../../bd/utg/ipshared/69b7/src/s_axi.v" \
    "$ref_dir/../../../bd/utg/ipshared/69b7/src/s_axi_adc_ctrl.v" \
    "$ref_dir/../../../bd/utg/ipshared/69b7/src/AXI_ADC_v1_0.v" \
    "$ref_dir/../../../bd/utg/ip/utg_AXI_ADC_0_4/sim/utg_AXI_ADC_0_4.v" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/sim/bd_a552.v" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_0/sim/bd_a552_one_0.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work xil_defaultlib $vhdlan_opts \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_1/sim/bd_a552_psr_aclk_0.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work xil_defaultlib $vlogan_opts -sverilog +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_2/sim/bd_a552_s00mmu_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_3/sim/bd_a552_s00tr_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_4/sim/bd_a552_s00sic_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_5/sim/bd_a552_s00a2s_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_6/sim/bd_a552_sarn_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_7/sim/bd_a552_srn_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_8/sim/bd_a552_sawn_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_9/sim/bd_a552_swn_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_10/sim/bd_a552_sbn_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_11/sim/bd_a552_m00s2a_0.sv" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_12/sim/bd_a552_m00e_0.sv" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_axi_smc_0/sim/utg_axi_smc_0.v" \
    "$ref_dir/../../../bd/utg/ip/utg_xlconstant_0_1/sim/utg_xlconstant_0_1.v" \
  2>&1 | tee -a vlogan.log

  vhdlan -work xil_defaultlib $vhdlan_opts \
    "$ref_dir/../../../bd/utg/ip/utg_rst_ps7_0_100M_0/sim/utg_rst_ps7_0_100M_0.vhd" \
  2>&1 | tee -a vhdlan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/sim/utg.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work axi_protocol_converter_v2_1_18 $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/7a04/hdl/axi_protocol_converter_v2_1_vl_rfs.v" \
  2>&1 | tee -a vlogan.log

  vlogan -work xil_defaultlib $vlogan_opts +v2k +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/ec67/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/70cf/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/58e2/hdl" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/979d/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ipshared/b2d0/hdl/verilog" +incdir+"$ref_dir/../../../../UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0" +incdir+"C:/Xilinx/Vivado/2018.3/data/xilinx_vip/include" \
    "$ref_dir/../../../bd/utg/ip/utg_auto_pc_0/sim/utg_auto_pc_0.v" \
  2>&1 | tee -a vlogan.log


  vlogan -work xil_defaultlib $vlogan_opts +v2k \
    glbl.v \
  2>&1 | tee -a vlogan.log

}

# RUN_STEP: <elaborate>
elaborate()
{
  vcs $vcs_elab_opts xil_defaultlib.utg xil_defaultlib.glbl -o utg_simv
}

# RUN_STEP: <simulate>
simulate()
{
  ./utg_simv $vcs_sim_opts -do simulate.do
}

# STEP: setup
setup()
{
  case $1 in
    "-lib_map_path" )
      if [[ ($2 == "") ]]; then
        echo -e "ERROR: Simulation library directory path not specified (type \"./utg.sh -help\" for more information)\n"
        exit 1
      fi
      create_lib_mappings $2
    ;;
    "-reset_run" )
      reset_run
      echo -e "INFO: Simulation run files deleted.\n"
      exit 0
    ;;
    "-noclean_files" )
      # do not remove previous data
    ;;
    * )
      create_lib_mappings $2
  esac

  create_lib_dir

  # Add any setup/initialization commands here:-

  # <user specific commands>

}

# Define design library mappings
create_lib_mappings()
{
  file="synopsys_sim.setup"
  if [[ -e $file ]]; then
    if [[ ($1 == "") ]]; then
      return
    else
      rm -rf $file
    fi
  fi

  touch $file

  lib_map_path=""
  if [[ ($1 != "") ]]; then
    lib_map_path="$1"
  fi

  for (( i=0; i<${#design_libs[*]}; i++ )); do
    lib="${design_libs[i]}"
    mapping="$lib:$sim_lib_dir/$lib"
    echo $mapping >> $file
  done

  if [[ ($lib_map_path != "") ]]; then
    incl_ref="OTHERS=$lib_map_path/synopsys_sim.setup"
    echo $incl_ref >> $file
  fi
}

# Create design library directory paths
create_lib_dir()
{
  if [[ -e $sim_lib_dir ]]; then
    rm -rf $sim_lib_dir
  fi

  for (( i=0; i<${#design_libs[*]}; i++ )); do
    lib="${design_libs[i]}"
    lib_dir="$sim_lib_dir/$lib"
    if [[ ! -e $lib_dir ]]; then
      mkdir -p $lib_dir
    fi
  done
}

# Delete generated data from the previous run
reset_run()
{
  files_to_remove=(ucli.key utg_simv vlogan.log vhdlan.log compile.log elaborate.log simulate.log .vlogansetup.env .vlogansetup.args .vcs_lib_lock scirocco_command.log 64 AN.DB csrc utg_simv.daidir)
  for (( i=0; i<${#files_to_remove[*]}; i++ )); do
    file="${files_to_remove[i]}"
    if [[ -e $file ]]; then
      rm -rf $file
    fi
  done

  create_lib_dir
}

# Check command line arguments
check_args()
{
  if [[ ($1 == 1 ) && ($2 != "-lib_map_path" && $2 != "-noclean_files" && $2 != "-reset_run" && $2 != "-help" && $2 != "-h") ]]; then
    echo -e "ERROR: Unknown option specified '$2' (type \"./utg.sh -help\" for more information)\n"
    exit 1
  fi

  if [[ ($2 == "-help" || $2 == "-h") ]]; then
    usage
  fi
}

# Script usage
usage()
{
  msg="Usage: utg.sh [-help]\n\
Usage: utg.sh [-lib_map_path]\n\
Usage: utg.sh [-reset_run]\n\
Usage: utg.sh [-noclean_files]\n\n\
[-help] -- Print help information for this script\n\n\
[-lib_map_path <path>] -- Compiled simulation library directory path. The simulation library is compiled\n\
using the compile_simlib tcl command. Please see 'compile_simlib -help' for more information.\n\n\
[-reset_run] -- Recreate simulator setup files and library mappings for a clean run. The generated files\n\
from the previous run will be removed. If you don't want to remove the simulator generated files, use the\n\
-noclean_files switch.\n\n\
[-noclean_files] -- Reset previous run, but do not remove simulator generated files from the previous run.\n\n"
  echo -e $msg
  exit 1
}

# Launch script
run $1 $2
