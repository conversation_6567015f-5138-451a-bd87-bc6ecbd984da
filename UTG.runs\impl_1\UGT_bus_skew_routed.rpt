Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.
------------------------------------------------------------------------------------------------------------------------------------------
| Tool Version : Vivado v.2018.3 (win64) Build 2405991 Thu Dec  6 23:38:27 MST 2018
| Date         : Mon Aug  4 14:38:59 2025
| Host         : wantong running 64-bit major release  (build 9200)
| Command      : report_bus_skew -warn_on_violation -file UGT_bus_skew_routed.rpt -pb UGT_bus_skew_routed.pb -rpx UGT_bus_skew_routed.rpx
| Design       : UGT
| Device       : 7z020-clg400
| Speed File   : -2  PRODUCTION 1.11 2014-09-11
------------------------------------------------------------------------------------------------------------------------------------------

Bus Skew Report

Table of Contents
-----------------
1. Bus Skew Report Summary
2. Bus Skew Report Per Constraint

1. Bus Skew Report Summary
--------------------------

Id  Position  From                            To                              Corner  Requirement(ns)  Actual(ns)  Slack(ns)
--  --------  ------------------------------  ------------------------------  ------  ---------------  ----------  ---------
1   20        [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow              8.000       0.870      7.130
2   22        [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[8]}]]
                                              [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]}]]
                                                                              Slow              8.000       0.827      7.173
3   24        [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[14]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[8]}]]
                                              [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][14]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]}]]
                                                                              Slow              8.000       0.867      7.133
4   26        [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow              8.000       0.834      7.166
5   28        [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       0.999      9.001
6   30        [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       1.161      8.839
7   32        [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[0]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[8]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[9]}]]
                                              [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]}]]
                                                                              Slow             10.000       1.151      8.849


2. Bus Skew Report Per Constraint
---------------------------------

Id: 1
set_bus_skew -from [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]}]] 8.000
Requirement: 8.000ns
Endpoints: 14

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
clk_fpga_0            ADC_DCLK              u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]/D
                                                                            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/D
                                                                                                            Slow         0.870      7.130


Slack (MET) :             7.130ns  (requirement - actual skew)
  Endpoint Source:        u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[1]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Endpoint Destination:   u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Reference Source:       u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[8]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Reference Destination:  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            8.000ns
  Endpoint Relative Delay:   -0.925ns
  Reference Relative Delay:  -2.207ns
  Relative CRPR:              0.567ns
  Uncertainty:                0.154ns
  Actual Bus Skew:            0.870ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR + Uncertainty)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.390     2.469    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_clk
    SLICE_X42Y26         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[1]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X42Y26         FDRE (Prop_fdre_C_Q)         0.398     2.867 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[1]/Q
                         net (fo=1, routed)           0.480     3.347    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/async_path[1]
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.454     1.454 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.701     3.155    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.077     3.232 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.240     4.471    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X44Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]/C
                         clock pessimism              0.000     4.471    
    SLICE_X44Y28         FDRE (Setup_fdre_C_D)       -0.200     4.271    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]
  -------------------------------------------------------------------
                         data arrival                           3.347    
                         clock arrival                          4.271    
  -------------------------------------------------------------------
                         relative delay                        -0.925    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.241     2.223    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_clk
    SLICE_X43Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[8]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X43Y28         FDRE (Prop_fdre_C_Q)         0.304     2.527 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/src_gray_ff_reg[8]/Q
                         net (fo=1, routed)           0.277     2.805    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/async_path[8]
    SLICE_X43Y29         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.521     1.521 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.873     3.394    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.085     3.479 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.395     4.874    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_clk
    SLICE_X43Y29         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/C
                         clock pessimism              0.000     4.874    
    SLICE_X43Y29         FDRE (Hold_fdre_C_D)         0.138     5.012    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]
  -------------------------------------------------------------------
                         data arrival                           2.805    
                         clock arrival                          5.012    
  -------------------------------------------------------------------
                         relative delay                        -2.207    



Id: 2
set_bus_skew -from [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[8]}]] -to [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]}]] 8.000
Requirement: 8.000ns
Endpoints: 13

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
ADC_DCLK              clk_fpga_0            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                                                                            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]/D
                                                                                                            Slow         0.827      7.173


Slack (MET) :             7.173ns  (requirement - actual skew)
  Endpoint Source:        u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Endpoint Destination:   u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Reference Source:       u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[10]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Reference Destination:  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            8.000ns
  Endpoint Relative Delay:    3.618ns
  Reference Relative Delay:   2.409ns
  Relative CRPR:              0.536ns
  Uncertainty:                0.154ns
  Actual Bus Skew:            0.827ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR + Uncertainty)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.521     1.521 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.873     3.394    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.085     3.479 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.391     4.870    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_clk
    SLICE_X38Y26         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X38Y26         FDRE (Prop_fdre_C_Q)         0.398     5.268 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/Q
                         net (fo=1, routed)           0.373     5.641    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[7]
    SLICE_X39Y25         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.239     2.221    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_clk
    SLICE_X39Y25         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/C
                         clock pessimism              0.000     2.221    
    SLICE_X39Y25         FDRE (Setup_fdre_C_D)       -0.199     2.022    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]
  -------------------------------------------------------------------
                         data arrival                           5.641    
                         clock arrival                          2.022    
  -------------------------------------------------------------------
                         relative delay                         3.618    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.454     1.454 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.701     3.155    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.077     3.232 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.242     4.473    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_clk
    SLICE_X33Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[10]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y27         FDRE (Prop_fdre_C_Q)         0.304     4.777 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[10]/Q
                         net (fo=1, routed)           0.296     5.073    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[10]
    SLICE_X34Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.394     2.473    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_clk
    SLICE_X34Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]/C
                         clock pessimism              0.000     2.473    
    SLICE_X34Y27         FDRE (Hold_fdre_C_D)         0.191     2.664    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][10]
  -------------------------------------------------------------------
                         data arrival                           5.073    
                         clock arrival                          2.664    
  -------------------------------------------------------------------
                         relative delay                         2.409    



Id: 3
set_bus_skew -from [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[14]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[8]}]] -to [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][13]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][14]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]}]] 8.000
Requirement: 8.000ns
Endpoints: 14

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
ADC_DCLK              clk_fpga_0            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]/D
                                                                            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]/D
                                                                                                            Slow         0.867      7.133


Slack (MET) :             7.133ns  (requirement - actual skew)
  Endpoint Source:        u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[11]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Endpoint Destination:   u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Reference Source:       u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[12]/C
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Reference Destination:  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            8.000ns
  Endpoint Relative Delay:    3.691ns
  Reference Relative Delay:   2.411ns
  Relative CRPR:              0.567ns
  Uncertainty:                0.154ns
  Actual Bus Skew:            0.867ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR + Uncertainty)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.521     1.521 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.873     3.394    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.085     3.479 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.394     4.873    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_clk
    SLICE_X39Y28         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X39Y28         FDRE (Prop_fdre_C_Q)         0.348     5.221 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[11]/Q
                         net (fo=1, routed)           0.482     5.703    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/async_path[11]
    SLICE_X40Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.241     2.223    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X40Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]/C
                         clock pessimism              0.000     2.223    
    SLICE_X40Y27         FDRE (Setup_fdre_C_D)       -0.212     2.011    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][11]
  -------------------------------------------------------------------
                         data arrival                           5.703    
                         clock arrival                          2.011    
  -------------------------------------------------------------------
                         relative delay                         3.691    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.454     1.454 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.701     3.155    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.077     3.232 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.246     4.477    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_clk
    SLICE_X40Y32         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[12]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X40Y32         FDRE (Prop_fdre_C_Q)         0.304     4.781 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[12]/Q
                         net (fo=1, routed)           0.294     5.075    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/async_path[12]
    SLICE_X42Y32         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.398     2.477    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X42Y32         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]/C
                         clock pessimism              0.000     2.477    
    SLICE_X42Y32         FDRE (Hold_fdre_C_D)         0.187     2.664    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][12]
  -------------------------------------------------------------------
                         data arrival                           5.075    
                         clock arrival                          2.664    
  -------------------------------------------------------------------
                         relative delay                         2.411    



Id: 4
set_bus_skew -from [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][12]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} \
          {u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 8.000
Requirement: 8.000ns
Endpoints: 13

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
clk_fpga_0            ADC_DCLK              u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/D
                                                                            u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                                                                                                            Slow         0.834      7.166


Slack (MET) :             7.166ns  (requirement - actual skew)
  Endpoint Source:        u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[11]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Endpoint Destination:   u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Reference Source:       u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[0]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Reference Destination:  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
                            (rising edge-triggered cell FDRE clocked by ADC_DCLK)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            8.000ns
  Endpoint Relative Delay:   -1.012ns
  Reference Relative Delay:  -2.228ns
  Relative CRPR:              0.536ns
  Uncertainty:                0.154ns
  Actual Bus Skew:            0.834ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR + Uncertainty)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.394     2.473    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_clk
    SLICE_X34Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[11]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X34Y27         FDRE (Prop_fdre_C_Q)         0.398     2.871 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[11]/Q
                         net (fo=1, routed)           0.388     3.259    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/async_path[11]
    SLICE_X33Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.454     1.454 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.701     3.155    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.077     3.232 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.242     4.473    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X33Y27         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]/C
                         clock pessimism              0.000     4.473    
    SLICE_X33Y27         FDRE (Setup_fdre_C_D)       -0.202     4.271    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][11]
  -------------------------------------------------------------------
                         data arrival                           3.259    
                         clock arrival                          4.271    
  -------------------------------------------------------------------
                         relative delay                        -1.012    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.245     2.227    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_clk
    SLICE_X33Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[0]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X33Y30         FDRE (Prop_fdre_C_Q)         0.304     2.531 r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[0]/Q
                         net (fo=1, routed)           0.304     2.835    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/async_path[0]
    SLICE_X34Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/D
  -------------------------------------------------------------------    -------------------

                         (clock ADC_DCLK rise edge)
                                                      0.000     0.000 r  
    U14                                               0.000     0.000 r  ADC_DCLK (IN)
                         net (fo=0)                   0.000     0.000    ADC_DCLK
    U14                  IBUF (Prop_ibuf_I_O)         1.521     1.521 r  ADC_DCLK_IBUF_inst/O
                         net (fo=1, routed)           1.873     3.394    ADC_DCLK_IBUF
    BUFGCTRL_X0Y18       BUFG (Prop_bufg_I_O)         0.085     3.479 r  ADC_DCLK_IBUF_BUFG_inst/O
                         net (fo=308, routed)         1.398     4.877    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X34Y30         FDRE                                         r  u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]/C
                         clock pessimism              0.000     4.877    
    SLICE_X34Y30         FDRE (Hold_fdre_C_D)         0.187     5.064    u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]
  -------------------------------------------------------------------
                         data arrival                           2.835    
                         clock arrival                          5.064    
  -------------------------------------------------------------------
                         relative delay                        -2.228    



Id: 5
set_bus_skew -from [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 10

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
LCD_CLK_OBUF          clk_fpga_0            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]/D
                                                                            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]/D
                                                                                                            Slow         0.999      9.001


Slack (MET) :             9.001ns  (requirement - actual skew)
  Endpoint Source:        u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[9]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF)
  Endpoint Destination:   u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Reference Source:       u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[2]/C
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF)
  Reference Destination:  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]/D
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:    3.860ns
  Reference Relative Delay:   2.751ns
  Relative CRPR:              0.373ns
  Uncertainty:                0.263ns
  Actual Bus Skew:            0.999ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR + Uncertainty)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.820     5.220    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_clk
    SLICE_X61Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[9]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X61Y50         FDRE (Prop_fdre_C_Q)         0.348     5.568 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[9]/Q
                         net (fo=1, routed)           0.466     6.034    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/async_path[9]
    SLICE_X61Y47         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.305     2.287    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X61Y47         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]/C
                         clock pessimism              0.097     2.384    
    SLICE_X61Y47         FDRE (Setup_fdre_C_D)       -0.210     2.174    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][9]
  -------------------------------------------------------------------
                         data arrival                           6.034    
                         clock arrival                          2.174    
  -------------------------------------------------------------------
                         relative delay                         3.860    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367     2.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073     2.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827     3.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725     3.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.752     4.727    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_clk
    SLICE_X60Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[2]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X60Y50         FDRE (Prop_fdre_C_Q)         0.304     5.031 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/src_gray_ff_reg[2]/Q
                         net (fo=1, routed)           0.285     5.316    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/async_path[2]
    SLICE_X63Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]/D
  -------------------------------------------------------------------    -------------------

                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.444     2.523    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_clk
    SLICE_X63Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]/C
                         clock pessimism             -0.097     2.427    
    SLICE_X63Y50         FDRE (Hold_fdre_C_D)         0.138     2.565    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst/dest_graysync_ff_reg[0][2]
  -------------------------------------------------------------------
                         data arrival                           5.316    
                         clock arrival                          2.565    
  -------------------------------------------------------------------
                         relative delay                         2.751    



Id: 6
set_bus_skew -from [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][0]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][1]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][2]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][3]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][4]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][5]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][6]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]} {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 10

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
clk_fpga_0            LCD_CLK_OBUF          u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                                                                            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/D
                                                                                                            Slow         1.161      8.839


Slack (MET) :             8.839ns  (requirement - actual skew)
  Endpoint Source:        u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Endpoint Destination:   u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF)
  Reference Source:       u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[8]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Reference Destination:  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:   -1.059ns
  Reference Relative Delay:  -2.326ns
  Relative CRPR:              0.369ns
  Uncertainty:                0.263ns
  Actual Bus Skew:            1.161ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR + Uncertainty)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.461     2.540    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_clk
    SLICE_X58Y49         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X58Y49         FDRE (Prop_fdre_C_Q)         0.398     2.938 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[7]/Q
                         net (fo=1, routed)           0.624     3.562    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[7]
    SLICE_X60Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367     2.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073     2.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827     3.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725     3.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.752     4.727    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_clk
    SLICE_X60Y50         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]/C
                         clock pessimism              0.097     4.823    
    SLICE_X60Y50         FDRE (Setup_fdre_C_D)       -0.202     4.621    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][7]
  -------------------------------------------------------------------
                         data arrival                           3.562    
                         clock arrival                          4.621    
  -------------------------------------------------------------------
                         relative delay                        -1.059    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.286     2.269    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_clk
    SLICE_X62Y51         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[8]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X62Y51         FDRE (Prop_fdre_C_Q)         0.347     2.616 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/src_gray_ff_reg[8]/Q
                         net (fo=1, routed)           0.317     2.933    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/async_path[8]
    SLICE_X60Y51         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.820     5.220    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_clk
    SLICE_X60Y51         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]/C
                         clock pessimism             -0.097     5.124    
    SLICE_X60Y51         FDRE (Hold_fdre_C_D)         0.136     5.260    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst/dest_graysync_ff_reg[0][8]
  -------------------------------------------------------------------
                         data arrival                           2.933    
                         clock arrival                          5.259    
  -------------------------------------------------------------------
                         relative delay                        -2.326    



Id: 7
set_bus_skew -from [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[0]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[10]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[1]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[2]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[3]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[4]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[6]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[7]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[8]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[9]}]] -to [get_cells [list {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][0]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][10]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][1]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][2]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][3]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][4]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][6]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][7]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]} \
          {u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][9]}]] 10.000
Requirement: 10.000ns
Endpoints: 11

From Clock            To Clock              Endpoint Pin                    Reference Pin                   Corner  Actual(ns)  Slack(ns)
--------------------  --------------------  ------------------------------  ------------------------------  ------  ----------  ---------
clk_fpga_0            LCD_CLK_OBUF          u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]/D
                                                                            u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/D
                                                                                                            Slow         1.151      8.849


Slack (MET) :             8.849ns  (requirement - actual skew)
  Endpoint Source:        u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Endpoint Destination:   u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF)
  Reference Source:       u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[8]/C
                            (rising edge-triggered cell FDRE clocked by clk_fpga_0)
  Reference Destination:  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/D
                            (rising edge-triggered cell FDRE clocked by LCD_CLK_OBUF)
  Path Type:              Bus Skew (Max at Slow Process Corner)
  Requirement:            10.000ns
  Endpoint Relative Delay:   -1.127ns
  Reference Relative Delay:  -2.344ns
  Relative CRPR:              0.329ns
  Uncertainty:                0.263ns
  Actual Bus Skew:            1.151ns  (Endpoint Relative Delay - Reference Relative Delay - Relative CRPR + Uncertainty)

Endpoint path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.459     2.538    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_clk
    SLICE_X54Y49         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X54Y49         FDRE (Prop_fdre_C_Q)         0.398     2.936 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[5]/Q
                         net (fo=1, routed)           0.600     3.536    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/async_path[5]
    SLICE_X54Y52         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.367     2.350    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.073     2.423 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.827     3.250    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.725     3.975 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.752     4.727    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X54Y52         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]/C
                         clock pessimism              0.097     4.823    
    SLICE_X54Y52         FDRE (Setup_fdre_C_D)       -0.160     4.663    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][5]
  -------------------------------------------------------------------
                         data arrival                           3.536    
                         clock arrival                          4.663    
  -------------------------------------------------------------------
                         relative delay                        -1.127    

Reference path:
    Location             Delay type                Incr(ns)  Path(ns)    Netlist Resource(s)
  -------------------------------------------------------------------    -------------------
                         (clock clk_fpga_0 rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.905     0.905    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.077     0.982 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.286     2.269    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_clk
    SLICE_X62Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[8]/C
  -------------------------------------------------------------------    -------------------
    SLICE_X62Y53         FDRE (Prop_fdre_C_Q)         0.347     2.616 r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/src_gray_ff_reg[8]/Q
                         net (fo=1, routed)           0.300     2.915    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/async_path[8]
    SLICE_X56Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/D
  -------------------------------------------------------------------    -------------------

                         (clock LCD_CLK_OBUF rise edge)
                                                      0.000     0.000 r  
    PS7_X0Y0             PS7                          0.000     0.000 r  u1/utg_i/processing_system7_0/inst/PS7_i/FCLKCLK[0]
                         net (fo=1, routed)           0.994     0.994    u1/utg_i/processing_system7_0/inst/FCLK_CLK_unbuffered[0]
    BUFGCTRL_X0Y17       BUFG (Prop_bufg_I_O)         0.085     1.079 r  u1/utg_i/processing_system7_0/inst/buffer_fclk_clk_0.FCLK_CLK_0_BUFG/O
                         net (fo=7536, routed)        1.534     2.613    u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/REF_CLK_I
    MMCME2_ADV_X1Y1      MMCME2_ADV (Prop_mmcme2_adv_CLKIN1_CLKOUT0)
                                                      0.077     2.690 r  u1/utg_i/axi_dynclk_0/U0/Inst_mmcme2_drp/mmcm_adv_inst/CLKOUT0
                         net (fo=1, routed)           0.916     3.606    u1/utg_i/axi_dynclk_0/U0/I
    BUFR_X1Y5            BUFR (Prop_bufr_I_O)         0.794     4.400 r  u1/utg_i/axi_dynclk_0/U0/BUFR_inst/O
                         net (fo=2504, routed)        0.820     5.220    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_clk
    SLICE_X56Y53         FDRE                                         r  u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]/C
                         clock pessimism             -0.097     5.124    
    SLICE_X56Y53         FDRE (Hold_fdre_C_D)         0.136     5.260    u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst/dest_graysync_ff_reg[0][8]
  -------------------------------------------------------------------
                         data arrival                           2.915    
                         clock arrival                          5.259    
  -------------------------------------------------------------------
                         relative delay                        -2.344    



