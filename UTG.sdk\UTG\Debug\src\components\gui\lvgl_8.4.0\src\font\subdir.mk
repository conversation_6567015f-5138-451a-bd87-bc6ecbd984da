################################################################################
# Automatically-generated file. Do not edit!
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../src/components/gui/lvgl_8.4.0/src/font/lv_font.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_dejavu_16_persian_hebrew.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_fmt_txt.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_loader.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_10.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_12.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_12_subpx.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_14.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_16.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_18.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_20.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_22.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_24.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_26.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_28.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_28_compressed.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_30.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_32.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_34.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_36.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_38.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_40.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_42.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_44.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_46.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_48.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_8.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_simsun_16_cjk.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_unscii_16.c \
../src/components/gui/lvgl_8.4.0/src/font/lv_font_unscii_8.c 

OBJS += \
./src/components/gui/lvgl_8.4.0/src/font/lv_font.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_dejavu_16_persian_hebrew.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_fmt_txt.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_loader.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_10.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_12.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_12_subpx.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_14.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_16.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_18.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_20.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_22.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_24.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_26.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_28.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_28_compressed.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_30.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_32.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_34.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_36.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_38.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_40.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_42.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_44.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_46.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_48.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_8.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_simsun_16_cjk.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_unscii_16.o \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_unscii_8.o 

C_DEPS += \
./src/components/gui/lvgl_8.4.0/src/font/lv_font.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_dejavu_16_persian_hebrew.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_fmt_txt.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_loader.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_10.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_12.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_12_subpx.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_14.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_16.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_18.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_20.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_22.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_24.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_26.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_28.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_28_compressed.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_30.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_32.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_34.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_36.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_38.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_40.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_42.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_44.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_46.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_48.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_montserrat_8.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_simsun_16_cjk.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_unscii_16.d \
./src/components/gui/lvgl_8.4.0/src/font/lv_font_unscii_8.d 


# Each subdirectory must supply rules for building sources it contributes
src/components/gui/lvgl_8.4.0/src/font/%.o: ../src/components/gui/lvgl_8.4.0/src/font/%.c
	@echo 'Building file: $<'
	@echo 'Invoking: ARM v7 gcc compiler'
	arm-none-eabi-gcc -DLV_LVGL_H_INCLUDE_SIMPLE=1 -Wall -O0 -g3 -I../../UTG_bsp/ps7_cortexa9_0/include -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_display" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers\drv_touch" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos" -I"F:\UTG\UTG\UTG.sdk\UTG\src\drivers" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\demos\printer\generated" -I"F:\UTG\UTG\UTG.sdk\UTG\src\components\gui\lvgl_8.4.0\porting" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app" -I"F:\UTG\UTG\UTG.sdk\UTG\src\app\gui" -c -fmessage-length=0 -MT"$@" -mcpu=cortex-a9 -mfpu=vfpv3 -mfloat-abi=hard -MMD -MP -MF"$(@:%.o=%.d)" -MT"$(@)" -o "$@" "$<"
	@echo 'Finished building: $<'
	@echo ' '


