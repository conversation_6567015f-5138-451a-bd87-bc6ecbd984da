{"version": "1.0", "modules": {"utg": {"proto_instances": {"/AXI_ADC_0/M00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "m00_axi_aclk"}, "ARADDR": {"actual": "m00_axi_araddr"}, "ARBURST": {"actual": "m00_axi_arburst"}, "ARCACHE": {"actual": "m00_axi_arcache"}, "ARESETN": {"actual": "m00_axi_aresetn"}, "ARID": {"actual": "m00_axi_arid"}, "ARLEN": {"actual": "m00_axi_arlen"}, "ARLOCK": {"actual": "m00_axi_arlock"}, "ARPROT": {"actual": "m00_axi_arprot"}, "ARQOS": {"actual": "m00_axi_arqos"}, "ARREADY": {"actual": "m00_axi_arready"}, "ARSIZE": {"actual": "m00_axi_arsize"}, "ARVALID": {"actual": "m00_axi_arvalid"}, "AWADDR": {"actual": "m00_axi_awaddr"}, "AWBURST": {"actual": "m00_axi_awburst"}, "AWCACHE": {"actual": "m00_axi_awcache"}, "AWID": {"actual": "m00_axi_awid"}, "AWLEN": {"actual": "m00_axi_awlen"}, "AWLOCK": {"actual": "m00_axi_awlock"}, "AWPROT": {"actual": "m00_axi_awprot"}, "AWQOS": {"actual": "m00_axi_awqos"}, "AWREADY": {"actual": "m00_axi_awready"}, "AWSIZE": {"actual": "m00_axi_awsize"}, "AWVALID": {"actual": "m00_axi_awvalid"}, "BID": {"actual": "m00_axi_bid"}, "BREADY": {"actual": "m00_axi_bready"}, "BRESP": {"actual": "m00_axi_bresp"}, "BVALID": {"actual": "m00_axi_bvalid"}, "RDATA": {"actual": "m00_axi_rdata"}, "RID": {"actual": "m00_axi_rid"}, "RLAST": {"actual": "m00_axi_rlast"}, "RREADY": {"actual": "m00_axi_rready"}, "RRESP": {"actual": "m00_axi_rresp"}, "RVALID": {"actual": "m00_axi_rvalid"}, "WDATA": {"actual": "m00_axi_wdata"}, "WLAST": {"actual": "m00_axi_wlast"}, "WREADY": {"actual": "m00_axi_wready"}, "WSTRB": {"actual": "m00_axi_wstrb"}, "WVALID": {"actual": "m00_axi_wvalid"}}}, "/AXI_ADC_0/S00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "s00_axi_aclk"}, "ARADDR": {"actual": "s00_axi_araddr"}, "ARESETN": {"actual": "s00_axi_aresetn"}, "ARPROT": {"actual": "s00_axi_arprot"}, "ARREADY": {"actual": "s00_axi_arready"}, "ARVALID": {"actual": "s00_axi_arvalid"}, "AWADDR": {"actual": "s00_axi_awaddr"}, "AWPROT": {"actual": "s00_axi_awprot"}, "AWREADY": {"actual": "s00_axi_awready"}, "AWVALID": {"actual": "s00_axi_awvalid"}, "BREADY": {"actual": "s00_axi_bready"}, "BRESP": {"actual": "s00_axi_bresp"}, "BVALID": {"actual": "s00_axi_bvalid"}, "RDATA": {"actual": "s00_axi_rdata"}, "RREADY": {"actual": "s00_axi_rready"}, "RRESP": {"actual": "s00_axi_rresp"}, "RVALID": {"actual": "s00_axi_rvalid"}, "WDATA": {"actual": "s00_axi_wdata"}, "WREADY": {"actual": "s00_axi_wready"}, "WSTRB": {"actual": "s00_axi_wstrb"}, "WVALID": {"actual": "s00_axi_wvalid"}}}, "/axi_dynclk_0/s00_axi": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "s00_axi_aclk"}, "ARADDR": {"actual": "s00_axi_araddr"}, "ARESETN": {"actual": "s00_axi_aresetn"}, "ARPROT": {"actual": "s00_axi_arprot"}, "ARREADY": {"actual": "s00_axi_arready"}, "ARVALID": {"actual": "s00_axi_arvalid"}, "AWADDR": {"actual": "s00_axi_awaddr"}, "AWPROT": {"actual": "s00_axi_awprot"}, "AWREADY": {"actual": "s00_axi_awready"}, "AWVALID": {"actual": "s00_axi_awvalid"}, "BREADY": {"actual": "s00_axi_bready"}, "BRESP": {"actual": "s00_axi_bresp"}, "BVALID": {"actual": "s00_axi_bvalid"}, "RDATA": {"actual": "s00_axi_rdata"}, "RREADY": {"actual": "s00_axi_rready"}, "RRESP": {"actual": "s00_axi_rresp"}, "RVALID": {"actual": "s00_axi_rvalid"}, "WDATA": {"actual": "s00_axi_wdata"}, "WREADY": {"actual": "s00_axi_wready"}, "WSTRB": {"actual": "s00_axi_wstrb"}, "WVALID": {"actual": "s00_axi_wvalid"}}}, "/axi_smc/M00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "M00_AXI_araddr"}, "ARBURST": {"actual": "M00_AXI_arburst"}, "ARCACHE": {"actual": "M00_AXI_arcache"}, "ARLEN": {"actual": "M00_AXI_arlen"}, "ARLOCK": {"actual": "M00_AXI_arlock"}, "ARPROT": {"actual": "M00_AXI_arprot"}, "ARQOS": {"actual": "M00_AXI_arqos"}, "ARREADY": {"actual": "M00_AXI_arready"}, "ARSIZE": {"actual": "M00_AXI_arsize"}, "ARVALID": {"actual": "M00_AXI_arvalid"}, "RDATA": {"actual": "M00_AXI_rdata"}, "RLAST": {"actual": "M00_AXI_rlast"}, "RREADY": {"actual": "M00_AXI_rready"}, "RRESP": {"actual": "M00_AXI_rresp"}, "RVALID": {"actual": "M00_AXI_rvalid"}}}, "/axi_smc/S00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "S00_AXI_araddr"}, "ARBURST": {"actual": "S00_AXI_arburst"}, "ARCACHE": {"actual": "S00_AXI_arcache"}, "ARLEN": {"actual": "S00_AXI_arlen"}, "ARLOCK": {"actual": "S00_AXI_arlock"}, "ARPROT": {"actual": "S00_AXI_arprot"}, "ARQOS": {"actual": "S00_AXI_arqos"}, "ARREADY": {"actual": "S00_AXI_arready"}, "ARSIZE": {"actual": "S00_AXI_arsize"}, "ARVALID": {"actual": "S00_AXI_arvalid"}, "RDATA": {"actual": "S00_AXI_rdata"}, "RLAST": {"actual": "S00_AXI_rlast"}, "RREADY": {"actual": "S00_AXI_rready"}, "RRESP": {"actual": "S00_AXI_rresp"}, "RVALID": {"actual": "S00_AXI_rvalid"}}}, "/axi_smc1/M00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "M00_AXI_araddr"}, "ARBURST": {"actual": "M00_AXI_arburst"}, "ARCACHE": {"actual": "M00_AXI_arcache"}, "ARLEN": {"actual": "M00_AXI_arlen"}, "ARLOCK": {"actual": "M00_AXI_arlock"}, "ARPROT": {"actual": "M00_AXI_arprot"}, "ARQOS": {"actual": "M00_AXI_arqos"}, "ARREADY": {"actual": "M00_AXI_arready"}, "ARSIZE": {"actual": "M00_AXI_arsize"}, "ARVALID": {"actual": "M00_AXI_arvalid"}, "AWADDR": {"actual": "M00_AXI_awaddr"}, "AWBURST": {"actual": "M00_AXI_awburst"}, "AWCACHE": {"actual": "M00_AXI_awcache"}, "AWLEN": {"actual": "M00_AXI_awlen"}, "AWLOCK": {"actual": "M00_AXI_awlock"}, "AWPROT": {"actual": "M00_AXI_awprot"}, "AWQOS": {"actual": "M00_AXI_awqos"}, "AWREADY": {"actual": "M00_AXI_awready"}, "AWSIZE": {"actual": "M00_AXI_awsize"}, "AWVALID": {"actual": "M00_AXI_awvalid"}, "BREADY": {"actual": "M00_AXI_bready"}, "BRESP": {"actual": "M00_AXI_bresp"}, "BVALID": {"actual": "M00_AXI_bvalid"}, "RDATA": {"actual": "M00_AXI_rdata"}, "RLAST": {"actual": "M00_AXI_rlast"}, "RREADY": {"actual": "M00_AXI_rready"}, "RRESP": {"actual": "M00_AXI_rresp"}, "RVALID": {"actual": "M00_AXI_rvalid"}, "WDATA": {"actual": "M00_AXI_wdata"}, "WLAST": {"actual": "M00_AXI_wlast"}, "WREADY": {"actual": "M00_AXI_wready"}, "WSTRB": {"actual": "M00_AXI_wstrb"}, "WVALID": {"actual": "M00_AXI_wvalid"}}}, "/axi_smc1/S00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "S00_AXI_araddr"}, "ARBURST": {"actual": "S00_AXI_arburst"}, "ARCACHE": {"actual": "S00_AXI_arcache"}, "ARID": {"actual": "S00_AXI_arid"}, "ARLEN": {"actual": "S00_AXI_arlen"}, "ARLOCK": {"actual": "S00_AXI_arlock"}, "ARPROT": {"actual": "S00_AXI_arprot"}, "ARQOS": {"actual": "S00_AXI_arqos"}, "ARREADY": {"actual": "S00_AXI_arready"}, "ARSIZE": {"actual": "S00_AXI_arsize"}, "ARVALID": {"actual": "S00_AXI_arvalid"}, "AWADDR": {"actual": "S00_AXI_awaddr"}, "AWBURST": {"actual": "S00_AXI_awburst"}, "AWCACHE": {"actual": "S00_AXI_awcache"}, "AWID": {"actual": "S00_AXI_awid"}, "AWLEN": {"actual": "S00_AXI_awlen"}, "AWLOCK": {"actual": "S00_AXI_awlock"}, "AWPROT": {"actual": "S00_AXI_awprot"}, "AWQOS": {"actual": "S00_AXI_awqos"}, "AWREADY": {"actual": "S00_AXI_awready"}, "AWSIZE": {"actual": "S00_AXI_awsize"}, "AWVALID": {"actual": "S00_AXI_awvalid"}, "BID": {"actual": "S00_AXI_bid"}, "BREADY": {"actual": "S00_AXI_bready"}, "BRESP": {"actual": "S00_AXI_bresp"}, "BVALID": {"actual": "S00_AXI_bvalid"}, "RDATA": {"actual": "S00_AXI_rdata"}, "RID": {"actual": "S00_AXI_rid"}, "RLAST": {"actual": "S00_AXI_rlast"}, "RREADY": {"actual": "S00_AXI_rready"}, "RRESP": {"actual": "S00_AXI_rresp"}, "RVALID": {"actual": "S00_AXI_rvalid"}, "WDATA": {"actual": "S00_AXI_wdata"}, "WLAST": {"actual": "S00_AXI_wlast"}, "WREADY": {"actual": "S00_AXI_wready"}, "WSTRB": {"actual": "S00_AXI_wstrb"}, "WVALID": {"actual": "S00_AXI_wvalid"}}}, "/axi_vdma_0/M_AXIS_MM2S": {"interface": "xilinx.com:interface:axis:1.0", "ports": {"ACLK": {"actual": "m_axis_mm2s_aclk"}, "TDATA": {"actual": "m_axis_mm2s_tdata"}, "TKEEP": {"actual": "m_axis_mm2s_tkeep"}, "TLAST": {"actual": "m_axis_mm2s_tlast"}, "TREADY": {"actual": "m_axis_mm2s_tready"}, "TUSER": {"actual": "m_axis_mm2s_tuser"}, "TVALID": {"actual": "m_axis_mm2s_tvalid"}}}, "/axi_vdma_0/M_AXI_MM2S": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "m_axi_mm2s_aclk"}, "ARADDR": {"actual": "m_axi_mm2s_araddr"}, "ARBURST": {"actual": "m_axi_mm2s_arburst"}, "ARCACHE": {"actual": "m_axi_mm2s_arcache"}, "ARLEN": {"actual": "m_axi_mm2s_arlen"}, "ARPROT": {"actual": "m_axi_mm2s_arprot"}, "ARREADY": {"actual": "m_axi_mm2s_arready"}, "ARSIZE": {"actual": "m_axi_mm2s_arsize"}, "ARVALID": {"actual": "m_axi_mm2s_arvalid"}, "RDATA": {"actual": "m_axi_mm2s_rdata"}, "RLAST": {"actual": "m_axi_mm2s_rlast"}, "RREADY": {"actual": "m_axi_mm2s_rready"}, "RRESP": {"actual": "m_axi_mm2s_rresp"}, "RVALID": {"actual": "m_axi_mm2s_rvalid"}}}, "/axi_vdma_0/S_AXI_LITE": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "s_axi_lite_aclk"}, "ARADDR": {"actual": "s_axi_lite_araddr"}, "ARESETN": {"actual": "axi_resetn"}, "ARREADY": {"actual": "s_axi_lite_arready"}, "ARVALID": {"actual": "s_axi_lite_arvalid"}, "AWADDR": {"actual": "s_axi_lite_awaddr"}, "AWREADY": {"actual": "s_axi_lite_awready"}, "AWVALID": {"actual": "s_axi_lite_awvalid"}, "BREADY": {"actual": "s_axi_lite_bready"}, "BRESP": {"actual": "s_axi_lite_bresp"}, "BVALID": {"actual": "s_axi_lite_bvalid"}, "RDATA": {"actual": "s_axi_lite_rdata"}, "RREADY": {"actual": "s_axi_lite_rready"}, "RRESP": {"actual": "s_axi_lite_rresp"}, "RVALID": {"actual": "s_axi_lite_rvalid"}, "WDATA": {"actual": "s_axi_lite_wdata"}, "WREADY": {"actual": "s_axi_lite_wready"}, "WVALID": {"actual": "s_axi_lite_wvalid"}}}, "/misc_0/S00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "s00_axi_aclk"}, "ARADDR": {"actual": "s00_axi_araddr"}, "ARESETN": {"actual": "s00_axi_aresetn"}, "ARPROT": {"actual": "s00_axi_arprot"}, "ARREADY": {"actual": "s00_axi_arready"}, "ARVALID": {"actual": "s00_axi_arvalid"}, "AWADDR": {"actual": "s00_axi_awaddr"}, "AWPROT": {"actual": "s00_axi_awprot"}, "AWREADY": {"actual": "s00_axi_awready"}, "AWVALID": {"actual": "s00_axi_awvalid"}, "BREADY": {"actual": "s00_axi_bready"}, "BRESP": {"actual": "s00_axi_bresp"}, "BVALID": {"actual": "s00_axi_bvalid"}, "RDATA": {"actual": "s00_axi_rdata"}, "RREADY": {"actual": "s00_axi_rready"}, "RRESP": {"actual": "s00_axi_rresp"}, "RVALID": {"actual": "s00_axi_rvalid"}, "WDATA": {"actual": "s00_axi_wdata"}, "WREADY": {"actual": "s00_axi_wready"}, "WSTRB": {"actual": "s00_axi_wstrb"}, "WVALID": {"actual": "s00_axi_wvalid"}}}, "/processing_system7_0/M_AXI_GP0": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M_AXI_GP0_ACLK"}, "ARADDR": {"actual": "M_AXI_GP0_ARADDR"}, "ARBURST": {"actual": "M_AXI_GP0_ARBURST"}, "ARCACHE": {"actual": "M_AXI_GP0_ARCACHE"}, "ARID": {"actual": "M_AXI_GP0_ARID"}, "ARLEN": {"actual": "M_AXI_GP0_ARLEN"}, "ARLOCK": {"actual": "M_AXI_GP0_ARLOCK"}, "ARPROT": {"actual": "M_AXI_GP0_ARPROT"}, "ARQOS": {"actual": "M_AXI_GP0_ARQOS"}, "ARREADY": {"actual": "M_AXI_GP0_ARREADY"}, "ARSIZE": {"actual": "M_AXI_GP0_ARSIZE"}, "ARVALID": {"actual": "M_AXI_GP0_ARVALID"}, "AWADDR": {"actual": "M_AXI_GP0_AWADDR"}, "AWBURST": {"actual": "M_AXI_GP0_AWBURST"}, "AWCACHE": {"actual": "M_AXI_GP0_AWCACHE"}, "AWID": {"actual": "M_AXI_GP0_AWID"}, "AWLEN": {"actual": "M_AXI_GP0_AWLEN"}, "AWLOCK": {"actual": "M_AXI_GP0_AWLOCK"}, "AWPROT": {"actual": "M_AXI_GP0_AWPROT"}, "AWQOS": {"actual": "M_AXI_GP0_AWQOS"}, "AWREADY": {"actual": "M_AXI_GP0_AWREADY"}, "AWSIZE": {"actual": "M_AXI_GP0_AWSIZE"}, "AWVALID": {"actual": "M_AXI_GP0_AWVALID"}, "BID": {"actual": "M_AXI_GP0_BID"}, "BREADY": {"actual": "M_AXI_GP0_BREADY"}, "BRESP": {"actual": "M_AXI_GP0_BRESP"}, "BVALID": {"actual": "M_AXI_GP0_BVALID"}, "RDATA": {"actual": "M_AXI_GP0_RDATA"}, "RID": {"actual": "M_AXI_GP0_RID"}, "RLAST": {"actual": "M_AXI_GP0_RLAST"}, "RREADY": {"actual": "M_AXI_GP0_RREADY"}, "RRESP": {"actual": "M_AXI_GP0_RRESP"}, "RVALID": {"actual": "M_AXI_GP0_RVALID"}, "WDATA": {"actual": "M_AXI_GP0_WDATA"}, "WID": {"actual": "M_AXI_GP0_WID"}, "WLAST": {"actual": "M_AXI_GP0_WLAST"}, "WREADY": {"actual": "M_AXI_GP0_WREADY"}, "WSTRB": {"actual": "M_AXI_GP0_WSTRB"}, "WVALID": {"actual": "M_AXI_GP0_WVALID"}}}, "/processing_system7_0/S_AXI_HP0": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_AXI_HP0_ACLK"}, "ARADDR": {"actual": "S_AXI_HP0_ARADDR"}, "ARBURST": {"actual": "S_AXI_HP0_ARBURST"}, "ARCACHE": {"actual": "S_AXI_HP0_ARCACHE"}, "ARID": {"actual": "S_AXI_HP0_ARID"}, "ARLEN": {"actual": "S_AXI_HP0_ARLEN"}, "ARLOCK": {"actual": "S_AXI_HP0_ARLOCK"}, "ARPROT": {"actual": "S_AXI_HP0_ARPROT"}, "ARQOS": {"actual": "S_AXI_HP0_ARQOS"}, "ARREADY": {"actual": "S_AXI_HP0_ARREADY"}, "ARSIZE": {"actual": "S_AXI_HP0_ARSIZE"}, "ARVALID": {"actual": "S_AXI_HP0_ARVALID"}, "AWADDR": {"actual": "S_AXI_HP0_AWADDR"}, "AWBURST": {"actual": "S_AXI_HP0_AWBURST"}, "AWCACHE": {"actual": "S_AXI_HP0_AWCACHE"}, "AWID": {"actual": "S_AXI_HP0_AWID"}, "AWLEN": {"actual": "S_AXI_HP0_AWLEN"}, "AWLOCK": {"actual": "S_AXI_HP0_AWLOCK"}, "AWPROT": {"actual": "S_AXI_HP0_AWPROT"}, "AWQOS": {"actual": "S_AXI_HP0_AWQOS"}, "AWREADY": {"actual": "S_AXI_HP0_AWREADY"}, "AWSIZE": {"actual": "S_AXI_HP0_AWSIZE"}, "AWVALID": {"actual": "S_AXI_HP0_AWVALID"}, "BID": {"actual": "S_AXI_HP0_BID"}, "BREADY": {"actual": "S_AXI_HP0_BREADY"}, "BRESP": {"actual": "S_AXI_HP0_BRESP"}, "BVALID": {"actual": "S_AXI_HP0_BVALID"}, "RDATA": {"actual": "S_AXI_HP0_RDATA"}, "RID": {"actual": "S_AXI_HP0_RID"}, "RLAST": {"actual": "S_AXI_HP0_RLAST"}, "RREADY": {"actual": "S_AXI_HP0_RREADY"}, "RRESP": {"actual": "S_AXI_HP0_RRESP"}, "RVALID": {"actual": "S_AXI_HP0_RVALID"}, "WDATA": {"actual": "S_AXI_HP0_WDATA"}, "WID": {"actual": "S_AXI_HP0_WID"}, "WLAST": {"actual": "S_AXI_HP0_WLAST"}, "WREADY": {"actual": "S_AXI_HP0_WREADY"}, "WSTRB": {"actual": "S_AXI_HP0_WSTRB"}, "WVALID": {"actual": "S_AXI_HP0_WVALID"}}}, "/processing_system7_0/S_AXI_HP1": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_AXI_HP1_ACLK"}, "ARADDR": {"actual": "S_AXI_HP1_ARADDR"}, "ARBURST": {"actual": "S_AXI_HP1_ARBURST"}, "ARCACHE": {"actual": "S_AXI_HP1_ARCACHE"}, "ARID": {"actual": "S_AXI_HP1_ARID"}, "ARLEN": {"actual": "S_AXI_HP1_ARLEN"}, "ARLOCK": {"actual": "S_AXI_HP1_ARLOCK"}, "ARPROT": {"actual": "S_AXI_HP1_ARPROT"}, "ARQOS": {"actual": "S_AXI_HP1_ARQOS"}, "ARREADY": {"actual": "S_AXI_HP1_ARREADY"}, "ARSIZE": {"actual": "S_AXI_HP1_ARSIZE"}, "ARVALID": {"actual": "S_AXI_HP1_ARVALID"}, "AWADDR": {"actual": "S_AXI_HP1_AWADDR"}, "AWBURST": {"actual": "S_AXI_HP1_AWBURST"}, "AWCACHE": {"actual": "S_AXI_HP1_AWCACHE"}, "AWID": {"actual": "S_AXI_HP1_AWID"}, "AWLEN": {"actual": "S_AXI_HP1_AWLEN"}, "AWLOCK": {"actual": "S_AXI_HP1_AWLOCK"}, "AWPROT": {"actual": "S_AXI_HP1_AWPROT"}, "AWQOS": {"actual": "S_AXI_HP1_AWQOS"}, "AWREADY": {"actual": "S_AXI_HP1_AWREADY"}, "AWSIZE": {"actual": "S_AXI_HP1_AWSIZE"}, "AWVALID": {"actual": "S_AXI_HP1_AWVALID"}, "BID": {"actual": "S_AXI_HP1_BID"}, "BREADY": {"actual": "S_AXI_HP1_BREADY"}, "BRESP": {"actual": "S_AXI_HP1_BRESP"}, "BVALID": {"actual": "S_AXI_HP1_BVALID"}, "RDATA": {"actual": "S_AXI_HP1_RDATA"}, "RID": {"actual": "S_AXI_HP1_RID"}, "RLAST": {"actual": "S_AXI_HP1_RLAST"}, "RREADY": {"actual": "S_AXI_HP1_RREADY"}, "RRESP": {"actual": "S_AXI_HP1_RRESP"}, "RVALID": {"actual": "S_AXI_HP1_RVALID"}, "WDATA": {"actual": "S_AXI_HP1_WDATA"}, "WID": {"actual": "S_AXI_HP1_WID"}, "WLAST": {"actual": "S_AXI_HP1_WLAST"}, "WREADY": {"actual": "S_AXI_HP1_WREADY"}, "WSTRB": {"actual": "S_AXI_HP1_WSTRB"}, "WVALID": {"actual": "S_AXI_HP1_WVALID"}}}, "/ps7_0_axi_periph/M00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M00_ACLK"}, "ARADDR": {"actual": "M00_AXI_araddr[31:0]"}, "ARESETN": {"actual": "ARESETN"}, "ARREADY": {"actual": "M00_AXI_arready[0:0]"}, "ARVALID": {"actual": "M00_AXI_arvalid[0:0]"}, "AWADDR": {"actual": "M00_AXI_awaddr[31:0]"}, "AWREADY": {"actual": "M00_AXI_awready[0:0]"}, "AWVALID": {"actual": "M00_AXI_awvalid[0:0]"}, "BREADY": {"actual": "M00_AXI_bready[0:0]"}, "BRESP": {"actual": "M00_AXI_bresp[1:0]"}, "BVALID": {"actual": "M00_AXI_bvalid[0:0]"}, "RDATA": {"actual": "M00_AXI_rdata[31:0]"}, "RREADY": {"actual": "M00_AXI_rready[0:0]"}, "RRESP": {"actual": "M00_AXI_rresp[1:0]"}, "RVALID": {"actual": "M00_AXI_rvalid[0:0]"}, "WDATA": {"actual": "M00_AXI_wdata[31:0]"}, "WREADY": {"actual": "M00_AXI_wready[0:0]"}, "WVALID": {"actual": "M00_AXI_wvalid[0:0]"}}}, "/ps7_0_axi_periph/M01_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M01_ACLK"}, "ARADDR": {"actual": "M01_AXI_araddr[63:32]"}, "ARESETN": {"actual": "ARESETN"}, "ARREADY": {"actual": "M01_AXI_arready[1:1]"}, "ARVALID": {"actual": "M01_AXI_arvalid[1:1]"}, "AWADDR": {"actual": "M01_AXI_awaddr[63:32]"}, "AWREADY": {"actual": "M01_AXI_awready[1:1]"}, "AWVALID": {"actual": "M01_AXI_awvalid[1:1]"}, "BREADY": {"actual": "M01_AXI_bready[1:1]"}, "BRESP": {"actual": "M01_AXI_bresp[3:2]"}, "BVALID": {"actual": "M01_AXI_bvalid[1:1]"}, "RDATA": {"actual": "M01_AXI_rdata[63:32]"}, "RREADY": {"actual": "M01_AXI_rready[1:1]"}, "RRESP": {"actual": "M01_AXI_rresp[3:2]"}, "RVALID": {"actual": "M01_AXI_rvalid[1:1]"}, "WDATA": {"actual": "M01_AXI_wdata[63:32]"}, "WREADY": {"actual": "M01_AXI_wready[1:1]"}, "WSTRB": {"actual": "M01_AXI_wstrb[7:4]"}, "WVALID": {"actual": "M01_AXI_wvalid[1:1]"}}}, "/ps7_0_axi_periph/M02_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M02_ACLK"}, "ARADDR": {"actual": "M02_AXI_araddr[95:64]"}, "ARESETN": {"actual": "ARESETN"}, "ARPROT": {"actual": "M02_AXI_arprot[8:6]"}, "ARREADY": {"actual": "M02_AXI_arready"}, "ARVALID": {"actual": "M02_AXI_arvalid"}, "AWADDR": {"actual": "M02_AXI_awaddr[95:64]"}, "AWPROT": {"actual": "M02_AXI_awprot[8:6]"}, "AWREADY": {"actual": "M02_AXI_awready"}, "AWVALID": {"actual": "M02_AXI_awvalid"}, "BREADY": {"actual": "M02_AXI_bready"}, "BRESP": {"actual": "M02_AXI_bresp[5:4]"}, "BVALID": {"actual": "M02_AXI_bvalid"}, "RDATA": {"actual": "M02_AXI_rdata[95:64]"}, "RREADY": {"actual": "M02_AXI_rready"}, "RRESP": {"actual": "M02_AXI_rresp[5:4]"}, "RVALID": {"actual": "M02_AXI_rvalid"}, "WDATA": {"actual": "M02_AXI_wdata[95:64]"}, "WREADY": {"actual": "M02_AXI_wready"}, "WSTRB": {"actual": "M02_AXI_wstrb[11:8]"}, "WVALID": {"actual": "M02_AXI_wvalid"}}}, "/ps7_0_axi_periph/M03_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M03_ACLK"}, "ARADDR": {"actual": "M03_AXI_araddr[127:96]"}, "ARESETN": {"actual": "ARESETN"}, "ARPROT": {"actual": "M03_AXI_arprot[11:9]"}, "ARREADY": {"actual": "M03_AXI_arready"}, "ARVALID": {"actual": "M03_AXI_arvalid"}, "AWADDR": {"actual": "M03_AXI_awaddr[127:96]"}, "AWPROT": {"actual": "M03_AXI_awprot[11:9]"}, "AWREADY": {"actual": "M03_AXI_awready"}, "AWVALID": {"actual": "M03_AXI_awvalid"}, "BREADY": {"actual": "M03_AXI_bready"}, "BRESP": {"actual": "M03_AXI_bresp[7:6]"}, "BVALID": {"actual": "M03_AXI_bvalid"}, "RDATA": {"actual": "M03_AXI_rdata[127:96]"}, "RREADY": {"actual": "M03_AXI_rready"}, "RRESP": {"actual": "M03_AXI_rresp[7:6]"}, "RVALID": {"actual": "M03_AXI_rvalid"}, "WDATA": {"actual": "M03_AXI_wdata[127:96]"}, "WREADY": {"actual": "M03_AXI_wready"}, "WSTRB": {"actual": "M03_AXI_wstrb[15:12]"}, "WVALID": {"actual": "M03_AXI_wvalid"}}}, "/ps7_0_axi_periph/M04_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M04_ACLK"}, "ARADDR": {"actual": "M04_AXI_araddr[159:128]"}, "ARESETN": {"actual": "ARESETN"}, "ARPROT": {"actual": "M04_AXI_arprot[14:12]"}, "ARREADY": {"actual": "M04_AXI_arready"}, "ARVALID": {"actual": "M04_AXI_arvalid"}, "AWADDR": {"actual": "M04_AXI_awaddr[159:128]"}, "AWPROT": {"actual": "M04_AXI_awprot[14:12]"}, "AWREADY": {"actual": "M04_AXI_awready"}, "AWVALID": {"actual": "M04_AXI_awvalid"}, "BREADY": {"actual": "M04_AXI_bready"}, "BRESP": {"actual": "M04_AXI_bresp[9:8]"}, "BVALID": {"actual": "M04_AXI_bvalid"}, "RDATA": {"actual": "M04_AXI_rdata[159:128]"}, "RREADY": {"actual": "M04_AXI_rready"}, "RRESP": {"actual": "M04_AXI_rresp[9:8]"}, "RVALID": {"actual": "M04_AXI_rvalid"}, "WDATA": {"actual": "M04_AXI_wdata[159:128]"}, "WREADY": {"actual": "M04_AXI_wready"}, "WSTRB": {"actual": "M04_AXI_wstrb[19:16]"}, "WVALID": {"actual": "M04_AXI_wvalid"}}}, "/ps7_0_axi_periph/S00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S00_ACLK"}, "ARADDR": {"actual": "S00_AXI_araddr"}, "ARBURST": {"actual": "S00_AXI_arburst"}, "ARCACHE": {"actual": "S00_AXI_arcache"}, "ARESETN": {"actual": "ARESETN"}, "ARID": {"actual": "S00_AXI_arid"}, "ARLEN": {"actual": "S00_AXI_arlen"}, "ARLOCK": {"actual": "S00_AXI_arlock"}, "ARPROT": {"actual": "S00_AXI_arprot"}, "ARQOS": {"actual": "S00_AXI_arqos"}, "ARREADY": {"actual": "S00_AXI_arready"}, "ARSIZE": {"actual": "S00_AXI_arsize"}, "ARVALID": {"actual": "S00_AXI_arvalid"}, "AWADDR": {"actual": "S00_AXI_awaddr"}, "AWBURST": {"actual": "S00_AXI_awburst"}, "AWCACHE": {"actual": "S00_AXI_awcache"}, "AWID": {"actual": "S00_AXI_awid"}, "AWLEN": {"actual": "S00_AXI_awlen"}, "AWLOCK": {"actual": "S00_AXI_awlock"}, "AWPROT": {"actual": "S00_AXI_awprot"}, "AWQOS": {"actual": "S00_AXI_awqos"}, "AWREADY": {"actual": "S00_AXI_awready"}, "AWSIZE": {"actual": "S00_AXI_awsize"}, "AWVALID": {"actual": "S00_AXI_awvalid"}, "BID": {"actual": "S00_AXI_bid"}, "BREADY": {"actual": "S00_AXI_bready"}, "BRESP": {"actual": "S00_AXI_bresp"}, "BVALID": {"actual": "S00_AXI_bvalid"}, "RDATA": {"actual": "S00_AXI_rdata"}, "RID": {"actual": "S00_AXI_rid"}, "RLAST": {"actual": "S00_AXI_rlast"}, "RREADY": {"actual": "S00_AXI_rready"}, "RRESP": {"actual": "S00_AXI_rresp"}, "RVALID": {"actual": "S00_AXI_rvalid"}, "WDATA": {"actual": "S00_AXI_wdata"}, "WID": {"actual": "S00_AXI_wid"}, "WLAST": {"actual": "S00_AXI_wlast"}, "WREADY": {"actual": "S00_AXI_wready"}, "WSTRB": {"actual": "S00_AXI_wstrb"}, "WVALID": {"actual": "S00_AXI_wvalid"}}}, "/ps7_0_axi_periph/m00_couplers/M_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M_ACLK"}, "ARADDR": {"actual": "M_AXI_araddr[31:0]"}, "ARESETN": {"actual": "M_ARESETN"}, "ARREADY": {"actual": "M_AXI_arready[0:0]"}, "ARVALID": {"actual": "M_AXI_arvalid[0:0]"}, "AWADDR": {"actual": "M_AXI_awaddr[31:0]"}, "AWREADY": {"actual": "M_AXI_awready[0:0]"}, "AWVALID": {"actual": "M_AXI_awvalid[0:0]"}, "BREADY": {"actual": "M_AXI_bready[0:0]"}, "BRESP": {"actual": "M_AXI_bresp[1:0]"}, "BVALID": {"actual": "M_AXI_bvalid[0:0]"}, "RDATA": {"actual": "M_AXI_rdata[31:0]"}, "RREADY": {"actual": "M_AXI_rready[0:0]"}, "RRESP": {"actual": "M_AXI_rresp[1:0]"}, "RVALID": {"actual": "M_AXI_rvalid[0:0]"}, "WDATA": {"actual": "M_AXI_wdata[31:0]"}, "WREADY": {"actual": "M_AXI_wready[0:0]"}, "WVALID": {"actual": "M_AXI_wvalid[0:0]"}}}, "/ps7_0_axi_periph/m00_couplers/S_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_ACLK"}, "ARADDR": {"actual": "S_AXI_araddr[31:0]"}, "ARESETN": {"actual": "S_ARESETN"}, "ARREADY": {"actual": "S_AXI_arready[0:0]"}, "ARVALID": {"actual": "S_AXI_arvalid[0:0]"}, "AWADDR": {"actual": "S_AXI_awaddr[31:0]"}, "AWREADY": {"actual": "S_AXI_awready[0:0]"}, "AWVALID": {"actual": "S_AXI_awvalid[0:0]"}, "BREADY": {"actual": "S_AXI_bready[0:0]"}, "BRESP": {"actual": "S_AXI_bresp[1:0]"}, "BVALID": {"actual": "S_AXI_bvalid[0:0]"}, "RDATA": {"actual": "S_AXI_rdata[31:0]"}, "RREADY": {"actual": "S_AXI_rready[0:0]"}, "RRESP": {"actual": "S_AXI_rresp[1:0]"}, "RVALID": {"actual": "S_AXI_rvalid[0:0]"}, "WDATA": {"actual": "S_AXI_wdata[31:0]"}, "WREADY": {"actual": "S_AXI_wready[0:0]"}, "WVALID": {"actual": "S_AXI_wvalid[0:0]"}}}, "/ps7_0_axi_periph/m01_couplers/M_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M_ACLK"}, "ARADDR": {"actual": "M_AXI_araddr[63:32]"}, "ARESETN": {"actual": "M_ARESETN"}, "ARREADY": {"actual": "M_AXI_arready[1:1]"}, "ARVALID": {"actual": "M_AXI_arvalid[1:1]"}, "AWADDR": {"actual": "M_AXI_awaddr[63:32]"}, "AWREADY": {"actual": "M_AXI_awready[1:1]"}, "AWVALID": {"actual": "M_AXI_awvalid[1:1]"}, "BREADY": {"actual": "M_AXI_bready[1:1]"}, "BRESP": {"actual": "M_AXI_bresp[3:2]"}, "BVALID": {"actual": "M_AXI_bvalid[1:1]"}, "RDATA": {"actual": "M_AXI_rdata[63:32]"}, "RREADY": {"actual": "M_AXI_rready[1:1]"}, "RRESP": {"actual": "M_AXI_rresp[3:2]"}, "RVALID": {"actual": "M_AXI_rvalid[1:1]"}, "WDATA": {"actual": "M_AXI_wdata[63:32]"}, "WREADY": {"actual": "M_AXI_wready[1:1]"}, "WSTRB": {"actual": "M_AXI_wstrb[7:4]"}, "WVALID": {"actual": "M_AXI_wvalid[1:1]"}}}, "/ps7_0_axi_periph/m01_couplers/S_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_ACLK"}, "ARADDR": {"actual": "S_AXI_araddr[63:32]"}, "ARESETN": {"actual": "S_ARESETN"}, "ARREADY": {"actual": "S_AXI_arready[1:1]"}, "ARVALID": {"actual": "S_AXI_arvalid[1:1]"}, "AWADDR": {"actual": "S_AXI_awaddr[63:32]"}, "AWREADY": {"actual": "S_AXI_awready[1:1]"}, "AWVALID": {"actual": "S_AXI_awvalid[1:1]"}, "BREADY": {"actual": "S_AXI_bready[1:1]"}, "BRESP": {"actual": "S_AXI_bresp[3:2]"}, "BVALID": {"actual": "S_AXI_bvalid[1:1]"}, "RDATA": {"actual": "S_AXI_rdata[63:32]"}, "RREADY": {"actual": "S_AXI_rready[1:1]"}, "RRESP": {"actual": "S_AXI_rresp[3:2]"}, "RVALID": {"actual": "S_AXI_rvalid[1:1]"}, "WDATA": {"actual": "S_AXI_wdata[63:32]"}, "WREADY": {"actual": "S_AXI_wready[1:1]"}, "WSTRB": {"actual": "S_AXI_wstrb[7:4]"}, "WVALID": {"actual": "S_AXI_wvalid[1:1]"}}}, "/ps7_0_axi_periph/m02_couplers/M_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M_ACLK"}, "ARADDR": {"actual": "M_AXI_araddr[95:64]"}, "ARESETN": {"actual": "M_ARESETN"}, "ARPROT": {"actual": "M_AXI_arprot[8:6]"}, "ARREADY": {"actual": "M_AXI_arready"}, "ARVALID": {"actual": "M_AXI_arvalid"}, "AWADDR": {"actual": "M_AXI_awaddr[95:64]"}, "AWPROT": {"actual": "M_AXI_awprot[8:6]"}, "AWREADY": {"actual": "M_AXI_awready"}, "AWVALID": {"actual": "M_AXI_awvalid"}, "BREADY": {"actual": "M_AXI_bready"}, "BRESP": {"actual": "M_AXI_bresp[5:4]"}, "BVALID": {"actual": "M_AXI_bvalid"}, "RDATA": {"actual": "M_AXI_rdata[95:64]"}, "RREADY": {"actual": "M_AXI_rready"}, "RRESP": {"actual": "M_AXI_rresp[5:4]"}, "RVALID": {"actual": "M_AXI_rvalid"}, "WDATA": {"actual": "M_AXI_wdata[95:64]"}, "WREADY": {"actual": "M_AX<PERSON>_wready"}, "WSTRB": {"actual": "M_AXI_wstrb[11:8]"}, "WVALID": {"actual": "M_AXI_wvalid"}}}, "/ps7_0_axi_periph/m02_couplers/S_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_ACLK"}, "ARADDR": {"actual": "S_AXI_araddr[95:64]"}, "ARESETN": {"actual": "S_ARESETN"}, "ARPROT": {"actual": "S_AXI_arprot[8:6]"}, "ARREADY": {"actual": "S_AXI_arready"}, "ARVALID": {"actual": "S_AXI_arvalid"}, "AWADDR": {"actual": "S_AXI_awaddr[95:64]"}, "AWPROT": {"actual": "S_AXI_awprot[8:6]"}, "AWREADY": {"actual": "S_AXI_awready"}, "AWVALID": {"actual": "S_AXI_awvalid"}, "BREADY": {"actual": "S_AXI_bready"}, "BRESP": {"actual": "S_AXI_bresp[5:4]"}, "BVALID": {"actual": "S_AXI_bvalid"}, "RDATA": {"actual": "S_AXI_rdata[95:64]"}, "RREADY": {"actual": "S_AXI_rready"}, "RRESP": {"actual": "S_AXI_rresp[5:4]"}, "RVALID": {"actual": "S_AXI_rvalid"}, "WDATA": {"actual": "S_AXI_wdata[95:64]"}, "WREADY": {"actual": "S_AXI_wready"}, "WSTRB": {"actual": "S_AXI_wstrb[11:8]"}, "WVALID": {"actual": "S_AXI_wvalid"}}}, "/ps7_0_axi_periph/m03_couplers/M_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M_ACLK"}, "ARADDR": {"actual": "M_AXI_araddr[127:96]"}, "ARESETN": {"actual": "M_ARESETN"}, "ARPROT": {"actual": "M_AXI_arprot[11:9]"}, "ARREADY": {"actual": "M_AXI_arready"}, "ARVALID": {"actual": "M_AXI_arvalid"}, "AWADDR": {"actual": "M_AXI_awaddr[127:96]"}, "AWPROT": {"actual": "M_AXI_awprot[11:9]"}, "AWREADY": {"actual": "M_AXI_awready"}, "AWVALID": {"actual": "M_AXI_awvalid"}, "BREADY": {"actual": "M_AXI_bready"}, "BRESP": {"actual": "M_AXI_bresp[7:6]"}, "BVALID": {"actual": "M_AXI_bvalid"}, "RDATA": {"actual": "M_AXI_rdata[127:96]"}, "RREADY": {"actual": "M_AXI_rready"}, "RRESP": {"actual": "M_AXI_rresp[7:6]"}, "RVALID": {"actual": "M_AXI_rvalid"}, "WDATA": {"actual": "M_AXI_wdata[127:96]"}, "WREADY": {"actual": "M_AX<PERSON>_wready"}, "WSTRB": {"actual": "M_AXI_wstrb[15:12]"}, "WVALID": {"actual": "M_AXI_wvalid"}}}, "/ps7_0_axi_periph/m03_couplers/S_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_ACLK"}, "ARADDR": {"actual": "S_AXI_araddr[127:96]"}, "ARESETN": {"actual": "S_ARESETN"}, "ARPROT": {"actual": "S_AXI_arprot[11:9]"}, "ARREADY": {"actual": "S_AXI_arready"}, "ARVALID": {"actual": "S_AXI_arvalid"}, "AWADDR": {"actual": "S_AXI_awaddr[127:96]"}, "AWPROT": {"actual": "S_AXI_awprot[11:9]"}, "AWREADY": {"actual": "S_AXI_awready"}, "AWVALID": {"actual": "S_AXI_awvalid"}, "BREADY": {"actual": "S_AXI_bready"}, "BRESP": {"actual": "S_AXI_bresp[7:6]"}, "BVALID": {"actual": "S_AXI_bvalid"}, "RDATA": {"actual": "S_AXI_rdata[127:96]"}, "RREADY": {"actual": "S_AXI_rready"}, "RRESP": {"actual": "S_AXI_rresp[7:6]"}, "RVALID": {"actual": "S_AXI_rvalid"}, "WDATA": {"actual": "S_AXI_wdata[127:96]"}, "WREADY": {"actual": "S_AXI_wready"}, "WSTRB": {"actual": "S_AXI_wstrb[15:12]"}, "WVALID": {"actual": "S_AXI_wvalid"}}}, "/ps7_0_axi_periph/m04_couplers/M_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M_ACLK"}, "ARADDR": {"actual": "M_AXI_araddr[159:128]"}, "ARESETN": {"actual": "M_ARESETN"}, "ARPROT": {"actual": "M_AXI_arprot[14:12]"}, "ARREADY": {"actual": "M_AXI_arready"}, "ARVALID": {"actual": "M_AXI_arvalid"}, "AWADDR": {"actual": "M_AXI_awaddr[159:128]"}, "AWPROT": {"actual": "M_AXI_awprot[14:12]"}, "AWREADY": {"actual": "M_AXI_awready"}, "AWVALID": {"actual": "M_AXI_awvalid"}, "BREADY": {"actual": "M_AXI_bready"}, "BRESP": {"actual": "M_AXI_bresp[9:8]"}, "BVALID": {"actual": "M_AXI_bvalid"}, "RDATA": {"actual": "M_AXI_rdata[159:128]"}, "RREADY": {"actual": "M_AXI_rready"}, "RRESP": {"actual": "M_AXI_rresp[9:8]"}, "RVALID": {"actual": "M_AXI_rvalid"}, "WDATA": {"actual": "M_AXI_wdata[159:128]"}, "WREADY": {"actual": "M_AX<PERSON>_wready"}, "WSTRB": {"actual": "M_AXI_wstrb[19:16]"}, "WVALID": {"actual": "M_AXI_wvalid"}}}, "/ps7_0_axi_periph/m04_couplers/S_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_ACLK"}, "ARADDR": {"actual": "S_AXI_araddr[159:128]"}, "ARESETN": {"actual": "S_ARESETN"}, "ARPROT": {"actual": "S_AXI_arprot[14:12]"}, "ARREADY": {"actual": "S_AXI_arready"}, "ARVALID": {"actual": "S_AXI_arvalid"}, "AWADDR": {"actual": "S_AXI_awaddr[159:128]"}, "AWPROT": {"actual": "S_AXI_awprot[14:12]"}, "AWREADY": {"actual": "S_AXI_awready"}, "AWVALID": {"actual": "S_AXI_awvalid"}, "BREADY": {"actual": "S_AXI_bready"}, "BRESP": {"actual": "S_AXI_bresp[9:8]"}, "BVALID": {"actual": "S_AXI_bvalid"}, "RDATA": {"actual": "S_AXI_rdata[159:128]"}, "RREADY": {"actual": "S_AXI_rready"}, "RRESP": {"actual": "S_AXI_rresp[9:8]"}, "RVALID": {"actual": "S_AXI_rvalid"}, "WDATA": {"actual": "S_AXI_wdata[159:128]"}, "WREADY": {"actual": "S_AXI_wready"}, "WSTRB": {"actual": "S_AXI_wstrb[19:16]"}, "WVALID": {"actual": "S_AXI_wvalid"}}}, "/ps7_0_axi_periph/s00_couplers/M_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "M_ACLK"}, "ARADDR": {"actual": "M_AXI_araddr"}, "ARESETN": {"actual": "M_ARESETN"}, "ARPROT": {"actual": "M_AXI_arprot"}, "ARREADY": {"actual": "M_AXI_arready"}, "ARVALID": {"actual": "M_AXI_arvalid"}, "AWADDR": {"actual": "M_AXI_awaddr"}, "AWPROT": {"actual": "M_AXI_awprot"}, "AWREADY": {"actual": "M_AXI_awready"}, "AWVALID": {"actual": "M_AXI_awvalid"}, "BREADY": {"actual": "M_AXI_bready"}, "BRESP": {"actual": "M_AXI_bresp"}, "BVALID": {"actual": "M_AXI_bvalid"}, "RDATA": {"actual": "M_AXI_rdata"}, "RREADY": {"actual": "M_AXI_rready"}, "RRESP": {"actual": "M_AXI_rresp"}, "RVALID": {"actual": "M_AXI_rvalid"}, "WDATA": {"actual": "M_AXI_wdata"}, "WREADY": {"actual": "M_AX<PERSON>_wready"}, "WSTRB": {"actual": "M_AXI_wstrb"}, "WVALID": {"actual": "M_AXI_wvalid"}}}, "/ps7_0_axi_periph/s00_couplers/S_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "S_ACLK"}, "ARADDR": {"actual": "S_AXI_araddr"}, "ARBURST": {"actual": "S_AXI_arburst"}, "ARCACHE": {"actual": "S_AXI_arcache"}, "ARESETN": {"actual": "S_ARESETN"}, "ARID": {"actual": "S_AXI_arid"}, "ARLEN": {"actual": "S_AXI_arlen"}, "ARLOCK": {"actual": "S_AXI_arlock"}, "ARPROT": {"actual": "S_AXI_arprot"}, "ARQOS": {"actual": "S_AXI_arqos"}, "ARREADY": {"actual": "S_AXI_arready"}, "ARSIZE": {"actual": "S_AXI_arsize"}, "ARVALID": {"actual": "S_AXI_arvalid"}, "AWADDR": {"actual": "S_AXI_awaddr"}, "AWBURST": {"actual": "S_AXI_awburst"}, "AWCACHE": {"actual": "S_AXI_awcache"}, "AWID": {"actual": "S_AXI_awid"}, "AWLEN": {"actual": "S_AXI_awlen"}, "AWLOCK": {"actual": "S_AXI_awlock"}, "AWPROT": {"actual": "S_AXI_awprot"}, "AWQOS": {"actual": "S_AXI_awqos"}, "AWREADY": {"actual": "S_AXI_awready"}, "AWSIZE": {"actual": "S_AXI_awsize"}, "AWVALID": {"actual": "S_AXI_awvalid"}, "BID": {"actual": "S_AXI_bid"}, "BREADY": {"actual": "S_AXI_bready"}, "BRESP": {"actual": "S_AXI_bresp"}, "BVALID": {"actual": "S_AXI_bvalid"}, "RDATA": {"actual": "S_AXI_rdata"}, "RID": {"actual": "S_AXI_rid"}, "RLAST": {"actual": "S_AXI_rlast"}, "RREADY": {"actual": "S_AXI_rready"}, "RRESP": {"actual": "S_AXI_rresp"}, "RVALID": {"actual": "S_AXI_rvalid"}, "WDATA": {"actual": "S_AXI_wdata"}, "WID": {"actual": "S_AXI_wid"}, "WLAST": {"actual": "S_AXI_wlast"}, "WREADY": {"actual": "S_AXI_wready"}, "WSTRB": {"actual": "S_AXI_wstrb"}, "WVALID": {"actual": "S_AXI_wvalid"}}}, "/ps7_0_axi_periph/s00_couplers/auto_pc/M_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "m_axi_araddr"}, "ARESETN": {"actual": "aresetn"}, "ARPROT": {"actual": "m_axi_arprot"}, "ARREADY": {"actual": "m_axi_arready"}, "ARVALID": {"actual": "m_axi_arvalid"}, "AWADDR": {"actual": "m_axi_awaddr"}, "AWPROT": {"actual": "m_axi_awprot"}, "AWREADY": {"actual": "m_axi_awready"}, "AWVALID": {"actual": "m_axi_awvalid"}, "BREADY": {"actual": "m_axi_bready"}, "BRESP": {"actual": "m_axi_bresp"}, "BVALID": {"actual": "m_axi_bvalid"}, "RDATA": {"actual": "m_axi_rdata"}, "RREADY": {"actual": "m_axi_rready"}, "RRESP": {"actual": "m_axi_rresp"}, "RVALID": {"actual": "m_axi_rvalid"}, "WDATA": {"actual": "m_axi_wdata"}, "WREADY": {"actual": "m_axi_wready"}, "WSTRB": {"actual": "m_axi_wstrb"}, "WVALID": {"actual": "m_axi_wvalid"}}}, "/ps7_0_axi_periph/s00_couplers/auto_pc/S_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "s_axi_araddr"}, "ARBURST": {"actual": "s_axi_arburst"}, "ARCACHE": {"actual": "s_axi_arcache"}, "ARESETN": {"actual": "aresetn"}, "ARID": {"actual": "s_axi_arid"}, "ARLEN": {"actual": "s_axi_arlen"}, "ARLOCK": {"actual": "s_axi_arlock"}, "ARPROT": {"actual": "s_axi_arprot"}, "ARQOS": {"actual": "s_axi_arqos"}, "ARREADY": {"actual": "s_axi_arready"}, "ARSIZE": {"actual": "s_axi_arsize"}, "ARVALID": {"actual": "s_axi_arvalid"}, "AWADDR": {"actual": "s_axi_awaddr"}, "AWBURST": {"actual": "s_axi_awburst"}, "AWCACHE": {"actual": "s_axi_awcache"}, "AWID": {"actual": "s_axi_awid"}, "AWLEN": {"actual": "s_axi_awlen"}, "AWLOCK": {"actual": "s_axi_awlock"}, "AWPROT": {"actual": "s_axi_awprot"}, "AWQOS": {"actual": "s_axi_awqos"}, "AWREADY": {"actual": "s_axi_awready"}, "AWSIZE": {"actual": "s_axi_awsize"}, "AWVALID": {"actual": "s_axi_awvalid"}, "BID": {"actual": "s_axi_bid"}, "BREADY": {"actual": "s_axi_bready"}, "BRESP": {"actual": "s_axi_bresp"}, "BVALID": {"actual": "s_axi_bvalid"}, "RDATA": {"actual": "s_axi_rdata"}, "RID": {"actual": "s_axi_rid"}, "RLAST": {"actual": "s_axi_rlast"}, "RREADY": {"actual": "s_axi_rready"}, "RRESP": {"actual": "s_axi_rresp"}, "RVALID": {"actual": "s_axi_rvalid"}, "WDATA": {"actual": "s_axi_wdata"}, "WID": {"actual": "s_axi_wid"}, "WLAST": {"actual": "s_axi_wlast"}, "WREADY": {"actual": "s_axi_wready"}, "WSTRB": {"actual": "s_axi_wstrb"}, "WVALID": {"actual": "s_axi_wvalid"}}}, "/ps7_0_axi_periph/xbar/M00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "m_axi_araddr[31:0]"}, "ARESETN": {"actual": "aresetn"}, "ARPROT": {"actual": "m_axi_arprot[2:0]"}, "ARREADY": {"actual": "m_axi_arready[0:0]"}, "ARVALID": {"actual": "m_axi_arvalid[0:0]"}, "AWADDR": {"actual": "m_axi_awaddr[31:0]"}, "AWPROT": {"actual": "m_axi_awprot[2:0]"}, "AWREADY": {"actual": "m_axi_awready[0:0]"}, "AWVALID": {"actual": "m_axi_awvalid[0:0]"}, "BREADY": {"actual": "m_axi_bready[0:0]"}, "BRESP": {"actual": "m_axi_bresp[1:0]"}, "BVALID": {"actual": "m_axi_bvalid[0:0]"}, "RDATA": {"actual": "m_axi_rdata[31:0]"}, "RREADY": {"actual": "m_axi_rready[0:0]"}, "RRESP": {"actual": "m_axi_rresp[1:0]"}, "RVALID": {"actual": "m_axi_rvalid[0:0]"}, "WDATA": {"actual": "m_axi_wdata[31:0]"}, "WREADY": {"actual": "m_axi_wready[0:0]"}, "WSTRB": {"actual": "m_axi_wstrb[3:0]"}, "WVALID": {"actual": "m_axi_wvalid[0:0]"}}}, "/ps7_0_axi_periph/xbar/M01_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "m_axi_araddr[63:32]"}, "ARESETN": {"actual": "aresetn"}, "ARPROT": {"actual": "m_axi_arprot[5:3]"}, "ARREADY": {"actual": "m_axi_arready[1:1]"}, "ARVALID": {"actual": "m_axi_arvalid[1:1]"}, "AWADDR": {"actual": "m_axi_awaddr[63:32]"}, "AWPROT": {"actual": "m_axi_awprot[5:3]"}, "AWREADY": {"actual": "m_axi_awready[1:1]"}, "AWVALID": {"actual": "m_axi_awvalid[1:1]"}, "BREADY": {"actual": "m_axi_bready[1:1]"}, "BRESP": {"actual": "m_axi_bresp[3:2]"}, "BVALID": {"actual": "m_axi_bvalid[1:1]"}, "RDATA": {"actual": "m_axi_rdata[63:32]"}, "RREADY": {"actual": "m_axi_rready[1:1]"}, "RRESP": {"actual": "m_axi_rresp[3:2]"}, "RVALID": {"actual": "m_axi_rvalid[1:1]"}, "WDATA": {"actual": "m_axi_wdata[63:32]"}, "WREADY": {"actual": "m_axi_wready[1:1]"}, "WSTRB": {"actual": "m_axi_wstrb[7:4]"}, "WVALID": {"actual": "m_axi_wvalid[1:1]"}}}, "/ps7_0_axi_periph/xbar/M02_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "m_axi_araddr[95:64]"}, "ARESETN": {"actual": "aresetn"}, "ARPROT": {"actual": "m_axi_arprot[8:6]"}, "ARREADY": {"actual": "m_axi_arready[2:2]"}, "ARVALID": {"actual": "m_axi_arvalid[2:2]"}, "AWADDR": {"actual": "m_axi_awaddr[95:64]"}, "AWPROT": {"actual": "m_axi_awprot[8:6]"}, "AWREADY": {"actual": "m_axi_awready[2:2]"}, "AWVALID": {"actual": "m_axi_awvalid[2:2]"}, "BREADY": {"actual": "m_axi_bready[2:2]"}, "BRESP": {"actual": "m_axi_bresp[5:4]"}, "BVALID": {"actual": "m_axi_bvalid[2:2]"}, "RDATA": {"actual": "m_axi_rdata[95:64]"}, "RREADY": {"actual": "m_axi_rready[2:2]"}, "RRESP": {"actual": "m_axi_rresp[5:4]"}, "RVALID": {"actual": "m_axi_rvalid[2:2]"}, "WDATA": {"actual": "m_axi_wdata[95:64]"}, "WREADY": {"actual": "m_axi_wready[2:2]"}, "WSTRB": {"actual": "m_axi_wstrb[11:8]"}, "WVALID": {"actual": "m_axi_wvalid[2:2]"}}}, "/ps7_0_axi_periph/xbar/M03_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "m_axi_araddr[127:96]"}, "ARESETN": {"actual": "aresetn"}, "ARPROT": {"actual": "m_axi_arprot[11:9]"}, "ARREADY": {"actual": "m_axi_arready[3:3]"}, "ARVALID": {"actual": "m_axi_arvalid[3:3]"}, "AWADDR": {"actual": "m_axi_awaddr[127:96]"}, "AWPROT": {"actual": "m_axi_awprot[11:9]"}, "AWREADY": {"actual": "m_axi_awready[3:3]"}, "AWVALID": {"actual": "m_axi_awvalid[3:3]"}, "BREADY": {"actual": "m_axi_bready[3:3]"}, "BRESP": {"actual": "m_axi_bresp[7:6]"}, "BVALID": {"actual": "m_axi_bvalid[3:3]"}, "RDATA": {"actual": "m_axi_rdata[127:96]"}, "RREADY": {"actual": "m_axi_rready[3:3]"}, "RRESP": {"actual": "m_axi_rresp[7:6]"}, "RVALID": {"actual": "m_axi_rvalid[3:3]"}, "WDATA": {"actual": "m_axi_wdata[127:96]"}, "WREADY": {"actual": "m_axi_wready[3:3]"}, "WSTRB": {"actual": "m_axi_wstrb[15:12]"}, "WVALID": {"actual": "m_axi_wvalid[3:3]"}}}, "/ps7_0_axi_periph/xbar/M04_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "m_axi_araddr[159:128]"}, "ARESETN": {"actual": "aresetn"}, "ARPROT": {"actual": "m_axi_arprot[14:12]"}, "ARREADY": {"actual": "m_axi_arready[4:4]"}, "ARVALID": {"actual": "m_axi_arvalid[4:4]"}, "AWADDR": {"actual": "m_axi_awaddr[159:128]"}, "AWPROT": {"actual": "m_axi_awprot[14:12]"}, "AWREADY": {"actual": "m_axi_awready[4:4]"}, "AWVALID": {"actual": "m_axi_awvalid[4:4]"}, "BREADY": {"actual": "m_axi_bready[4:4]"}, "BRESP": {"actual": "m_axi_bresp[9:8]"}, "BVALID": {"actual": "m_axi_bvalid[4:4]"}, "RDATA": {"actual": "m_axi_rdata[159:128]"}, "RREADY": {"actual": "m_axi_rready[4:4]"}, "RRESP": {"actual": "m_axi_rresp[9:8]"}, "RVALID": {"actual": "m_axi_rvalid[4:4]"}, "WDATA": {"actual": "m_axi_wdata[159:128]"}, "WREADY": {"actual": "m_axi_wready[4:4]"}, "WSTRB": {"actual": "m_axi_wstrb[19:16]"}, "WVALID": {"actual": "m_axi_wvalid[4:4]"}}}, "/ps7_0_axi_periph/xbar/S00_AXI": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "aclk"}, "ARADDR": {"actual": "s_axi_araddr[31:0]"}, "ARESETN": {"actual": "aresetn"}, "ARPROT": {"actual": "s_axi_arprot[2:0]"}, "ARREADY": {"actual": "s_axi_arready[0:0]"}, "ARVALID": {"actual": "s_axi_arvalid[0:0]"}, "AWADDR": {"actual": "s_axi_awaddr[31:0]"}, "AWPROT": {"actual": "s_axi_awprot[2:0]"}, "AWREADY": {"actual": "s_axi_awready[0:0]"}, "AWVALID": {"actual": "s_axi_awvalid[0:0]"}, "BREADY": {"actual": "s_axi_bready[0:0]"}, "BRESP": {"actual": "s_axi_bresp[1:0]"}, "BVALID": {"actual": "s_axi_bvalid[0:0]"}, "RDATA": {"actual": "s_axi_rdata[31:0]"}, "RREADY": {"actual": "s_axi_rready[0:0]"}, "RRESP": {"actual": "s_axi_rresp[1:0]"}, "RVALID": {"actual": "s_axi_rvalid[0:0]"}, "WDATA": {"actual": "s_axi_wdata[31:0]"}, "WREADY": {"actual": "s_axi_wready[0:0]"}, "WSTRB": {"actual": "s_axi_wstrb[3:0]"}, "WVALID": {"actual": "s_axi_wvalid[0:0]"}}}, "/v_axi4s_vid_out_0/video_in": {"interface": "xilinx.com:interface:axis:1.0", "ports": {"ACLK": {"actual": "aclk"}, "TDATA": {"actual": "s_axis_video_tdata"}, "TLAST": {"actual": "s_axis_video_tlast"}, "TREADY": {"actual": "s_axis_video_tready"}, "TUSER": {"actual": "s_axis_video_tuser"}, "TVALID": {"actual": "s_axis_video_tvalid"}}}, "/v_tc_0/ctrl": {"interface": "xilinx.com:interface:aximm:1.0", "ports": {"ACLK": {"actual": "s_axi_aclk"}, "ARADDR": {"actual": "s_axi_araddr"}, "ARESETN": {"actual": "s_axi_aresetn"}, "ARREADY": {"actual": "s_axi_arready"}, "ARVALID": {"actual": "s_axi_arvalid"}, "AWADDR": {"actual": "s_axi_awaddr"}, "AWREADY": {"actual": "s_axi_awready"}, "AWVALID": {"actual": "s_axi_awvalid"}, "BREADY": {"actual": "s_axi_bready"}, "BRESP": {"actual": "s_axi_bresp"}, "BVALID": {"actual": "s_axi_bvalid"}, "RDATA": {"actual": "s_axi_rdata"}, "RREADY": {"actual": "s_axi_rready"}, "RRESP": {"actual": "s_axi_rresp"}, "RVALID": {"actual": "s_axi_rvalid"}, "WDATA": {"actual": "s_axi_wdata"}, "WREADY": {"actual": "s_axi_wready"}, "WSTRB": {"actual": "s_axi_wstrb"}, "WVALID": {"actual": "s_axi_wvalid"}}}}}}}