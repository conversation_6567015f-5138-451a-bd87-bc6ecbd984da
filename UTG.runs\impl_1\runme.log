
*** Running vivado
    with args -log UGT.vdi -applog -m64 -product Vivado -messageDb vivado.pb -mode batch -source UGT.tcl -notrace


****** Vivado v2018.3 (64-bit)
  **** SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
  **** IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

source UGT.tcl -notrace
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1700] Loaded user IP repository 'f:/UTG/UTG/ip_repo'.
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'C:/Xilinx/Vivado/2018.3/data/ip'.
Command: link_design -top UGT -part xc7z020clg400-2
Design is defaulting to srcset: sources_1
Design is defaulting to constrset: constrs_1
INFO: [Project 1-454] Reading design checkpoint 'f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.dcp' for cell 'clk_gen'
INFO: [Netlist 29-17] Analyzing 698 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7z020clg400-2
INFO: [Project 1-570] Preparing netlist for logic optimization
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc] for cell 'u1/utg_i/processing_system7_0/inst'
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:30]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:31]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:32]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:33]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:34]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:35]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:37]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:38]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:39]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:40]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:41]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:42]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:44]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:45]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:46]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:47]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:48]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:49]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:51]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:52]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:53]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:54]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:55]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:56]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:58]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:59]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:60]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:61]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:62]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:63]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:65]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:66]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:67]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:68]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:69]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:70]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:72]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:73]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:74]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:75]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:76]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:77]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:79]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:80]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:81]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:82]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:83]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:84]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:86]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:87]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:88]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:89]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:90]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:91]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:93]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:94]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:95]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:96]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:97]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:99]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:100]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:101]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:102]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:103]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:105]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:106]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:107]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:108]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:109]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:111]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:112]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:113]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:114]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:115]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:117]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:118]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:119]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:120]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:121]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:123]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:124]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:125]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'drive', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:126]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'pullup', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:127]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:128]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:129]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:130]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:131]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:132]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:133]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:134]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:135]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:136]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:137]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:138]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:139]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PIO_DIRECTION', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:140]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'iostandard', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:141]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'PACKAGE_PIN', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:142]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
CRITICAL WARNING: [Netlist 29-160] Cannot set property 'slew', because the property does not exist for objects of type 'pin'. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:143]
Resolution: Modify the set_property command to apply the property on the correct object type. Since the property is being applied as a scoped constraint, ensure the proper connectivity of the object port objects can be translated into pin objects. This could be due to the insertion of IO Buffers between the top level terminal and cell pin. If the goal is to apply constraints that will migrate to top level ports it is required that IO Buffers manually be instanced.
INFO: [Common 17-14] Message 'Netlist 29-160' appears 100 times and further instances of the messages will be disabled. Use the Tcl command set_msg_config to change the current settings. [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc:143]
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_processing_system7_0_0/utg_processing_system7_0_0.xdc] for cell 'u1/utg_i/processing_system7_0/inst'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_vdma_0_0/utg_axi_vdma_0_0.xdc] for cell 'u1/utg_i/axi_vdma_0/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_vdma_0_0/utg_axi_vdma_0_0.xdc] for cell 'u1/utg_i/axi_vdma_0/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_1/utg_rst_ps7_0_100M_1_board.xdc] for cell 'u1/utg_i/rst_ps7_0_100M/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_1/utg_rst_ps7_0_100M_1_board.xdc] for cell 'u1/utg_i/rst_ps7_0_100M/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_1/utg_rst_ps7_0_100M_1.xdc] for cell 'u1/utg_i/rst_ps7_0_100M/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_1/utg_rst_ps7_0_100M_1.xdc] for cell 'u1/utg_i/rst_ps7_0_100M/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_1/bd_64d3_psr_aclk_0_board.xdc] for cell 'u1/utg_i/axi_smc/inst/clk_map/psr_aclk/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_1/bd_64d3_psr_aclk_0_board.xdc] for cell 'u1/utg_i/axi_smc/inst/clk_map/psr_aclk/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_1/bd_64d3_psr_aclk_0.xdc] for cell 'u1/utg_i/axi_smc/inst/clk_map/psr_aclk/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_2/bd_0/ip/ip_1/bd_64d3_psr_aclk_0.xdc] for cell 'u1/utg_i/axi_smc/inst/clk_map/psr_aclk/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_1/bd_a552_psr_aclk_0_board.xdc] for cell 'u1/utg_i/axi_smc1/inst/clk_map/psr_aclk/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_1/bd_a552_psr_aclk_0_board.xdc] for cell 'u1/utg_i/axi_smc1/inst/clk_map/psr_aclk/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_1/bd_a552_psr_aclk_0.xdc] for cell 'u1/utg_i/axi_smc1/inst/clk_map/psr_aclk/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_smc_0/bd_0/ip/ip_1/bd_a552_psr_aclk_0.xdc] for cell 'u1/utg_i/axi_smc1/inst/clk_map/psr_aclk/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_0/utg_rst_ps7_0_100M_0_board.xdc] for cell 'u1/utg_i/rst_ps7_0_100M1/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_0/utg_rst_ps7_0_100M_0_board.xdc] for cell 'u1/utg_i/rst_ps7_0_100M1/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_0/utg_rst_ps7_0_100M_0.xdc] for cell 'u1/utg_i/rst_ps7_0_100M1/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_rst_ps7_0_100M_0/utg_rst_ps7_0_100M_0.xdc] for cell 'u1/utg_i/rst_ps7_0_100M1/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_board.xdc] for cell 'clk_gen/inst'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_board.xdc] for cell 'clk_gen/inst'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc] for cell 'clk_gen/inst'
INFO: [Timing 38-35] Done setting XDC timing constraints. [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc:57]
INFO: [Timing 38-2] Deriving generated clocks [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc:57]
get_clocks: Time (s): cpu = 00:00:07 ; elapsed = 00:00:07 . Memory (MB): peak = 1420.645 ; gain = 575.219
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc] for cell 'clk_gen/inst'
Parsing XDC File [F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_timing.xdc]
Finished Parsing XDC File [F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_timing.xdc]
Parsing XDC File [F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_pins.xdc]
Finished Parsing XDC File [F:/UTG/UTG/UTG.srcs/constrs_1/new/utg_pins.xdc]
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_vdma_0_0/utg_axi_vdma_0_0_clocks.xdc] for cell 'u1/utg_i/axi_vdma_0/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_axi_vdma_0_0/utg_axi_vdma_0_0_clocks.xdc] for cell 'u1/utg_i/axi_vdma_0/U0'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_axi4s_vid_out_0_2/utg_v_axi4s_vid_out_0_2_clocks.xdc] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_axi4s_vid_out_0_2/utg_v_axi4s_vid_out_0_2_clocks.xdc] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_tc_0_1/utg_v_tc_0_1_clocks.xdc] for cell 'u1/utg_i/v_tc_0/U0'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/bd/utg/ip/utg_v_tc_0_1/utg_v_tc_0_1_clocks.xdc] for cell 'u1/utg_i/v_tc_0/U0'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.wr_pntr_cdc_dc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_gray.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_cdc_pntr.rd_pntr_cdc_dc_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_single.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/CDC_SINGLE_REMAP_UNDERFLOW_INST/xpm_cdc_single_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_single.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/CDC_SINGLE_REMAP_UNDERFLOW_INST/xpm_cdc_single_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_single.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/CDC_SINGLE_LOCKED_INST/xpm_cdc_single_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_single.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/CDC_SINGLE_LOCKED_INST/xpm_cdc_single_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.rrst_wr_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.rrst_wr_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.wrst_rd_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.wrst_rd_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.rrst_wr_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.rrst_wr_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.wrst_rd_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_cdc/tcl/xpm_cdc_sync_rst.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/xpm_fifo_rst_inst/gen_rst_ic.wrst_rd_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_fifo/tcl/xpm_fifo_rst.tcl] for cell 'u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
Finished Sourcing Tcl File [C:/Xilinx/Vivado/2018.3/data/ip/xpm/xpm_memory/tcl/xpm_memory_xdc.tcl] for cell 'u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory'
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.013 . Memory (MB): peak = 1420.645 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 139 instances were transformed.
  IOBUF => IOBUF (IBUF, OBUFT): 5 instances
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 134 instances

14 Infos, 0 Warnings, 100 Critical Warnings and 0 Errors encountered.
link_design completed successfully
link_design: Time (s): cpu = 00:00:15 ; elapsed = 00:00:17 . Memory (MB): peak = 1420.645 ; gain = 1036.516
Command: opt_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z020'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z020'
Running DRC as a precondition to command opt_design

Starting DRC Task
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Project 1-461] DRC finished with 0 Errors
INFO: [Project 1-462] Please refer to the DRC report (report_drc) for more information.

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.513 . Memory (MB): peak = 1420.645 ; gain = 0.000

Starting Cache Timing Information Task
INFO: [Timing 38-35] Done setting XDC timing constraints.
Ending Cache Timing Information Task | Checksum: 1503afb3a

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.627 . Memory (MB): peak = 1420.645 ; gain = 0.000

Starting Logic Optimization Task

Phase 1 Retarget
INFO: [Opt 31-138] Pushed 16 inverter(s) to 92 load pin(s).
INFO: [Opt 31-49] Retargeted 0 cell(s).
Phase 1 Retarget | Checksum: b6491284

Time (s): cpu = 00:00:01 ; elapsed = 00:00:00.933 . Memory (MB): peak = 1467.102 ; gain = 0.895
INFO: [Opt 31-389] Phase Retarget created 313 cells and removed 576 cells
INFO: [Opt 31-1021] In phase Retarget, 255 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 2 Constant propagation
INFO: [Opt 31-138] Pushed 6 inverter(s) to 9 load pin(s).
Phase 2 Constant propagation | Checksum: 16a5be7e4

Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1467.102 ; gain = 0.895
INFO: [Opt 31-389] Phase Constant propagation created 147 cells and removed 1212 cells
INFO: [Opt 31-1021] In phase Constant propagation, 421 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 3 Sweep
Phase 3 Sweep | Checksum: cabdf0ff

Time (s): cpu = 00:00:03 ; elapsed = 00:00:03 . Memory (MB): peak = 1467.102 ; gain = 0.895
INFO: [Opt 31-389] Phase Sweep created 0 cells and removed 2713 cells
INFO: [Opt 31-1021] In phase Sweep, 67 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 

Phase 4 BUFG optimization
INFO: [Opt 31-194] Inserted BUFG clk_gen/inst/clk_out2_clk_wiz_0_BUFG_inst to drive 0 load(s) on clock net clk_gen/inst/clk_out2_clk_wiz_0_BUFG
INFO: [Opt 31-193] Inserted 1 BUFG(s) on clock nets
Phase 4 BUFG optimization | Checksum: 18f159e82

Time (s): cpu = 00:00:03 ; elapsed = 00:00:03 . Memory (MB): peak = 1467.102 ; gain = 0.895
INFO: [Opt 31-662] Phase BUFG optimization created 0 cells of which 0 are BUFGs and removed 0 cells.

Phase 5 Shift Register Optimization
Phase 5 Shift Register Optimization | Checksum: ce7ca5f4

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1467.102 ; gain = 0.895
INFO: [Opt 31-389] Phase Shift Register Optimization created 0 cells and removed 0 cells

Phase 6 Post Processing Netlist
Phase 6 Post Processing Netlist | Checksum: 158c30467

Time (s): cpu = 00:00:05 ; elapsed = 00:00:05 . Memory (MB): peak = 1467.102 ; gain = 0.895
INFO: [Opt 31-389] Phase Post Processing Netlist created 0 cells and removed 0 cells
INFO: [Opt 31-1021] In phase Post Processing Netlist, 28 netlist objects are constrained preventing optimization. Please run opt_design with -debug_log to get more detail. 
Opt_design Change Summary
=========================


-------------------------------------------------------------------------------------------------------------------------
|  Phase                        |  #Cells created  |  #Cells Removed  |  #Constrained objects preventing optimizations  |
-------------------------------------------------------------------------------------------------------------------------
|  Retarget                     |             313  |             576  |                                            255  |
|  Constant propagation         |             147  |            1212  |                                            421  |
|  Sweep                        |               0  |            2713  |                                             67  |
|  BUFG optimization            |               0  |               0  |                                              0  |
|  Shift Register Optimization  |               0  |               0  |                                              0  |
|  Post Processing Netlist      |               0  |               0  |                                             28  |
-------------------------------------------------------------------------------------------------------------------------



Starting Connectivity Check Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.021 . Memory (MB): peak = 1467.102 ; gain = 0.000
Ending Logic Optimization Task | Checksum: eaa7d70d

Time (s): cpu = 00:00:05 ; elapsed = 00:00:05 . Memory (MB): peak = 1467.102 ; gain = 0.895

Starting Power Optimization Task
INFO: [Pwropt 34-132] Skipping clock gating for clocks with a period < 2.00 ns.
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [Pwropt 34-9] Applying IDT optimizations ...
INFO: [Pwropt 34-10] Applying ODC optimizations ...
INFO: [Physopt 32-619] Estimated Timing Summary | WNS=2.501 | TNS=0.000 |
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation


Starting PowerOpt Patch Enables Task
INFO: [Pwropt 34-162] WRITE_MODE attribute of 0 BRAM(s) out of a total of 10 has been updated to save power. Run report_power_opt to get a complete listing of the BRAMs updated.
INFO: [Pwropt 34-201] Structural ODC has moved 0 WE to EN ports
Number of BRAM Ports augmented: 3 newly gated: 0 Total Ports: 20
Ending PowerOpt Patch Enables Task | Checksum: df54b6ac

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.070 . Memory (MB): peak = 1764.469 ; gain = 0.000
Ending Power Optimization Task | Checksum: df54b6ac

Time (s): cpu = 00:00:06 ; elapsed = 00:00:03 . Memory (MB): peak = 1764.469 ; gain = 297.367

Starting Final Cleanup Task
Ending Final Cleanup Task | Checksum: df54b6ac

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 1764.469 ; gain = 0.000

Starting Netlist Obfuscation Task
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.012 . Memory (MB): peak = 1764.469 ; gain = 0.000
Ending Netlist Obfuscation Task | Checksum: 1434c880d

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.012 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
42 Infos, 0 Warnings, 100 Critical Warnings and 0 Errors encountered.
opt_design completed successfully
opt_design: Time (s): cpu = 00:00:14 ; elapsed = 00:00:10 . Memory (MB): peak = 1764.469 ; gain = 343.824
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.029 . Memory (MB): peak = 1764.469 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'F:/UTG/UTG/UTG.runs/impl_1/UGT_opt.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file UGT_drc_opted.rpt -pb UGT_drc_opted.pb -rpx UGT_drc_opted.rpx
Command: report_drc -file UGT_drc_opted.rpt -pb UGT_drc_opted.pb -rpx UGT_drc_opted.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file F:/UTG/UTG/UTG.runs/impl_1/UGT_drc_opted.rpt.
report_drc completed successfully
Command: place_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z020'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z020'
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.
Running DRC as a precondition to command place_design
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors, 12 Warnings
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.

Starting Placer Task
INFO: [Place 30-611] Multithreading enabled for place_design using a maximum of 2 CPUs

Phase 1 Placer Initialization

Phase 1.1 Placer Initialization Netlist Sorting
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1764.469 ; gain = 0.000
Phase 1.1 Placer Initialization Netlist Sorting | Checksum: 7042ff2d

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 1764.469 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device
INFO: [Timing 38-35] Done setting XDC timing constraints.
Phase 1.2 IO Placement/ Clock Placement/ Build Placer Device | Checksum: eafadfaf

Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 1.3 Build Placer Netlist Model
INFO: [Place 30-896] Complex timing constraints detected. Disabling fast timing updates
Phase 1.3 Build Placer Netlist Model | Checksum: 1c42ba7ac

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 1.4 Constrain Clocks/Macros
Phase 1.4 Constrain Clocks/Macros | Checksum: 1c42ba7ac

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1764.469 ; gain = 0.000
Phase 1 Placer Initialization | Checksum: 1c42ba7ac

Time (s): cpu = 00:00:05 ; elapsed = 00:00:04 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 2 Global Placement

Phase 2.1 Floorplanning
Phase 2.1 Floorplanning | Checksum: 23a018f42

Time (s): cpu = 00:00:06 ; elapsed = 00:00:04 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 2.2 Physical Synthesis In Placer
INFO: [Physopt 32-65] No nets found for high-fanout optimization.
INFO: [Physopt 32-232] Optimized 0 net. Created 0 new instance.
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
INFO: [Physopt 32-670] No setup violation found.  DSP Register Optimization was not performed.
INFO: [Physopt 32-670] No setup violation found.  Shift Register Optimization was not performed.
INFO: [Physopt 32-670] No setup violation found.  BRAM Register Optimization was not performed.
INFO: [Physopt 32-949] No candidate nets found for HD net replication
INFO: [Physopt 32-775] End 1 Pass. Optimized 0 net or cell. Created 0 new cell, deleted 0 existing cell and moved 0 existing cell
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1764.469 ; gain = 0.000

Summary of Physical Synthesis Optimizations
============================================


----------------------------------------------------------------------------------------------------------------------------------------
|  Optimization                  |  Added Cells  |  Removed Cells  |  Optimized Cells/Nets  |  Dont Touch  |  Iterations  |  Elapsed   |
----------------------------------------------------------------------------------------------------------------------------------------
|  Very High Fanout              |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  DSP Register                  |            0  |              0  |                     0  |           0  |           0  |  00:00:00  |
|  Shift Register                |            0  |              0  |                     0  |           0  |           0  |  00:00:00  |
|  BRAM Register                 |            0  |              0  |                     0  |           0  |           0  |  00:00:00  |
|  HD Interface Net Replication  |            0  |              0  |                     0  |           0  |           1  |  00:00:00  |
|  Total                         |            0  |              0  |                     0  |           0  |           2  |  00:00:00  |
----------------------------------------------------------------------------------------------------------------------------------------


Phase 2.2 Physical Synthesis In Placer | Checksum: 21bc01ba6

Time (s): cpu = 00:00:19 ; elapsed = 00:00:12 . Memory (MB): peak = 1764.469 ; gain = 0.000
Phase 2 Global Placement | Checksum: 2c4de3b5a

Time (s): cpu = 00:00:19 ; elapsed = 00:00:12 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 3 Detail Placement

Phase 3.1 Commit Multi Column Macros
Phase 3.1 Commit Multi Column Macros | Checksum: 2c4de3b5a

Time (s): cpu = 00:00:20 ; elapsed = 00:00:12 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 3.2 Commit Most Macros & LUTRAMs
Phase 3.2 Commit Most Macros & LUTRAMs | Checksum: 20a6ede61

Time (s): cpu = 00:00:22 ; elapsed = 00:00:14 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 3.3 Area Swap Optimization
Phase 3.3 Area Swap Optimization | Checksum: 2a40fa686

Time (s): cpu = 00:00:22 ; elapsed = 00:00:14 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 3.4 Pipeline Register Optimization
Phase 3.4 Pipeline Register Optimization | Checksum: 27b92363c

Time (s): cpu = 00:00:23 ; elapsed = 00:00:14 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 3.5 Small Shape Detail Placement
Phase 3.5 Small Shape Detail Placement | Checksum: 14e9a6256

Time (s): cpu = 00:00:25 ; elapsed = 00:00:17 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 3.6 Re-assign LUT pins
Phase 3.6 Re-assign LUT pins | Checksum: 15cbda00f

Time (s): cpu = 00:00:25 ; elapsed = 00:00:17 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 3.7 Pipeline Register Optimization
Phase 3.7 Pipeline Register Optimization | Checksum: 1c648b0ee

Time (s): cpu = 00:00:26 ; elapsed = 00:00:17 . Memory (MB): peak = 1764.469 ; gain = 0.000
Phase 3 Detail Placement | Checksum: 1c648b0ee

Time (s): cpu = 00:00:26 ; elapsed = 00:00:17 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 4 Post Placement Optimization and Clean-Up

Phase 4.1 Post Commit Optimization

Phase 4.1.1 Post Placement Optimization
Post Placement Optimization Initialization | Checksum: 1ac233f4e

Phase ******* BUFG Insertion
INFO: [Place 46-33] Processed net u1/utg_i/v_tc_0/U0/U_VIDEO_CTRL/GEN_HAS_IRQ.irq_i_1_n_0, BUFG insertion was skipped due to placement/routing conflicts.
INFO: [Place 46-46] BUFG insertion identified 1 candidate nets, 0 success, 0 bufg driver replicated, 1 skipped for placement/routing, 0 skipped for timing, 0 skipped for netlist change reason
Phase ******* BUFG Insertion | Checksum: 1ac233f4e

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [Place 30-746] Post Placement Timing Summary WNS=3.728. For the most accurate timing information please run report_timing.
Phase 4.1.1 Post Placement Optimization | Checksum: 19a4874d1

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000
Phase 4.1 Post Commit Optimization | Checksum: 19a4874d1

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 4.2 Post Placement Cleanup
Phase 4.2 Post Placement Cleanup | Checksum: 19a4874d1

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 4.3 Placer Reporting
Phase 4.3 Placer Reporting | Checksum: 19a4874d1

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 4.4 Final Placement Cleanup
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.006 . Memory (MB): peak = 1764.469 ; gain = 0.000
Phase 4.4 Final Placement Cleanup | Checksum: 13c6e4fc0

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000
Phase 4 Post Placement Optimization and Clean-Up | Checksum: 13c6e4fc0

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000
Ending Placer Task | Checksum: 119f7af0d

Time (s): cpu = 00:00:28 ; elapsed = 00:00:19 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [Common 17-83] Releasing license: Implementation
70 Infos, 12 Warnings, 100 Critical Warnings and 0 Errors encountered.
place_design completed successfully
place_design: Time (s): cpu = 00:00:30 ; elapsed = 00:00:20 . Memory (MB): peak = 1764.469 ; gain = 0.000
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.005 . Memory (MB): peak = 1764.469 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:02 ; elapsed = 00:00:00.751 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'F:/UTG/UTG/UTG.runs/impl_1/UGT_placed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_io -file UGT_io_placed.rpt
report_io: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.042 . Memory (MB): peak = 1764.469 ; gain = 0.000
INFO: [runtcl-4] Executing : report_utilization -file UGT_utilization_placed.rpt -pb UGT_utilization_placed.pb
INFO: [runtcl-4] Executing : report_control_sets -verbose -file UGT_control_sets_placed.rpt
report_control_sets: Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.043 . Memory (MB): peak = 1764.469 ; gain = 0.000
Command: route_design
Attempting to get a license for feature 'Implementation' and/or device 'xc7z020'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z020'
Running DRC as a precondition to command route_design
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Vivado_Tcl 4-198] DRC finished with 0 Errors
INFO: [Vivado_Tcl 4-199] Please refer to the DRC report (report_drc) for more information.


Starting Routing Task
INFO: [Route 35-254] Multithreading enabled for route_design using a maximum of 2 CPUs
Checksum: PlaceDB: dac55b73 ConstDB: 0 ShapeSum: 3f32539a RouteDB: 0

Phase 1 Build RT Design
Phase 1 Build RT Design | Checksum: 1b95d352a

Time (s): cpu = 00:00:23 ; elapsed = 00:00:20 . Memory (MB): peak = 1764.469 ; gain = 0.000
Post Restoration Checksum: NetGraph: be7a1705 NumContArr: fae31e25 Constraints: 0 Timing: 0

Phase 2 Router Initialization

Phase 2.1 Create Timer
Phase 2.1 Create Timer | Checksum: 1b95d352a

Time (s): cpu = 00:00:24 ; elapsed = 00:00:20 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 2.2 Fix Topology Constraints
Phase 2.2 Fix Topology Constraints | Checksum: 1b95d352a

Time (s): cpu = 00:00:24 ; elapsed = 00:00:21 . Memory (MB): peak = 1764.469 ; gain = 0.000

Phase 2.3 Pre Route Cleanup
Phase 2.3 Pre Route Cleanup | Checksum: 1b95d352a

Time (s): cpu = 00:00:24 ; elapsed = 00:00:21 . Memory (MB): peak = 1764.469 ; gain = 0.000
 Number of Nodes with overlaps = 0

Phase 2.4 Update Timing
Phase 2.4 Update Timing | Checksum: 1e4519ff6

Time (s): cpu = 00:00:28 ; elapsed = 00:00:23 . Memory (MB): peak = 1774.406 ; gain = 9.938
INFO: [Route 35-416] Intermediate Timing Summary | WNS=3.868  | TNS=0.000  | WHS=-0.363 | THS=-443.053|


Phase 2.5 Update Timing for Bus Skew

Phase 2.5.1 Update Timing
Phase 2.5.1 Update Timing | Checksum: 1683a4181

Time (s): cpu = 00:00:32 ; elapsed = 00:00:26 . Memory (MB): peak = 1791.926 ; gain = 27.457
INFO: [Route 35-416] Intermediate Timing Summary | WNS=3.868  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 2.5 Update Timing for Bus Skew | Checksum: 1765a0405

Time (s): cpu = 00:00:32 ; elapsed = 00:00:26 . Memory (MB): peak = 1804.297 ; gain = 39.828
Phase 2 Router Initialization | Checksum: 1154abfa3

Time (s): cpu = 00:00:32 ; elapsed = 00:00:26 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 3 Initial Routing
Phase 3 Initial Routing | Checksum: ab2c5320

Time (s): cpu = 00:00:34 ; elapsed = 00:00:27 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 4 Rip-up And Reroute

Phase 4.1 Global Iteration 0
 Number of Nodes with overlaps = 855
 Number of Nodes with overlaps = 41
 Number of Nodes with overlaps = 0
INFO: [Route 35-416] Intermediate Timing Summary | WNS=3.051  | TNS=0.000  | WHS=N/A    | THS=N/A    |

Phase 4.1 Global Iteration 0 | Checksum: 7b63f9dd

Time (s): cpu = 00:00:39 ; elapsed = 00:00:30 . Memory (MB): peak = 1804.297 ; gain = 39.828
Phase 4 Rip-up And Reroute | Checksum: 7b63f9dd

Time (s): cpu = 00:00:39 ; elapsed = 00:00:30 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 5 Delay and Skew Optimization

Phase 5.1 Delay CleanUp
Phase 5.1 Delay CleanUp | Checksum: 7b63f9dd

Time (s): cpu = 00:00:39 ; elapsed = 00:00:30 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 5.2 Clock Skew Optimization
Phase 5.2 Clock Skew Optimization | Checksum: 7b63f9dd

Time (s): cpu = 00:00:39 ; elapsed = 00:00:30 . Memory (MB): peak = 1804.297 ; gain = 39.828
Phase 5 Delay and Skew Optimization | Checksum: 7b63f9dd

Time (s): cpu = 00:00:39 ; elapsed = 00:00:30 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 6 Post Hold Fix

Phase 6.1 Hold Fix Iter

Phase 6.1.1 Update Timing
Phase 6.1.1 Update Timing | Checksum: 15a2ca911

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828
INFO: [Route 35-416] Intermediate Timing Summary | WNS=2.507  | TNS=0.000  | WHS=0.038  | THS=0.000  |

Phase 6.1 Hold Fix Iter | Checksum: 14b0fe24c

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828
Phase 6 Post Hold Fix | Checksum: 14b0fe24c

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 7 Route finalize

Router Utilization Summary
  Global Vertical Routing Utilization    = 2.33416 %
  Global Horizontal Routing Utilization  = 2.96281 %
  Routable Net Status*
  *Does not include unroutable nets such as driverless and loadless.
  Run report_route_status for detailed report.
  Number of Failed Nets               = 0
  Number of Unrouted Nets             = 0
  Number of Partially Routed Nets     = 0
  Number of Node Overlaps             = 0

Phase 7 Route finalize | Checksum: 147e11e91

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 8 Verifying routed nets

 Verification completed successfully
Phase 8 Verifying routed nets | Checksum: 147e11e91

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 9 Depositing Routes
Phase 9 Depositing Routes | Checksum: 145ceb0ba

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828

Phase 10 Post Router Timing
INFO: [Route 35-57] Estimated Timing Summary | WNS=2.507  | TNS=0.000  | WHS=0.038  | THS=0.000  |

INFO: [Route 35-327] The final timing numbers are based on the router estimated timing analysis. For a complete and accurate timing signoff, please run report_timing_summary.
Phase 10 Post Router Timing | Checksum: 145ceb0ba

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828
INFO: [Route 35-16] Router Completed Successfully

Time (s): cpu = 00:00:41 ; elapsed = 00:00:31 . Memory (MB): peak = 1804.297 ; gain = 39.828

Routing Is Done.
INFO: [Common 17-83] Releasing license: Implementation
88 Infos, 12 Warnings, 100 Critical Warnings and 0 Errors encountered.
route_design completed successfully
route_design: Time (s): cpu = 00:00:43 ; elapsed = 00:00:33 . Memory (MB): peak = 1804.297 ; gain = 39.828
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1804.297 ; gain = 0.000
INFO: [Timing 38-480] Writing timing data to binary archive.
Writing placer database...
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.007 . Memory (MB): peak = 1804.297 ; gain = 0.000
Writing XDEF routing.
Writing XDEF routing logical nets.
Writing XDEF routing special nets.
Write XDEF Complete: Time (s): cpu = 00:00:04 ; elapsed = 00:00:01 . Memory (MB): peak = 1804.297 ; gain = 0.000
INFO: [Common 17-1381] The checkpoint 'F:/UTG/UTG/UTG.runs/impl_1/UGT_routed.dcp' has been generated.
INFO: [runtcl-4] Executing : report_drc -file UGT_drc_routed.rpt -pb UGT_drc_routed.pb -rpx UGT_drc_routed.rpx
Command: report_drc -file UGT_drc_routed.rpt -pb UGT_drc_routed.pb -rpx UGT_drc_routed.rpx
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
INFO: [Coretcl 2-168] The results of DRC are in file F:/UTG/UTG/UTG.runs/impl_1/UGT_drc_routed.rpt.
report_drc completed successfully
INFO: [runtcl-4] Executing : report_methodology -file UGT_methodology_drc_routed.rpt -pb UGT_methodology_drc_routed.pb -rpx UGT_methodology_drc_routed.rpx
Command: report_methodology -file UGT_methodology_drc_routed.rpt -pb UGT_methodology_drc_routed.pb -rpx UGT_methodology_drc_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
INFO: [DRC 23-133] Running Methodology with 2 threads
INFO: [Coretcl 2-1520] The results of Report Methodology are in file F:/UTG/UTG/UTG.runs/impl_1/UGT_methodology_drc_routed.rpt.
report_methodology completed successfully
INFO: [runtcl-4] Executing : report_power -file UGT_power_routed.rpt -pb UGT_power_summary_routed.pb -rpx UGT_power_routed.rpx
Command: report_power -file UGT_power_routed.rpt -pb UGT_power_summary_routed.pb -rpx UGT_power_routed.rpx
INFO: [Timing 38-35] Done setting XDC timing constraints.
Running Vector-less Activity Propagation...

Finished Running Vector-less Activity Propagation
WARNING: [Power 33-332] Found switching activity that implies high-fanout reset nets being asserted for excessive periods of time which may result in inaccurate power analysis.
Resolution: To review and fix problems, please run Power Constraints Advisor in the GUI from Tools > Power Constraints Advisor or run report_power with the -advisory option to generate a text report.
100 Infos, 13 Warnings, 100 Critical Warnings and 0 Errors encountered.
report_power completed successfully
INFO: [runtcl-4] Executing : report_route_status -file UGT_route_status.rpt -pb UGT_route_status.pb
INFO: [runtcl-4] Executing : report_timing_summary -max_paths 10 -file UGT_timing_summary_routed.rpt -pb UGT_timing_summary_routed.pb -rpx UGT_timing_summary_routed.rpx -warn_on_violation 
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -2, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
WARNING: [Timing 38-436] There are set_bus_skew constraint(s) in this design. Please run report_bus_skew to ensure that bus skew requirements are met.
INFO: [runtcl-4] Executing : report_incremental_reuse -file UGT_incremental_reuse_routed.rpt
INFO: [Vivado_Tcl 4-1062] Incremental flow is disabled. No incremental reuse Info to report.
INFO: [runtcl-4] Executing : report_clock_utilization -file UGT_clock_utilization_routed.rpt
INFO: [runtcl-4] Executing : report_bus_skew -warn_on_violation -file UGT_bus_skew_routed.rpt -pb UGT_bus_skew_routed.pb -rpx UGT_bus_skew_routed.rpx
INFO: [Timing 38-91] UpdateTimingParams: Speed grade: -2, Delay Type: min_max.
INFO: [Timing 38-191] Multithreading enabled for timing update using a maximum of 2 CPUs
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/v_axi4s_vid_out_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/axi_vdma_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/axi_vdma_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/AXI_ADC_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
Command: write_bitstream -force UGT.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7z020'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z020'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-1839] IP Catalog is up to date.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
INFO: [DRC REQP-181] writefirst: Synchronous clocking is detected for BRAM (u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg) in SDP mode with WRITE_FIRST write-mode. This is the preferred mode for best power characteristics, however it may exhibit address collisions if the same address appears on both read and write ports resulting in unknown or corrupted read data. It is suggested to confirm via simulation that an address collision never occurs and if so it is suggested to try and avoid this situation. If address collisions cannot be avoided, the write-mode may be set to READ_FIRST which guarantees that the read data is the prior contents of the memory at the cost of additional power in the design. See the FPGA Memory Resources User Guide for additional information.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 12 Warnings, 1 Advisories
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Writing bitstream ./UGT.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-118] WebTalk data collection is enabled (User setting is ON. Install Setting is ON.).
INFO: [Common 17-83] Releasing license: Implementation
131 Infos, 26 Warnings, 100 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:21 ; elapsed = 00:00:17 . Memory (MB): peak = 2369.012 ; gain = 451.887
INFO: [Common 17-206] Exiting Vivado at Mon Aug  4 14:39:16 2025...

*** Running vivado
    with args -log UGT.vdi -applog -m64 -product Vivado -messageDb vivado.pb -mode batch -source UGT.tcl -notrace


****** Vivado v2018.3 (64-bit)
  **** SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
  **** IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

source UGT.tcl -notrace
Command: open_checkpoint UGT_routed.dcp

Starting open_checkpoint Task

Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.025 . Memory (MB): peak = 254.445 ; gain = 0.000
INFO: [Netlist 29-17] Analyzing 569 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-479] Netlist was created with Vivado 2018.3
INFO: [Device 21-403] Loading part xc7z020clg400-2
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Timing 38-478] Restoring timing data from binary archive.
INFO: [Timing 38-479] Binary timing data restore complete.
INFO: [Project 1-856] Restoring constraints from binary archive.
INFO: [Project 1-853] Binary constraint restore complete.
Reading XDEF placement.
Reading placer database...
Reading XDEF routing.
Read XDEF File: Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1354.355 ; gain = 2.770
Restored from archive | CPU: 1.000000 secs | Memory: 0.000000 MB |
Finished XDEF File Restore: Time (s): cpu = 00:00:01 ; elapsed = 00:00:01 . Memory (MB): peak = 1354.355 ; gain = 2.770
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.004 . Memory (MB): peak = 1354.402 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
  A total of 70 instances were transformed.
  IOBUF => IOBUF (IBUF, OBUFT): 5 instances
  RAM32M => RAM32M (RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMD32, RAMS32, RAMS32): 65 instances

INFO: [Project 1-604] Checkpoint was created with Vivado v2018.3 (64-bit) build 2405991
open_checkpoint: Time (s): cpu = 00:00:15 ; elapsed = 00:00:17 . Memory (MB): peak = 1354.402 ; gain = 1099.957
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/v_axi4s_vid_out_0/inst/COUPLER_INST/generate_async_fifo.FIFO_INST/XPM_FIFO_ASYNC_INST/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/v_axi4s_vid_out_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/axi_vdma_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/axi_vdma_0/U0/GEN_SPRT_FOR_MM2S.MM2S_LINEBUFFER_I/GEN_LINEBUF_NO_SOF.GEN_LINEBUFFER.GEN_SYNC_FIFO.I_LINEBUFFER_FIFO/xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/axi_vdma_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_w_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_b_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_aw_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc1/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc/inst/s00_nodes/s00_r_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-167] Found XPM memory block u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst with a P_MEMORY_PRIMITIVE property set to distributed. A value of block is required. You will not be able to use the updatemem program to update the bitstream with new data for the u1/utg_i/axi_smc/inst/s00_nodes/s00_ar_node/inst/inst_mi_handler/gen_normal_area.inst_fifo_node_payld/gen_xpm_memory_fifo.inst_fifo/gen_mem_rep[0].inst_xpm_memory/xpm_memory_base_inst block.
INFO: [Memdata 28-208] The XPM instance: <u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst> is part of IP: <u1/utg_i/AXI_ADC_0>. This XPM instance will be excluded from the .mmi because updatemem is prohibited from making changes to an XPM that is part of an IP.
Command: write_bitstream -force UGT.bit
Attempting to get a license for feature 'Implementation' and/or device 'xc7z020'
INFO: [Common 17-349] Got license for feature 'Implementation' and/or device 'xc7z020'
Running DRC as a precondition to command write_bitstream
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1704] No user IP repositories specified
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'C:/Xilinx/Vivado/2018.3/data/ip'.
INFO: [DRC 23-27] Running DRC with 2 threads
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_0/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_1/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_2/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_3/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_4/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/ENARDEN (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
WARNING: [DRC REQP-1839] RAMB36 async control check: The RAMB36E1 u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5 has an input control pin u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg_5/WEA[0] (net: u1/utg_i/AXI_ADC_0/inst/M_AXIS_S2MM_inst/async_fifo_inst/xpm_fifo_async_inst/gnuram_async_fifo.xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/ena) which is driven by a register (u1/utg_i/AXI_ADC_0/inst/adc_inst/fifo_wr_en_reg) that has an active asychronous set or reset. This may cause corruption of the memory contents and/or read values when the set/reset is asserted and is not analyzed by the default static timing analysis. It is suggested to eliminate the use of a set/reset to registers driving this RAMB pin or else use a synchronous reset in which the assertion of the reset is timed by default.
INFO: [DRC REQP-181] writefirst: Synchronous clocking is detected for BRAM (u1/utg_i/axi_vdma_0/U0/I_PRMRY_DATAMOVER/GEN_MM2S_FULL.I_MM2S_FULL_WRAPPER/GEN_INCLUDE_MM2S_SF.I_RD_SF/I_DATA_FIFO/BLK_MEM.I_SYNC_FIFOGEN_FIFO/xpm_fifo_instance.xpm_fifo_sync_inst/xpm_fifo_base_inst/gen_sdpram.xpm_memory_base_inst/gen_wr_a.gen_word_narrow.mem_reg) in SDP mode with WRITE_FIRST write-mode. This is the preferred mode for best power characteristics, however it may exhibit address collisions if the same address appears on both read and write ports resulting in unknown or corrupted read data. It is suggested to confirm via simulation that an address collision never occurs and if so it is suggested to try and avoid this situation. If address collisions cannot be avoided, the write-mode may be set to READ_FIRST which guarantees that the read data is the prior contents of the memory at the cost of additional power in the design. See the FPGA Memory Resources User Guide for additional information.
INFO: [Vivado 12-3199] DRC finished with 0 Errors, 12 Warnings, 1 Advisories
INFO: [Vivado 12-3200] Please refer to the DRC report (report_drc) for more information.
INFO: [Designutils 20-2272] Running write_bitstream with 2 threads.
Loading data files...
Loading site data...
Loading route data...
Processing options...
Creating bitmap...
Creating bitstream...
Writing bitstream ./UGT.bit...
INFO: [Vivado 12-1842] Bitgen Completed Successfully.
INFO: [Project 1-118] WebTalk data collection is enabled (User setting is ON. Install Setting is ON.).
INFO: [Common 17-83] Releasing license: Implementation
34 Infos, 12 Warnings, 0 Critical Warnings and 0 Errors encountered.
write_bitstream completed successfully
write_bitstream: Time (s): cpu = 00:00:23 ; elapsed = 00:00:22 . Memory (MB): peak = 1887.875 ; gain = 533.473
INFO: [Common 17-206] Exiting Vivado at Mon Aug 11 10:50:29 2025...
