
*** Running vivado
    with args -log clk_wiz_0.vds -m64 -product Vivado -mode batch -messageDb vivado.pb -notrace -source clk_wiz_0.tcl


****** Vivado v2018.3 (64-bit)
  **** SW Build 2405991 on Thu Dec  6 23:38:27 MST 2018
  **** IP Build 2404404 on Fri Dec  7 01:43:56 MST 2018
    ** Copyright 1986-2018 Xilinx, Inc. All Rights Reserved.

source clk_wiz_0.tcl -notrace
INFO: [IP_Flow 19-234] Refreshing IP repositories
INFO: [IP_Flow 19-1700] Loaded user IP repository 'f:/UTG/UTG/ip_repo'.
INFO: [IP_Flow 19-2313] Loaded Vivado IP repository 'C:/Xilinx/Vivado/2018.3/data/ip'.
Command: synth_design -top clk_wiz_0 -part xc7z020clg400-2 -mode out_of_context
Starting synth_design
Attempting to get a license for feature 'Synthesis' and/or device 'xc7z020'
INFO: [Common 17-349] Got license for feature 'Synthesis' and/or device 'xc7z020'
INFO: Launching helper process for spawning children vivado processes
INFO: Helper process launched with PID 10388 
---------------------------------------------------------------------------------
Starting RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:01 . Memory (MB): peak = 472.539 ; gain = 97.395
---------------------------------------------------------------------------------
INFO: [Synth 8-6157] synthesizing module 'clk_wiz_0' [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.v:71]
INFO: [Synth 8-6157] synthesizing module 'clk_wiz_0_clk_wiz' [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_clk_wiz.v:69]
INFO: [Synth 8-6157] synthesizing module 'IBUF' [C:/Xilinx/Vivado/2018.3/scripts/rt/data/unisim_comp.v:20408]
	Parameter CAPACITANCE bound to: DONT_CARE - type: string 
	Parameter IBUF_DELAY_VALUE bound to: 0 - type: string 
	Parameter IBUF_LOW_PWR bound to: TRUE - type: string 
	Parameter IFD_DELAY_VALUE bound to: AUTO - type: string 
	Parameter IOSTANDARD bound to: DEFAULT - type: string 
INFO: [Synth 8-6155] done synthesizing module 'IBUF' (1#1) [C:/Xilinx/Vivado/2018.3/scripts/rt/data/unisim_comp.v:20408]
INFO: [Synth 8-6157] synthesizing module 'PLLE2_ADV' [C:/Xilinx/Vivado/2018.3/scripts/rt/data/unisim_comp.v:41045]
	Parameter BANDWIDTH bound to: OPTIMIZED - type: string 
	Parameter CLKFBOUT_MULT bound to: 30 - type: integer 
	Parameter CLKFBOUT_PHASE bound to: 0.000000 - type: float 
	Parameter CLKIN1_PERIOD bound to: 20.000000 - type: float 
	Parameter CLKIN2_PERIOD bound to: 0.000000 - type: float 
	Parameter CLKOUT0_DIVIDE bound to: 5 - type: integer 
	Parameter CLKOUT0_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT0_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT1_DIVIDE bound to: 12 - type: integer 
	Parameter CLKOUT1_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT1_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT2_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT2_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT2_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT3_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT3_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT3_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT4_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT4_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT4_PHASE bound to: 0.000000 - type: float 
	Parameter CLKOUT5_DIVIDE bound to: 1 - type: integer 
	Parameter CLKOUT5_DUTY_CYCLE bound to: 0.500000 - type: float 
	Parameter CLKOUT5_PHASE bound to: 0.000000 - type: float 
	Parameter COMPENSATION bound to: ZHOLD - type: string 
	Parameter DIVCLK_DIVIDE bound to: 1 - type: integer 
	Parameter IS_CLKINSEL_INVERTED bound to: 1'b0 
	Parameter IS_PWRDWN_INVERTED bound to: 1'b0 
	Parameter IS_RST_INVERTED bound to: 1'b0 
	Parameter REF_JITTER1 bound to: 0.010000 - type: float 
	Parameter REF_JITTER2 bound to: 0.010000 - type: float 
	Parameter STARTUP_WAIT bound to: FALSE - type: string 
INFO: [Synth 8-6155] done synthesizing module 'PLLE2_ADV' (2#1) [C:/Xilinx/Vivado/2018.3/scripts/rt/data/unisim_comp.v:41045]
INFO: [Synth 8-6157] synthesizing module 'BUFG' [C:/Xilinx/Vivado/2018.3/scripts/rt/data/unisim_comp.v:609]
INFO: [Synth 8-6155] done synthesizing module 'BUFG' (3#1) [C:/Xilinx/Vivado/2018.3/scripts/rt/data/unisim_comp.v:609]
INFO: [Synth 8-6155] done synthesizing module 'clk_wiz_0_clk_wiz' (4#1) [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_clk_wiz.v:69]
INFO: [Synth 8-6155] done synthesizing module 'clk_wiz_0' (5#1) [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.v:71]
---------------------------------------------------------------------------------
Finished RTL Elaboration : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 528.719 ; gain = 153.574
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 528.719 ; gain = 153.574
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 1 : Time (s): cpu = 00:00:02 ; elapsed = 00:00:02 . Memory (MB): peak = 528.719 ; gain = 153.574
---------------------------------------------------------------------------------
INFO: [Netlist 29-17] Analyzing 1 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Device 21-403] Loading part xc7z020clg400-2
INFO: [Project 1-570] Preparing netlist for logic optimization

Processing XDC Constraints
Initializing timing engine
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_ooc.xdc] for cell 'inst'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_ooc.xdc] for cell 'inst'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_board.xdc] for cell 'inst'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0_board.xdc] for cell 'inst'
Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc] for cell 'inst'
Finished Parsing XDC File [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc] for cell 'inst'
INFO: [Project 1-236] Implementation specific constraints were found while reading constraint file [f:/UTG/UTG/UTG.srcs/sources_1/ip/clk_wiz_0/clk_wiz_0.xdc]. These constraints will be ignored for synthesis but will be used in implementation. Impacted constraints are listed in the file [.Xil/clk_wiz_0_propImpl.xdc].
Resolution: To avoid this warning, move constraints listed in [.Xil/clk_wiz_0_propImpl.xdc] to another XDC file and exclude this new file from synthesis with the used_in_synthesis property (File Properties dialog in GUI) and re-run elaboration/synthesis.
INFO: [Timing 38-2] Deriving generated clocks
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 796.250 ; gain = 0.000
Parsing XDC File [F:/UTG/UTG/UTG.runs/clk_wiz_0_synth_1/dont_touch.xdc]
Finished Parsing XDC File [F:/UTG/UTG/UTG.runs/clk_wiz_0_synth_1/dont_touch.xdc]
Completed Processing XDC Constraints

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 796.305 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 796.305 ; gain = 0.000
Constraint Validation Runtime : Time (s): cpu = 00:00:00 ; elapsed = 00:00:00.010 . Memory (MB): peak = 796.305 ; gain = 0.000
---------------------------------------------------------------------------------
Finished Constraint Validation : Time (s): cpu = 00:00:07 ; elapsed = 00:00:08 . Memory (MB): peak = 796.305 ; gain = 421.160
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Loading Part and Timing Information
---------------------------------------------------------------------------------
Loading part: xc7z020clg400-2
---------------------------------------------------------------------------------
Finished Loading Part and Timing Information : Time (s): cpu = 00:00:07 ; elapsed = 00:00:08 . Memory (MB): peak = 796.305 ; gain = 421.160
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Applying 'set_property' XDC Constraints
---------------------------------------------------------------------------------
Applied set_property DONT_TOUCH = true for inst. (constraint file  F:/UTG/UTG/UTG.runs/clk_wiz_0_synth_1/dont_touch.xdc, line 9).
---------------------------------------------------------------------------------
Finished applying 'set_property' XDC Constraints : Time (s): cpu = 00:00:07 ; elapsed = 00:00:08 . Memory (MB): peak = 796.305 ; gain = 421.160
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished RTL Optimization Phase 2 : Time (s): cpu = 00:00:07 ; elapsed = 00:00:08 . Memory (MB): peak = 796.305 ; gain = 421.160
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start RTL Component Statistics 
---------------------------------------------------------------------------------
Detailed RTL Component Info : 
---------------------------------------------------------------------------------
Finished RTL Component Statistics 
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start RTL Hierarchical Component Statistics 
---------------------------------------------------------------------------------
Hierarchical RTL Component report 
---------------------------------------------------------------------------------
Finished RTL Hierarchical Component Statistics
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Part Resource Summary
---------------------------------------------------------------------------------
Part Resources:
DSPs: 220 (col length:60)
BRAMs: 280 (col length: RAMB18 60 RAMB36 30)
---------------------------------------------------------------------------------
Finished Part Resource Summary
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Cross Boundary and Area Optimization
---------------------------------------------------------------------------------
Warning: Parallel synthesis criteria is not met 
---------------------------------------------------------------------------------
Finished Cross Boundary and Area Optimization : Time (s): cpu = 00:00:08 ; elapsed = 00:00:08 . Memory (MB): peak = 796.305 ; gain = 421.160
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Applying XDC Timing Constraints
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Applying XDC Timing Constraints : Time (s): cpu = 00:00:13 ; elapsed = 00:00:13 . Memory (MB): peak = 852.730 ; gain = 477.586
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Timing Optimization
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Timing Optimization : Time (s): cpu = 00:00:13 ; elapsed = 00:00:13 . Memory (MB): peak = 852.730 ; gain = 477.586
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Technology Mapping
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Technology Mapping : Time (s): cpu = 00:00:13 ; elapsed = 00:00:13 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Flattening Before IO Insertion
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Final Netlist Cleanup
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished IO Insertion : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------

Report Check Netlist: 
+------+------------------+-------+---------+-------+------------------+
|      |Item              |Errors |Warnings |Status |Description       |
+------+------------------+-------+---------+-------+------------------+
|1     |multi_driven_nets |      0|        0|Passed |Multi driven nets |
+------+------------------+-------+---------+-------+------------------+
---------------------------------------------------------------------------------
Start Renaming Generated Instances
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Instances : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------

Report RTL Partitions: 
+-+--------------+------------+----------+
| |RTL Partition |Replication |Instances |
+-+--------------+------------+----------+
+-+--------------+------------+----------+
---------------------------------------------------------------------------------
Start Rebuilding User Hierarchy
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Rebuilding User Hierarchy : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Ports
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Ports : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Handling Custom Attributes
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Handling Custom Attributes : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Renaming Generated Nets
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Finished Renaming Generated Nets : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------
---------------------------------------------------------------------------------
Start Writing Synthesis Report
---------------------------------------------------------------------------------

Report BlackBoxes: 
+-+--------------+----------+
| |BlackBox name |Instances |
+-+--------------+----------+
+-+--------------+----------+

Report Cell Usage: 
+------+----------+------+
|      |Cell      |Count |
+------+----------+------+
|1     |BUFG      |     3|
|2     |PLLE2_ADV |     1|
|3     |IBUF      |     1|
+------+----------+------+

Report Instance Areas: 
+------+---------+------------------+------+
|      |Instance |Module            |Cells |
+------+---------+------------------+------+
|1     |top      |                  |     5|
|2     |  inst   |clk_wiz_0_clk_wiz |     5|
+------+---------+------------------+------+
---------------------------------------------------------------------------------
Finished Writing Synthesis Report : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
---------------------------------------------------------------------------------
Synthesis finished with 0 errors, 0 critical warnings and 0 warnings.
Synthesis Optimization Runtime : Time (s): cpu = 00:00:07 ; elapsed = 00:00:09 . Memory (MB): peak = 862.293 ; gain = 219.562
Synthesis Optimization Complete : Time (s): cpu = 00:00:13 ; elapsed = 00:00:14 . Memory (MB): peak = 862.293 ; gain = 487.148
INFO: [Project 1-571] Translating synthesized netlist
INFO: [Netlist 29-17] Analyzing 1 Unisim elements for replacement
INFO: [Netlist 29-28] Unisim Transformation completed in 0 CPU seconds
INFO: [Project 1-570] Preparing netlist for logic optimization
INFO: [Opt 31-138] Pushed 0 inverter(s) to 0 load pin(s).
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 877.402 ; gain = 0.000
INFO: [Project 1-111] Unisim Transformation Summary:
No Unisim elements were transformed.

INFO: [Common 17-83] Releasing license: Synthesis
28 Infos, 0 Warnings, 0 Critical Warnings and 0 Errors encountered.
synth_design completed successfully
synth_design: Time (s): cpu = 00:00:14 ; elapsed = 00:00:15 . Memory (MB): peak = 877.426 ; gain = 513.773
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 877.426 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'F:/UTG/UTG/UTG.runs/clk_wiz_0_synth_1/clk_wiz_0.dcp' has been generated.
INFO: [Coretcl 2-1648] Added synthesis output to IP cache for IP clk_wiz_0, cache-ID = 8da6a0373991d9b8
Netlist sorting complete. Time (s): cpu = 00:00:00 ; elapsed = 00:00:00 . Memory (MB): peak = 881.816 ; gain = 0.000
WARNING: [Constraints 18-5210] No constraints selected for write.
Resolution: This message can indicate that there are no constraints for the design, or it can indicate that the used_in flags are set such that the constraints are ignored. This later case is used when running synth_design to not write synthesis constraints to the resulting checkpoint. Instead, project constraints are read when the synthesized design is opened.
INFO: [Common 17-1381] The checkpoint 'F:/UTG/UTG/UTG.runs/clk_wiz_0_synth_1/clk_wiz_0.dcp' has been generated.
INFO: [runtcl-4] Executing : report_utilization -file clk_wiz_0_utilization_synth.rpt -pb clk_wiz_0_utilization_synth.pb
INFO: [Common 17-206] Exiting Vivado at Fri Aug  1 17:04:55 2025...
